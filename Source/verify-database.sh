#!/bin/bash

# 資料庫驗證腳本

echo "🔍 開始驗證 PostgreSQL 資料庫..."

# 檢查 PostgreSQL 容器是否運行
if ! docker ps | grep -q paas-postgres; then
    echo "❌ PostgreSQL 容器未運行，請先執行: docker-compose up -d"
    exit 1
fi

echo "✅ PostgreSQL 容器正在運行"

# 等待資料庫準備就緒
echo "⏳ 等待資料庫準備就緒..."
sleep 3

# 檢查資料庫連線
echo "🔗 測試資料庫連線..."
if docker exec paas-postgres pg_isready -U paasuser -d paasdb > /dev/null 2>&1; then
    echo "✅ 資料庫連線正常"
else
    echo "❌ 資料庫連線失敗"
    exit 1
fi

# 檢查表格數量
echo "📊 檢查資料表..."
TABLE_COUNT=$(docker exec paas-postgres psql -U paasuser -d paasdb -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')

if [ "$TABLE_COUNT" -eq 20 ]; then
    echo "✅ 資料表數量正確: $TABLE_COUNT 個表格"
else
    echo "❌ 資料表數量不正確: 預期 20 個，實際 $TABLE_COUNT 個"
fi

# 列出所有表格
echo "📋 資料表清單:"
docker exec paas-postgres psql -U paasuser -d paasdb -c "\dt" | grep "public |"

# 檢查初始數據
echo ""
echo "📦 檢查初始數據..."

# 檢查幣別數據
CURRENCY_COUNT=$(docker exec paas-postgres psql -U paasuser -d paasdb -t -c "SELECT COUNT(*) FROM currencies;" | tr -d ' ')
echo "💰 幣別數據: $CURRENCY_COUNT 筆 (預期: 4 筆)"

# 檢查角色數據
ROLE_COUNT=$(docker exec paas-postgres psql -U paasuser -d paasdb -t -c "SELECT COUNT(*) FROM roles;" | tr -d ' ')
echo "👥 角色數據: $ROLE_COUNT 筆 (預期: 4 筆)"

# 檢查權限數據
PERMISSION_COUNT=$(docker exec paas-postgres psql -U paasuser -d paasdb -t -c "SELECT COUNT(*) FROM permissions;" | tr -d ' ')
echo "🔐 權限數據: $PERMISSION_COUNT 筆 (預期: 29 筆)"

# 檢查付款方式數據
PAYMENT_COUNT=$(docker exec paas-postgres psql -U paasuser -d paasdb -t -c "SELECT COUNT(*) FROM payment_methods;" | tr -d ' ')
echo "💳 付款方式: $PAYMENT_COUNT 筆 (預期: 4 筆)"

# 檢查通知類型數據
NOTIFICATION_TYPE_COUNT=$(docker exec paas-postgres psql -U paasuser -d paasdb -t -c "SELECT COUNT(*) FROM notification_types;" | tr -d ' ')
echo "📢 通知類型: $NOTIFICATION_TYPE_COUNT 筆 (預期: 4 筆)"

# 檢查安全事件類型數據
SECURITY_EVENT_COUNT=$(docker exec paas-postgres psql -U paasuser -d paasdb -t -c "SELECT COUNT(*) FROM security_event_types;" | tr -d ' ')
echo "🔒 安全事件類型: $SECURITY_EVENT_COUNT 筆 (預期: 6 筆)"

# 檢查外鍵約束
echo ""
echo "🔗 檢查外鍵約束..."
FK_COUNT=$(docker exec paas-postgres psql -U paasuser -d paasdb -t -c "SELECT COUNT(*) FROM information_schema.table_constraints WHERE constraint_type = 'FOREIGN KEY';" | tr -d ' ')
echo "🔑 外鍵約束: $FK_COUNT 個"

# 檢查索引
echo ""
echo "📇 檢查索引..."
INDEX_COUNT=$(docker exec paas-postgres psql -U paasuser -d paasdb -t -c "SELECT COUNT(*) FROM pg_indexes WHERE schemaname = 'public';" | tr -d ' ')
echo "📊 索引數量: $INDEX_COUNT 個"

# 檢查 API 健康狀態
echo ""
echo "🏥 檢查 API 健康狀態..."
if curl -s http://localhost:8080/v1/health | grep -q '"status":"healthy"'; then
    echo "✅ API 健康檢查通過"
else
    echo "❌ API 健康檢查失敗"
fi

echo ""
echo "🎉 資料庫驗證完成！"
echo ""
echo "📚 可用的測試指令:"
echo "• 查看所有表格: docker exec -it paas-postgres psql -U paasuser -d paasdb -c '\dt'"
echo "• 查看幣別數據: docker exec -it paas-postgres psql -U paasuser -d paasdb -c 'SELECT * FROM currencies;'"
echo "• 查看角色數據: docker exec -it paas-postgres psql -U paasuser -d paasdb -c 'SELECT * FROM roles;'"
echo "• 進入資料庫: docker exec -it paas-postgres psql -U paasuser -d paasdb"
