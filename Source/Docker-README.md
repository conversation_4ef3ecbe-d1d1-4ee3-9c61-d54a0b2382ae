# Paas API Docker 部署指南

本指南說明如何將 Paas.sln 解決方案打包成 Docker Image 並執行。

## 📋 前置需求

- Docker Desktop 或 Docker Engine
- Docker Compose (通常包含在 Docker Desktop 中)

## 🚀 快速開始

### 方法 1: 使用建置腳本

**Linux/macOS:**
```bash
./build-docker.sh
```

**Windows:**
```cmd
build-docker.bat
```

### 方法 2: 手動建置

```bash
# 建置 Docker Image
docker build -t paas-api:latest .

# 執行容器
docker run -d -p 8080:8080 --name paas-api-container paas-api:latest
```

### 方法 3: 使用 Docker Compose (推薦)

**生產環境:**
```bash
# 啟動所有服務 (包含 PostgreSQL 資料庫)
docker-compose up -d

# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs -f paas-api

# 停止所有服務
docker-compose down
```

**開發環境:**
```bash
# 使用開發環境配置 (預設啟用 Swagger)
docker-compose -f docker-compose.dev.yml up -d

# 查看開發環境日誌
docker-compose -f docker-compose.dev.yml logs -f paas-api

# 停止開發環境服務
docker-compose -f docker-compose.dev.yml down
```

## 🔧 配置說明

### Dockerfile 特點

- **多階段建置**: 使用 .NET 9.0 SDK 建置，.NET 9.0 Runtime 執行
- **安全性**: 使用非 root 使用者執行
- **健康檢查**: 內建健康檢查端點
- **最佳化**: 僅包含執行時所需檔案

### Swagger UI 控制

您可以透過 `EnableSwagger` 環境變數來控制 Swagger UI 的顯示：

- **開發環境**: 預設啟用 Swagger UI
- **生產環境**: 預設停用 Swagger UI
- **手動控制**: 設定 `EnableSwagger=true` 啟用，`EnableSwagger=false` 停用

```bash
# 啟用 Swagger UI
docker run -e EnableSwagger=true -p 8080:8080 paas-api:latest

# 停用 Swagger UI
docker run -e EnableSwagger=false -p 8080:8080 paas-api:latest
```

**注意**: 在生產環境中建議停用 Swagger UI 以提高安全性。

### 環境變數

在 `docker-compose.yml` 中可以設定以下環境變數：

```yaml
environment:
  - ASPNETCORE_ENVIRONMENT=Production
  - ASPNETCORE_URLS=http://+:8080
  - EnableSwagger=true  # 控制 Swagger UI 顯示 (true=啟用, false=停用)
  - ConnectionStrings__PostgreConnection=Host=postgres;Database=paasdb;Username=paasuser;Password=paaspass
  - JwtSettings__SecretKey=your-super-secret-jwt-key-here-must-be-at-least-32-characters
  - JwtSettings__Issuer=PaasAPI
  - JwtSettings__Audience=PaasClient
```

## 🌐 存取端點

- **API 基礎 URL**: http://localhost:8080
- **健康檢查**: http://localhost:8080/v1/health
- **Swagger UI**: http://localhost:8080/swagger (可透過 EnableSwagger 環境變數控制)

## 📊 常用 Docker 指令

```bash
# 查看執行中的容器
docker ps

# 查看容器日誌
docker logs paas-api-container

# 進入容器內部
docker exec -it paas-api-container /bin/bash

# 停止容器
docker stop paas-api-container

# 移除容器
docker rm paas-api-container

# 移除 Image
docker rmi paas-api:latest

# 查看 Image 大小
docker images paas-api:latest
```

## 🔍 疑難排解

### 常見問題

1. **建置失敗**
   - 確認所有專案檔案都存在
   - 檢查 .NET 9.0 SDK 是否正確安裝在 Docker Image 中

2. **容器啟動失敗**
   - 檢查連接埠 8080 是否被佔用
   - 查看容器日誌: `docker logs paas-api-container`

3. **資料庫連線問題**
   - 確認 PostgreSQL 容器正在執行
   - 檢查連線字串設定

### 日誌查看

```bash
# 查看即時日誌
docker-compose logs -f paas-api

# 查看最近 100 行日誌
docker logs --tail 100 paas-api-container
```

## 🔒 安全性注意事項

1. **JWT 密鑰**: 請更改預設的 JWT 密鑰
2. **資料庫密碼**: 請使用強密碼
3. **環境變數**: 敏感資訊應使用 Docker Secrets 或環境檔案

## 📦 生產環境部署

對於生產環境，建議：

1. 使用外部資料庫服務
2. 設定適當的資源限制
3. 使用 HTTPS
4. 設定日誌收集
5. 使用容器編排工具 (如 Kubernetes)

## 🔄 更新應用程式

```bash
# 重新建置 Image
docker-compose build paas-api

# 重新啟動服務
docker-compose up -d paas-api
```
