# 使用 .NET 9.0 SDK 作為建置階段
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# 複製所有專案檔案
COPY Common/Paas.Common.csproj Common/
COPY Model/Paas.Model.csproj Model/
COPY Repository/Paas.Dac.csproj Repository/
COPY Service/Paas.Service.csproj Service/
COPY Paas/Paas.csproj Paas/
COPY Paas.Tests/Paas.Tests.csproj Paas.Tests/

# 複製解決方案檔案
COPY Paas/Paas.sln Paas/

# 還原 NuGet 套件 (從 Paas 目錄執行)
WORKDIR /src/Paas
RUN dotnet restore

# 複製所有原始碼
WORKDIR /src
COPY . .

# 確保使用正確的 appsettings 檔案
COPY Paas/appsettings.json /src/Paas/appsettings.json
COPY Paas/appsettings.Production.json /src/Paas/appsettings.Production.json

# 建置應用程式 (只建置主要專案，排除測試)
WORKDIR /src/Paas
RUN dotnet build Paas.csproj -c Release -o /app/build

# 發佈應用程式
RUN dotnet publish Paas.csproj -c Release -o /app/publish /p:UseAppHost=false

# 使用 .NET 9.0 Runtime 作為執行階段
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app

# 建立非 root 使用者和必要目錄
RUN adduser --disabled-password --gecos '' appuser && \
    mkdir -p /app/log && \
    mkdir -p /app/dataprotection-keys && \
    chown -R appuser:appuser /app && \
    chmod 755 /app/log && \
    chmod 755 /app/dataprotection-keys
USER appuser

# 複製發佈的檔案
COPY --from=build /app/publish .

# 設定環境變數
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

# 暴露連接埠
EXPOSE 8080

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/v1/health || exit 1

# 啟動應用程式
ENTRYPOINT ["dotnet", "Paas.dll"]
