using System;
using BCrypt.Net;

class Program
{
    static void Main()
    {
        string password = "gssadmin";
        string hash = "$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXIG.JjixyvW";
        
        Console.WriteLine($"Password: {password}");
        Console.WriteLine($"Hash: {hash}");
        
        bool isValid = BCrypt.Net.BCrypt.Verify(password, hash);
        Console.WriteLine($"Is valid: {isValid}");
        
        // 生成新的雜湊
        string newHash = BCrypt.Net.BCrypt.HashPassword(password, 12);
        Console.WriteLine($"New hash: {newHash}");
        
        bool isNewValid = BCrypt.Net.BCrypt.Verify(password, newHash);
        Console.WriteLine($"New hash is valid: {isNewValid}");
    }
}
