using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Collections.Specialized.BitVector32;

namespace Paas.Model
{
    public class Permissions : BaseEntity
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public Permissions()
        {
            resource = string.Empty;
            action = string.Empty;
        }

        /// <summary>
        /// 權限唯一識別碼
        /// </summary>
        public Guid permission_id { get; set; }

        /// <summary>
        /// 角色識別碼
        /// </summary>
        public Guid role_id{ get; set; }

        /// <summary>
        /// 資源名稱
        /// </summary>
        public string resource{ get; set; }

        /// <summary>
        /// 操作類型
        /// </summary>
        public string action { get; set; }
    }
}
