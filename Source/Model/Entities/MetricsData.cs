using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    public class MetricsData : BaseEntity
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public MetricsData()
        {
            tags = string.Empty;
        }

        /// <summary>
        /// ID
        /// </summary>
        public Guid data_id { get; set; }

        /// <summary>
        /// 指標類型識別碼
        /// </summary>
        public Guid metric_type_id { get; set; }

        /// <summary>
        /// 租戶識別碼
        /// </summary>
        public Guid tenant_id { get; set; }

        /// <summary>
        /// API識別碼
        /// </summary>
        public Guid api_id { get; set; }

        /// <summary>
        /// 指標值
        /// </summary>
        public decimal value { get; set; }

        /// <summary>
        /// JSON格式的標籤資訊
        /// </summary>
        public string tags { get; set; }

        /// <summary>
        /// 資料時間戳記
        /// </summary>
        public DateTimeOffset timestamp { get; set; }
    }
}
