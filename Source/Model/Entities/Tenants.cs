using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// Tenants Entity
    /// </summary>
    public class Tenants : BaseEntity
    {
        /// <summary>
        /// 自動生成的UUID
        /// </summary>
        public Guid? tenant_id { get; set; }

        /// <summary>
        /// 主要聯絡信箱
        /// </summary>
        public required string contact_email { get; set; }

        /// <summary>
        /// 租戶法定名稱
        /// </summary>
        public required string name { get; set; }

        /// <summary>
        /// 帳號啟用狀態
        /// </summary>
        public bool? is_active { get; set; }

        /// <summary>
        /// 計價幣別
        /// </summary>
        public Guid billing_currency { get; set; }

        /// <summary>
        /// 法律認證地址
        /// </summary>
        public required string legal_address { get; set; }

    }
}
