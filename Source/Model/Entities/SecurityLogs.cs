using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    public class SecurityLogs : BaseEntity
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public SecurityLogs()
        {
            ip_address = IPAddress.Any;
        }

        /// <summary>
        /// 日誌唯一識別碼
        /// </summary>
        public Guid log_id { get; set; }
        /// <summary>
        /// 租戶識別碼
        /// </summary>
        public Guid? tenant_id { get; set; }
        /// <summary>
        /// 使用者識別碼
        /// </summary>
        public Guid? user_id { get; set; }
        /// <summary>
        /// 事件類型識別碼
        /// </summary>
        public Guid? event_type_id { get; set; }
        /// <summary>
        /// 事件來源IP地址
        /// </summary>
        public IPAddress ip_address { get; set; }
        /// <summary>
        /// 事件來源用戶代理
        /// </summary>
        public string? user_agent { get; set; }
        /// <summary>
        /// JSON格式的事件詳細資訊
        /// </summary>
        public string? details { get; set; }
        /// <summary>
        /// 開始時間
        /// </summary>
        public DateTimeOffset start_time { get; set; }
        /// <summary>
        /// 結束時間
        /// </summary>
        public DateTimeOffset end_time { get; set; }
        /// <summary>
        /// 回傳最多筆數設定
        /// </summary>
        public int Limit { get; set; } = 10;
        /// <summary>
        /// 跳過幾筆
        /// </summary>
        public int Offset { get; set; } = 0;
    }
}
