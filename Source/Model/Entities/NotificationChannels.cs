using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    public class NotificationChannels : BaseEntity
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public NotificationChannels()
        {
            type = string.Empty;
        }

        /// <summary>
        /// 通知管道唯一識別碼
        /// </summary>
        public Guid channel_id { get; set; }
        /// <summary>
        /// 管道類型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 管道設定
        /// </summary>
        public string? config { get; set; }
        /// <summary>
        /// 管道啟用狀態
        /// </summary>
        public bool? is_active { get; set; }
        /// <summary>
        /// 回傳最多筆數設定
        /// </summary>
        public int Limit { get; set; } = 10;
        /// <summary>
        /// 跳過幾筆
        /// </summary>
        public int Offset { get; set; } = 0;
    }
}
