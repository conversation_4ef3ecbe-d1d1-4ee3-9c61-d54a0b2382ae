using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// API 金鑰管理實體類別
    /// </summary>
    /// <remarks>
    /// 此實體類別對應資料庫中的 ApiKeys 表，用於存儲 API 金鑰的完整資訊。
    /// 繼承自 BaseEntity，因此包含所有實體的基本屬性，如時間戳記、ID 等。
    /// API 金鑰是用戶或應用程序存取 API 服務的主要驗證方式。
    /// 金鑰包含多種屬性，如其若有關聯的租戶、安全權限、過期設定等，以確保系統的安全性和可管理性。
    /// </remarks>
    public class ApiKeys:BaseEntity
    {
        /// <summary>
        /// API 金鑰唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為 API 金鑰的主鍵。
        /// 此值在創建 API 金鑰時自動指定，不得重複，並在金鑰的整個生命週期內保持不變。
        /// 在資料庫中此欄位設置為主鍵，並建立索引以加速查詢。
        /// 此 ID 用於金鑰管理、API 使用記錄和金鑰安全審計。
        /// </remarks>
        public Guid key_id { get; set; }
        /// <summary>
        /// 所屬租戶識別碼
        /// </summary>
        /// <remarks>
        /// 此 API 金鑰所屬的租戶識別碼，作為外鍵指向 Tenants 表的 tenant_id。
        /// 用於定義 API 金鑰與租戶之間的關聯，確保金鑰屬於特定租戶。
        /// 設計為可空值（nullable）以處理特殊機制，如系統管理金鑰可能不屬於特定租戶。
        /// 此外鍵關聯全對系統的多租戶階層和資源隔離至關重要。
        /// </remarks>
        public Guid? tenant_id { get; set; }
        /// <summary>
        /// API 金鑰值，須確保系統唯一性
        /// </summary>
        /// <remarks>
        /// API 金鑰的實際值，即客戶端用於驗證與 API 進行通訊的字串。
        /// 通常為一個難以猜測的隨機生成字串，如 UUID 或加密隨機字串。
        /// 必須在資料庫中保持全系統唯一，以避免識別衝突和安全問題。
        /// 通常使用唯一索引確保其唯一性。在寫入多個資料庫節點的系統中，必須確保金鑰的全局唯一性。
        /// </remarks>
        public required string api_key { get; set; }
        /// <summary>
        /// 金鑰狀態，僅允許 'active' 或 'revoked'
        /// </summary>
        /// <remarks>
        /// 指示 API 金鑰的目前狀態，控制金鑰是否可用。
        /// 支援的值包括：
        /// - 'active'：金鑰已啟用且可使用
        /// - 'revoked'：金鑰已撤銷且不再有效
        /// 系統應有屬性驗證機制確保狀態值符合這些限制。
        /// 當金鑰被撤銷時，系統應拒絕使用該金鑰的所有請求並回傳 401 Unauthorized 狀態碼。
        /// </remarks>
        public required string status { get; set; }
        /// <summary>
        /// API 存取範圍與權限設定(JSONB)
        /// </summary>
        /// <remarks>
        /// 定義此 API 金鑰的訪問權限和範圍，以 JSON 格式存儲。
        /// 包含金鑰可以存取的端點、可執行的操作和功能限制等信息。
        /// 範例格式：{"endpoints": ["users/*", "apis/read"], "methods": ["GET", "POST"]}
        /// 此欄位是可空的，若為 null 可能表示金鑰有完整權限或系統預設權限。
        /// 在資料庫中存儲為 JSON 或 JSONB 型別，允許彈性的權限設定。
        /// </remarks>
        public string? scopes { get; set; }
        /// <summary>
        /// 金鑰過期時間，null 表示永不過期
        /// </summary>
        /// <remarks>
        /// 指定 API 金鑰的過期時間，超過此時間後金鑰自動無效。
        /// 使用 DateTimeOffset 以包含時區信息，確保跨時區的正確性。
        /// 設計為可空值（nullable），若為 null 則表示此金鑰永不過期。
        /// 系統應定期檢查過期的金鑰並自動更新其狀態或給出適當的提示。
        /// 建議為所有生產環境的金鑰設置過期時間，以增強安全性。
        /// </remarks>
        public DateTimeOffset? expiration_at { get; set; }
        /// <summary>
        /// 金鑰生成時間
        /// </summary>
        /// <remarks>
        /// 記錄 API 金鑰的初始創建時間，用於審計和追蹤目的。
        /// 使用 DateTimeOffset 型別以包含時區信息，確保跨時區的正確性。
        /// 此欄位在金鑰創建時自動設置，且在金鑰的整個生命週期內不應被修改。
        /// 可用於分析金鑰的年齡和使用模式，如識別長期未使用的金鑰進行清理。
        /// </remarks>
        public DateTimeOffset generated_at { get; set; }
        /// <summary>
        /// 最後使用時間
        /// </summary>
        /// <remarks>
        /// 記錄 API 金鑰最後一次成功用於驗證 API 請求的時間。
        /// 設計為可空值（nullable），若為 null 則表示金鑰尚未被使用過。
        /// 使用 DateTimeOffset 型別以包含時區信息，確保跨時區的正確性。
        /// 此欄位在每次金鑰成功驗證時應被更新，用於追蹤金鑰使用模式和識別非活躍金鑰。
        /// 可用於定期清理未使用的金鑰，如「超過 90 天未使用」的金鑰自動撤銷等安全機制。
        /// </remarks>
        public DateTimeOffset? last_used_at { get; set; }
        /// <summary>
        /// 查詢結果回傳的最大筆數限制
        /// </summary>
        /// <remarks>
        /// 此屬性用於限制每次查詢中返回的結果數量，為查詢分頁參數的一部分。
        /// 預設值為 10，表示若不指定則每頁僅返回 10 筆資料。
        /// 此屬性在查詢 API 金鑰清單時使用，有助於實現分頁功能和控制查詢的資源消耗。
        /// </remarks>
        public int Limit { get; set; } = 10;
        
        /// <summary>
        /// 查詢資料的跳過筆數
        /// </summary>
        /// <remarks>
        /// 此屬性指定查詢時要跳過的資料筆數，為查詢分頁參數的另一部分。
        /// 預設值為 0，表示從第一筆資料開始查詢。
        /// 用於實現分頁功能，例如將 Offset 設為 10 代表第二頁（假設 Limit 為 10）。
        /// 結合 Limit 和 Offset 的使用可實現完整的分頁導覽功能。
        /// </remarks>
        public int Offset { get; set; } = 0;
    }
}
