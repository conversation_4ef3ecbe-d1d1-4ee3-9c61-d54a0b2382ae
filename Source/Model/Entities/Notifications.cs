﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    public class Notifications : BaseEntity
    {
        /// <summary>
        /// 通知唯一識別碼
        /// </summary>
        public Guid notification_id { get; set; }
        /// <summary>
        /// 通知類型識別碼
        /// </summary>
        public Guid? type_id { get; set; }
        /// <summary>
        /// 租戶識別碼
        /// </summary>
        public Guid? tenant_id { get; set; }
        /// <summary>
        /// 使用者識別碼
        /// </summary>
        public Guid? user_id { get; set; }
        /// <summary>
        /// 通知標題
        /// </summary>
        public string? title { get; set; }
        /// <summary>
        /// 通知內容
        /// </summary>
        public string? message { get; set; }
        /// <summary>
        /// JSON格式的事件詳細資訊
        /// </summary>
        public string? status { get; set; }
        /// <summary>
        /// 通知優先級
        /// </summary>
        public string? priority { get; set; }
        /// <summary>
        /// 通知過期時間
        /// </summary>
        public DateTimeOffset expires_at { get; set; }
        /// <summary>
        /// 回傳最多筆數設定
        /// </summary>
        public int Limit { get; set; } = 10;
        /// <summary>
        /// 跳過幾筆
        /// </summary>
        public int Offset { get; set; } = 0;
    }
}
