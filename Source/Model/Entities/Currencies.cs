﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// Currencies Entity
    /// </summary>
    public class Currencies : BaseEntity
    {
        /// <summary>
        /// 幣別唯一識別碼
        /// </summary>
        public Guid currency_id { get; set; }

        /// <summary>
        /// 幣別代碼
        /// </summary>
        public string? code { get; set; }

        /// <summary>
        /// 幣別符號
        /// </summary>
        public string? symbol { get; set; }

        /// <summary>
        /// 匯率
        /// </summary>
        public decimal? exchange_rate { get; set; }

        /// <summary>
        /// 幣別啟用狀態
        /// </summary>
        public bool? is_active { get; set; }
    }
}
