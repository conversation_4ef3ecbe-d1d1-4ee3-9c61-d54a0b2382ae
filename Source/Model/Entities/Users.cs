using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    public class Users : BaseEntity
    {
        /// <summary>
        /// 使用者唯一識別碼
        /// </summary>
        public Guid user_id { get; set; }

        /// <summary>
        /// 所屬租戶識別碼，可為空表示系統管理員
        /// </summary>
        public Guid tenant_id { get; set; }

        /// <summary>
        /// 使用者名稱
        /// </summary>
        public required string username { get; set; }

        /// <summary>
        /// 使用者電子郵件
        /// </summary>
        public required string email { get; set; }

        /// <summary>
        /// 密碼雜湊值
        /// </summary>
        public required string password_hash { get; set; }

        /// <summary>
        /// 使用者角色識別碼
        /// </summary>
        public Guid role_id { get; set; }

    }
}
