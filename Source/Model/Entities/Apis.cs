using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// API 服務實體類別
    /// </summary>
    /// <remarks>
    /// 此實體類別對應資料庫中的 APIs 表，用於存儲 API 服務的基本資訊。
    /// 繼承自 BaseEntity，因此包含所有實體的基本屬性，如時間戳記、ID 等。
    /// 此實體在使用 ORM 框架時會直接映射到資料庫表，用於 CRUD 操作。
    /// 每個 API 會關聯到一個租戶，並可能有多個索引鍵和用戶記錄。
    /// </remarks>
    public class Apis : BaseEntity
    {
        /// <summary>
        /// API 服務唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為 API 服務的主鍵。
        /// 此值在創建 API 服務時由系統自動指定，並在服務生命週期內保持不變。
        /// 在資料庫中此欄位設置為主鍵，並建立索引以加速查詢。
        /// 此 ID 在 API 管理、金鑰執行和綜合安全記錄中廣泛使用。
        /// </remarks>
        public Guid api_id { get; set; }

        /// <summary>
        /// 所屬租戶識別碼
        /// </summary>
        /// <remarks>
        /// 此 API 服務所屬的租戶識別碼，作為外鍵指向 Tenants 表的 tenant_id。
        /// 用於定義 API 服務與租戶之間的所有關係，以及定義存取控制和資源隔離。
        /// 在資料庫中建立外鍵約束，確保參考欄位的完整性。
        /// 各租戶的 API 完全隔離，使一個租戶無法存取或修改其他租戶的 API。
        /// </remarks>
        public Guid tenant_id { get; set; }

        /// <summary>
        /// API 服務名稱
        /// </summary>
        /// <remarks>
        /// API 服務的顯示名稱，用於管理介面、開發者入口和文檔中顯示。
        /// 此名稱應簡潔明確地反映 API 的主要功能或用途，如「付款 API」、「訂單 API」等。
        /// 在資料庫中通常需要為這欄位新增索引，以優化名稱搜索效能。
        /// 此欄位可能有長度限制，通常為 100 個字元左右。
        /// </remarks>
        public required string name { get; set; }

        /// <summary>
        /// API 功能描述
        /// </summary>
        /// <remarks>
        /// 對 API 服務功能和用途的詳細描述，用於文檔、開發者入口和管理界面。
        /// 該描述應清晰地說明 API 的功能、適用場景、主要特點和限制等信息。
        /// 此欄位通常為長文本字段（TEXT或VARCHAR(MAX)），以容納詳細的描述內容。
        /// 好的描述能幫助開發者更好地了解和使用 API，提高開發效率和減少支援需求。
        /// </remarks>
        public required string description { get; set; }

        /// <summary>
        /// API 版本號
        /// </summary>
        /// <remarks>
        /// API 的版本號，用於指定 API 的訂約、相容性和變更追蹤。
        /// 通常使用語義化版本號格式（主版本.次版本.修訂版本），如 1.0.0、2.1.3 等。
        /// - 主版本（Major）：表示不兼容的 API 變更
        /// - 次版本（Minor）：表示向下兼容的功能改進
        /// - 修訂版本（Patch）：表示向下兼容的錯誤修復
        /// 在資料庫中此欄位通常為 VARCHAR 型別，長度足以存儲版本號格式。
        /// 版本號將用於 API 的生命週期管理、不同版本之間的轉換和 API 文檔化。
        /// </remarks>
        public required string version { get; set; }

        /// <summary>
        /// 每分鐘最大請求次數
        /// </summary>
        /// <remarks>
        /// 此 API 每分鐘允許的最大請求數量，用於流量控制和資源保護。
        /// 超過此限制的請求將導致端點回傳 429 Too Many Requests 狀態碼。
        /// 適當的速率限制可保護伺服器免受過重負載，同時允許正常的使用場景。
        /// 根據 API 的用途和資源需求，典型值可能從每分鐘數百次到數千次不等。
        /// 在資料庫中此欄位為整數型別，存儲允許的請求限制值。
        /// </remarks>
        public int rate_limit_per_minute { get; set; }

        /// <summary>
        /// API 啟用狀態
        /// </summary>
        /// <remarks>
        /// 指示 API 服務的目前啟用狀態，控制 API 是否可用於客戶端。
        /// 當值為 true 時，表示 API 已啟用且可接受請求。
        /// 當值為 false 時，表示 API 已停用，所有請求將被拒絕並回傳適當的錯誤狀態碼。
        /// 在資料庫中此欄位為布林值型別，用於控制 API 的可用性。
        /// 這使得管理員可以暫時停用 API 而不需要完全刪除它，方便於維護和版本轉換。
        /// </remarks>
        public bool is_active { get; set; }
    }
}
