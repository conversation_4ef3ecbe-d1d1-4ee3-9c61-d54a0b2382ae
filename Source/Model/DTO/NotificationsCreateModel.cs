using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 創建系統通知時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了創建新的系統通知記錄時需要提供的必要資訊。
    /// 用於 HTTP POST 請求中，接收內部系統或管理介面提供的通知資料並進行驗證與處理。
    /// 符合 RESTful API 設計原則，用於 POST /v1/notifications 端點以創建新的系統通知。
    /// 通知記錄用於向租戶或特定使用者發送重要信息，如系統更新、帳單提醒、安全警報等。
    /// </remarks>
    public class NotificationsCreateModel
    {
        /// <summary>
        /// 通知的類型識別碼
        /// </summary>
        /// <remarks>
        /// 指定通知的類型，連結到通知類型表中的記錄，如系統警報、帳單提醒、系統更新等。
        /// 此欄位為必填項，用於確定通知的分類和處理邏輯。
        /// 不同的通知類型可能有不同的處理機制和顯示方式，如緊急通知可能需要即時推送。
        /// 控制器會驗證此 ID 對應到一個有效的通知類型。
        /// </remarks>
        public required Guid type_id { get; set; }
        /// <summary>
        /// 通知所屬的租戶識別碼
        /// </summary>
        /// <remarks>
        /// 指定通知所屬的租戶識別碼，連結到 tenants 表中的租戶記錄。
        /// 此欄位為必填項，用於確定通知的安全區隔和存取控制。
        /// 在多租戶系統中，此欄位確保通知只能被特定租戶的用戶讀取。
        /// 控制器會驗證此 ID 對應到一個有效的租戶，並確保發送者有權限對此租戶發送通知。
        /// </remarks>
        public required Guid tenant_id { get; set; }
        /// <summary>
        /// 通知接收者的使用者識別碼
        /// </summary>
        /// <remarks>
        /// 指定此通知的目標接收者，連結到使用者表中的記錄。
        /// 此欄位為必填項，確定通知將發送給哪位使用者。
        /// 從安全角度考慮，控制器應驗證指定的使用者屬於指定的租戶，以確保通知的適當連結。
        /// 在特定場景下，可能需要一個特殊的系統使用者 ID 來表示平台全域通知。
        /// </remarks>
        public required Guid user_id { get; set; }
        /// <summary>
        /// 通知的標題
        /// </summary>
        /// <remarks>
        /// 通知的標題或主旨，簡明描述通知的目的或內容。
        /// 此欄位為必填項，將在使用者介面中顯示為通知的主要標題。
        /// 標題應簡潔明確，且應考慮多語言環境下的本地化需求。
        /// 為了使用者體驗的一致性，標題長度可能會被限制。
        /// </remarks>
        public required string title { get; set; }
        /// <summary>
        /// 通知的詳細內容
        /// </summary>
        /// <remarks>
        /// 通知的完整內容或詳細說明，提供更多關於通知的詳細信息。
        /// 此欄位為必填項，系統可能會允許基本的文字格式化，如粗體、斜體或超鏈結。
        /// 內容應清楚、具體，並提供用戶需要採取的行動或重要信息。
        /// 某些通知類型可能需要特定的內容結構或格式，應根據 type_id 確保符合要求。
        /// </remarks>
        public required string message { get;set; }
        /// <summary>
        /// 通知的優先級別
        /// </summary>
        /// <remarks>
        /// 指定通知的重要性或緊急程度，只得使用指定的值："low"(低)、"medium"(中)、"high"(高)。
        /// 此欄位為必填項，影響通知在介面的顯示方式和可能的推送機制。
        /// 高優先級的通知可能會以特殊方式提醒用戶，如彈窗、聲音或手機録機提示。
        /// 應根據通知的實際重要性適當設置優先級，避免濟用高優先級而導致用戶忽視重要通知。
        /// </remarks>
        [AllowedValues("low","medium","high")]
        public required string priority { get; set; }
        /// <summary>
        /// 通知的過期時間
        /// </summary>
        /// <remarks>
        /// 指定通知的有效期限，超過此時間後通知將不再顯示給使用者或會被標記為過期。
        /// 此欄位為必填項，存儲為 ISO 8601 格式的日期時間帶時區資訊。
        /// 過期時間應基於通知的性質設置，如至關通知可能需要較長的有效期，而緊急事件通知可能只需要短時效期。
        /// 系統可能會定期清理過期的通知，以維持系統效能和優化用戶介面。
        /// </remarks>
        public required DateTimeOffset expires_at { get; set; }
    }
}
