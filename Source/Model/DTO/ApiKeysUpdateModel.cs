using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 更新 API 金鑰資訊時的數據傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型用於接收來自外部的請求，以更新現有 API 金鑰的資訊。
    /// 用於 HTTP PUT 請求中，在控制器層接收客戶端提供的更新項目。
    /// 所有屬性都是選填的，允許只更新部分屬性的資料。
    /// 符合 RESTful API 設計原則，用於 PUT /v1/api-keys/{key_id} 端點以更新現有 API 金鑰。
    /// 金鑰更新後，變更將影響使用此金鑰的所有客戶端應用程式。
    /// </remarks>
    public class ApiKeysUpdateModel
    {
        /// <summary>
        /// API 金鑰的唯一識別碼
        /// </summary>
        /// <remarks>
        /// 要更新的 API 金鑰的唯一 ID，用於進行金鑰的指定更新。
        /// 此欄位將在請求的網址路徑中提供，而非此模型中的資料內容。
        /// 控制器會驗證此 ID 對應到一個有效的存在 API 金鑰。
        /// </remarks>
        public Guid key_id { get; set; }

        /// <summary>
        /// API 金鑰的狀態
        /// </summary>
        /// <remarks>
        /// 指定 API 金鑰的狀態，如 "active"、"suspended"、"revoked" 等。
        /// 此欄位為可選填項，如果提供，控制器將更新金鑰的狀態。
        /// 狀態更改將直接影響金鑰的效力，例如「停用」狀態的金鑰將無法用於 API 認證。
        /// 若不提供此屬性，現有的金鑰狀態將保持不變。
        /// </remarks>
        public string? status { get; set; }

        /// <summary>
        /// API 金鑰的過期時間
        /// </summary>
        /// <remarks>
        /// 更新 API 金鑰的過期時間，超過此時間後，金鑰將失效且不能再用於認證。
        /// 此欄位為可選填項，如果設置為 null，表示金鑰永不過期。
        /// 此參數允許管理員延長或縮短金鑰的有效期，根據安全和商業要求。
        /// 若不提供此屬性，現有的過期時間設定將保持不變。
        /// </remarks>
        public DateTimeOffset? expiration_at { get; set; }

        /// <summary>
        /// API 存取範圍與權限設定
        /// </summary>
        /// <remarks>
        /// 更新 API 金鑰的存取權限範圍，指定其可以存取的 API 服務和操作。
        /// 此欄位為可選填項，使用 Scopes 物件定義細粒度的權限控制。
        /// 更新範圍後，金鑰的權限將立即變更，可能影響現有的正在進行的操作。
        /// 若不提供此屬性，現有的權限設定將保持不變。
        /// </remarks>
        public Scopes? scopes { get; set; }
    }
}
