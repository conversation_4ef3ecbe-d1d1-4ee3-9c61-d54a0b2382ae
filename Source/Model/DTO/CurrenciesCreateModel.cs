using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 創建貨幣幣別記錄時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了創建新貨幣幣別時需要提供的必要資訊。
    /// 用於 HTTP POST 請求中，接收管理員或內部系統提供的貨幣資料並進行驗證與處理。
    /// 符合 RESTful API 設計原則，用於 POST /v1/currencies 端點以創建新的貨幣幣別資源。
    /// 貨幣幣別用於系統中的所有金額相關操作，如帳單、付款和報表生成等。
    /// </remarks>
    public class CurrenciesCreateModel
    {
        /// <summary>
        /// 貨幣的標準代碼
        /// </summary>
        /// <remarks>
        /// 貨幣的標準代碼，符合 ISO 4217 標準，如 "TWD"、"USD"、"EUR"、"JPY" 等。
        /// 此欄位為必填項，通常為 3 個英文字母的組合，用於在系統中唯一識別貨幣。
        /// 代碼應為全球公認的貨幣索引，以確保与其他金融系統和外部服務的相容性。
        /// 控制器將檢查此代碼的唯一性，以避免系統中貨幣代碼的重複。
        /// </remarks>
        public required string code { get; set; }

        /// <summary>
        /// 貨幣的符號表示
        /// </summary>
        /// <remarks>
        /// 貨幣的外觀符號或傳統表示形式，如 "$"、"¥"、"€"、"NT$" 等。
        /// 此欄位為必填項，將在系統前端顯示和報表中使用，以便於用戶辨識金額的貨幣單位。
        /// 符號可能是 Unicode 字元，應確保在所有展示引擎和字體中都能正確呈現。
        /// 最少應提供最常見的符號表示，某些貨幣可能有多種表示形式（前置或後置）。
        /// </remarks>
        public required string symbol { get; set; }

        /// <summary>
        /// 相對於系統基準貨幣的匯率
        /// </summary>
        /// <remarks>
        /// 此貨幣相對於系統基準貨幣（通常為 USD 或系統預設貨幣）的單位匯率。
        /// 此欄位為必填項，使用小數點表示精確的匯率值，一般會保留多位小數。
        /// 匯率用於系統中與貨幣轉換相關的計算，如跨幣別的帳單、付款和報表生成。
        /// 匯率應定期更新以反映市場可能難以手動維護所有貨幣的精確匯率。
        /// </remarks>
        public required decimal exchange_rate { get; set; }

        /// <summary>
        /// 貨幣的啟用狀態
        /// </summary>
        /// <remarks>
        /// 指示貨幣在系統中是否為活躍狀態的布林值。
        /// 此欄位為必填項，當設置為 true 時，表示貨幣可用於系統中的交易和計算。
        /// 當設置為 false 時，表示貨幣被停用，不應在新交易中使用，但歷史記錄將保持不變。
        /// 停用的貨幣不應額外用於新創建的租戶、帳單或付款中，但應保持其遴史要考點資料。
        /// </remarks>
        public required bool is_active { get; set; }
    }
}
