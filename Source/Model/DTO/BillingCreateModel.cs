using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 創建帳單記錄時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了創建新帳單記錄時需要提供的必要資訊。
    /// 用於 HTTP POST 請求中，接收客戶端或內部系統提供的帳單資料並進行驗證與處理。
    /// 符合 RESTful API 設計原則，用於 POST /v1/billing 端點以創建新的帳單資源。
    /// 帳單記錄用於追蹤租戶的收費和付款情況，包含重要的財務和帳務資訊。
    /// </remarks>
    public class BillingCreateModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public BillingCreateModel()
        {
            status = string.Empty;
        }

        /// <summary>
        /// 帳單所屬的租戶識別碼
        /// </summary>
        /// <remarks>
        /// 指定此帳單記錄所屬的租戶唯一識別碼，連結到 tenants 表中的現有租戶。
        /// 此欄位為必填項，用於建立帳單與租戶之間的關聯關係。
        /// 系統會使用此識別碼來組織和分類帳單，便於租戶的財務追蹤和報表生成。
        /// 控制器會驗證此 ID 對應到一個有效的租戶，並確保其與該帳單的其他設置（如幣別）一致。
        /// </remarks>
        public Guid tenant_id { get; set; }

        /// <summary>
        /// 帳單的金額
        /// </summary>
        /// <remarks>
        /// 此帳單的金額價值，表示租戶應支付的金額。
        /// 此欄位為必填項，應為大於零的數值，以小數點形式儲存，以支援精確的貨幣金額。
        /// 此金額與帳單的 currency_id 結合使用，確定完整的金額表示。
        /// 系統可能會基於此金額套用不同的計算邏輯，如折扣、稅金或手續費等。
        /// </remarks>
        public decimal amount { get; set; }

        /// <summary>
        /// 帳單的貨幣幣別識別碼
        /// </summary>
        /// <remarks>
        /// 指定此帳單的貨幣幣別，連結到 currencies 表中的貨幣記錄。
        /// 此欄位為必填項，確定帳單金額的貨幣單位，如 TWD、USD 等。
        /// 綜合 amount 欄位使用，形成完整的金額表示，如 1000 USD 或 30000 TWD。
        /// 通常與租戶的預設幣別設定相同，除非有特殊需求，應確保其與租戶設置一致。
        /// </remarks>
        public Guid currency_id { get; set; }

        /// <summary>
        /// 帳單的付款方式識別碼
        /// </summary>
        /// <remarks>
        /// 指定此帳單的付款方式，連結到付款方式表中的記錄。
        /// 此欄位為必填項，確定租戶應使用的付款管道，如信用卡、電子轉帳、PayPal 等。
        /// 付款方式會影響處理邏輯、手續費率和結算時間，對帳單的處理流程至關重要。
        /// 應確保選擇的付款方式與租戶的帳戶設置和帳單金額相符，某些付款方式可能有最小或最大金額限制。
        /// </remarks>
        public Guid payment_method_id { get; set; }

        /// <summary>
        /// 帳單的狀態
        /// </summary>
        /// <remarks>
        /// 指定帳單的當前狀態，如 "pending"、"paid"、"overdue"、"cancelled" 等。
        /// 此欄位為必填項，使用預定義的狀態值集合，以確保一致性和有效的狀態追蹤。
        /// 狀態變化應跟隨帳單生命週期的變更，從創建到最終付款或取消。
        /// 狀態變化會觸發不同的商業邏輯，如付款提醒、逸期費計算或帳戶狀態變更。
        /// </remarks>
        public required string status { get; set; }

        /// <summary>
        /// 帳單的結帳到期時間
        /// </summary>
        /// <remarks>
        /// 指定此帳單的接付截止日期和時間，超過此時間後帳單將變為逸期。
        /// 此欄位為必填項，存儲為 ISO 8601 格式的日期時間帶時區資訊。
        /// 到期日應設置在將來的時間，且應考慮租戶的付款條款和局部商業慣例。
        /// 系統會使用此日期觸發通知、計算逸期費用和產生到期提醒等自動化操作。
        /// </remarks>
        public DateTimeOffset due_date { get; set; }
    }
}
