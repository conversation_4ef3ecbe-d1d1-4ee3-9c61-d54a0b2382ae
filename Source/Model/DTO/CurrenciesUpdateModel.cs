using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 更新貨幣幣別記錄時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型用於接收來自外部的請求，以更新現有貨幣幣別的資訊。
    /// 用於 HTTP PUT 請求中，在控制器層接收管理員或系統提供的更新項目。
    /// 所有屬性都是選填的，允許只更新部分屬性的資料，如匯率或狀態。
    /// 符合 RESTful API 設計原則，用於 PUT /v1/currencies/{currency_id} 端點以更新現有貨幣幣別。
    /// 貨幣更新後，變更將影響系統中的金額計算和貨幣轉換操作。
    /// </remarks>
    public class CurrenciesUpdateModel
    {
        /// <summary>
        /// 貨幣幣別的唯一識別碼
        /// </summary>
        /// <remarks>
        /// 要更新的貨幣幣別的唯一 ID，用於進行貨幣的指定更新。
        /// 此欄位將在請求的網址路徑中提供，而非此模型中的資料內容。
        /// 控制器會驗證此 ID 對應到一個有效的存在貨幣記錄。
        /// 由於貨幣記錄可能被廣泛引用，更新提升要謹慎處理，以避免影響現有交易和帳單。
        /// </remarks>
        public Guid currency_id { get; set; }
        /// <summary>
        /// 貨幣的標準代碼
        /// </summary>
        /// <remarks>
        /// 貨幣的標準代碼，符合 ISO 4217 標準，如 "TWD"、"USD"、"EUR"、"JPY" 等。
        /// 此欄位為可選填項，如果提供，控制器將更新貨幣的代碼。
        /// 代碼修改應謹慎處理，因為代碼在系統中可能被作為索引或查詢參數廣泛使用。
        /// 若不提供此屬性，現有的貨幣代碼將保持不變。
        /// </remarks>
        public string? code { get; set; }
        /// <summary>
        /// 貨幣的符號表示
        /// </summary>
        /// <remarks>
        /// 貨幣的外觀符號或傳統表示形式，如 "$"、"¥"、"€"、"NT$" 等。
        /// 此欄位為可選填項，如果提供，控制器將更新貨幣的符號表示。
        /// 符號更新將影響系統前端和報表中金額的顯示方式，應確保新符號能正確呈現。
        /// 若不提供此屬性，現有的貨幣符號將保持不變。
        /// </remarks>
        public string? symbol { get; set; }
        /// <summary>
        /// 相對於系統基準貨幣的匯率
        /// </summary>
        /// <remarks>
        /// 此貨幣相對於系統基準貨幣（通常為 USD 或系統預設貨幣）的單位匯率。
        /// 此欄位為可選填項，如果提供，控制器將更新貨幣的匯率。
        /// 匯率更新是貨幣最常見的更新操作，動態反映市場匯率變化，以確保系統中金額計算的準確性。
        /// 若不提供此屬性，現有的匯率將保持不變。
        /// </remarks>
        public decimal? exchange_rate { get; set; }
        /// <summary>
        /// 貨幣的啟用狀態
        /// </summary>
        /// <remarks>
        /// 指示貨幣在系統中是否為活躍狀態的布林值。
        /// 此欄位為可選填項，如果提供，控制器將更新貨幣的狀態。
        /// 當設置為 true 時，表示貨幣可用於系統中的交易和計算。
        /// 當設置為 false 時，表示貨幣被停用，不應在新交易中使用，但不會影響現有的使用此貨幣的交易。
        /// 若不提供此屬性，現有的狀態設定將保持不變。
        /// </remarks>
        public bool? is_active { get; set; }
    }
}
