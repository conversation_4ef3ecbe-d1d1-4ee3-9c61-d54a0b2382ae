using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 創建系統角色時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了創建新的系統角色記錄時需要提供的必要資訊。
    /// 用於 HTTP POST 請求中，接收系統管理員提供的角色資料並進行驗證與處理。
    /// 符合 RESTful API 設計原則，用於 POST /v1/roles 端點以創建新的系統角色。
    /// 角色是安全模型的基礎組成部分，用於定義使用者群組和欄彍所擁有的變機權限。
    /// 作為基於角色的存取控制(RBAC)模型的一部分，角色定義了使用者可以帘一來操作在系統中的行為。
    /// </remarks>
    public class RolesCreateModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public RolesCreateModel()
        {
            name = string.Empty;
            description = string.Empty;
        }

        /// <summary>
        /// 角色的名稱
        /// </summary>
        /// <remarks>
        /// 指定角色的名稱，如「系統管理員」、「租戶管理員」、「一般使用者」、「唯讀使用者」等。
        /// 此欄位為必填項，在系統中必須保持唯一性，以避免角色混淆。
        /// 角色名稱應簡潔方便記憶，並清楚地反映該角色的用途和權限範圍。
        /// 名稱將在管理介面和權限設定中顯示，用於識別和選擇角色。
        /// </remarks>
        public required string name { get; set; }

        /// <summary>
        /// 角色的實質描述
        /// </summary>
        /// <remarks>
        /// 指定角色的詳細描述，說明其用途、權限範圍和適用場景。
        /// 此欄位為選填項，但強烈建議提供，以協助管理員理解角色的用途和功能。
        /// 描述應介紹得足夠詳細，包含該角色可以存取的系統功能和執行的操作範圍。
        /// 完整而清楚的描述有助於確保角色指配的準確性，並促進系統安全性。
        /// </remarks>
        public required string description { get; set; }
    }
}
