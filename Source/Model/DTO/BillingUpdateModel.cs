using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 更新帳單記錄時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型用於接收來自外部的請求，以更新現有帳單記錄的資訊。
    /// 用於 HTTP PUT 請求中，在控制器層接收客戶端或系統內部提供的更新項目。
    /// 主要用於變更帳單的狀態和記錄付款時間，此模型的設計較為精簡。
    /// 符合 RESTful API 設計原則，用於 PUT /v1/billing/{record_id} 端點以更新現有帳單。
    /// </remarks>
    public class BillingUpdateModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public BillingUpdateModel()
        {
            status = string.Empty;
        }

        /// <summary>
        /// 帳單的狀態
        /// </summary>
        /// <remarks>
        /// 更新帳單的狀態，如 "pending"、"paid"、"overdue"、"cancelled" 等。
        /// 此欄位為必填項，反映帳單在付款生命週期中的目前狀態。
        /// 狀態變更可能觸發各種系統事件，如通知、記錄更新、報表生成等。
        /// 只從為一種狀態變更為另一種狀態的某些轉換是有效的，控制器將有專門的狀態驗證邏輯。
        /// </remarks>
        public string status { get; set; }

        /// <summary>
        /// 帳單的實際支付時間
        /// </summary>
        /// <remarks>
        /// 記錄帳單實際被支付的日期和時間。
        /// 此欄位通常在帳單狀態變更為 "paid" 時設置，存儲為 ISO 8601 格式的日期時間帶時區資訊。
        /// 此時間戳將用於帳務記錄、收入報表和收費分析，到期與付款時間的差距可用於分析付款行為。
        /// 如果帳單未付款，此欄位應為預設值或最小日期值，直到收到付款確認才更新。
        /// </remarks>
        public DateTimeOffset paid_at { get; set; }
    }
}
