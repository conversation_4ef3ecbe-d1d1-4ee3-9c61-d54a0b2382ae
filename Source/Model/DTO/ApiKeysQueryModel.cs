using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// API 金鑰查詢時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了查詢 API 金鑰記錄時需要提供的查詢條件參數。
    /// 用於 HTTP GET 請求中，在 URL 查詢參數中接收客戶端提供的查詢條件。
    /// 此模型繼承自 BaseQueryModel，因此也包含了分頁相關的參數（Limit、Offset）。
    /// 符合 RESTful API 設計原則，用於 GET /v1/api-keys 端點以查詢 API 金鑰記錄。
    /// 查詢結果將根據提供的查詢參數進行篩選，並以分頁格式返回結果。
    /// </remarks>
    public class ApiKeysQueryModel : BaseQueryModel
    {
        /// <summary>
        /// API 金鑰所屬的租戶識別碼
        /// </summary>
        /// <remarks>
        /// 指定要查詢的 API 金鑰所屬的租戶識別碼，連結到 tenants 表中的租戶記錄。
        /// 此欄位為可選填項，用於篩選特定租戶的 API 金鑰。
        /// 如果設置為 null，表示查詢所有租戶的 API 金鑰（前提是查詢者具有足夠的權限）。
        /// 控制器會驗證使用者的權限，確保其只能查詢其有權存取的租戶的 API 金鑰。
        /// </remarks>
        public Guid? tenant_id { get; set; }

        /// <summary>
        /// API 金鑰的狀態
        /// </summary>
        /// <remarks>
        /// 指定要查詢的 API 金鑰的狀態，如 "active"（活躍）、"revoked"（已撤銷）、"expired"（已過期）等。
        /// 此欄位為可選填項，用於篩選特定狀態的 API 金鑰。
        /// 如果設置為 null，表示查詢所有狀態的 API 金鑰。
        /// 控制器應驗證提供的狀態值是否在系統定義的狀態集合中，以確保查詢的有效性。
        /// </remarks>
        public string? status { get; set; }
    }
}
