
namespace Paas.Model
{
    /// <summary>
    /// 安全日誌查詢時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了查詢系統安全日誌記錄時需要提供的查詢條件參數。
    /// 用於 HTTP GET 請求中，在 URL 查詢參數中接收客戶端提供的查詢條件。
    /// 此模型繼承自 BaseQueryModel，因此也包含了分頁相關的參數（Limit、Offset）。
    /// 安全日誌查詢用於系統審計、安全分析和問題追蹤，是安全運營的重要工具。
    /// 符合 RESTful API 設計原則，用於 GET /v1/security-logs 端點以查詢安全日誌記錄。
    /// </remarks>
    public class SecurityLogsQueryModel : BaseQueryModel
    {
        /// <summary>
        /// 日誌記錄所屬的租戶識別碼
        /// </summary>
        /// <remarks>
        /// 指定要查詢的安全日誌記錄所屬的租戶識別碼，連結到 tenants 表中的租戶記錄。
        /// 此欄位為可選填項，用於篩選特定租戶的安全日誌。
        /// 如果設置為 null，表示查詢所有租戶的日誌記錄（前提是查詢者具有足夠的權限）。
        /// 控制器將執行權限檢查，確保使用者只能查詢其有權限存取的租戶的日誌記錄。
        /// </remarks>
        public Guid? tenant_id { get; set; }

        /// <summary>
        /// 安全事件的類型識別碼
        /// </summary>
        /// <remarks>
        /// 指定要查詢的安全事件類型，如登入失敗、密碼重設、權限更改、管理操作等。
        /// 此欄位為可選填項，用於篩選特定類型的安全事件。
        /// 如果設置為 null，表示查詢所有類型的安全事件。
        /// 事件類型的驗證應執行在控制器層，確保指定的事件類型 ID 對應到系統中已定義的安全事件類型。
        /// </remarks>
        public string? event_type_id { get; set; }
        /// <summary>
        /// 查詢的時間範圍開始時間
        /// </summary>
        /// <remarks>
        /// 指定要查詢的安全日誌記錄的開始時間範圍。
        /// 此欄位設置為記錄的時間戳大於或等於此值的記錄。
        /// 存儲為 ISO 8601 格式的日期時間帶時區資訊，確保在不同時區的清晰性。
        /// 平台應設置合理的預設值，如「過去 24 小時」或「過去 7 天」，但也應允許管理員指定更廣的時間範圍。
        /// </remarks>
        public DateTimeOffset start_time { get; set; }
        /// <summary>
        /// 查詢的時間範圍結束時間
        /// </summary>
        /// <remarks>
        /// 指定要查詢的安全日誌記錄的結束時間範圍。
        /// 此欄位設置為記錄的時間戳小於或等於此值的記錄。
        /// 存儲為 ISO 8601 格式的日期時間帶時區資訊，確保在不同時區的清晰性。
        /// 為了效能考慮，系統可能會限制時間範圍的最大寬度，確保查詢不會處理過量的記錄。
        /// </remarks>
        public DateTimeOffset end_time { get; set; }
    }
}
