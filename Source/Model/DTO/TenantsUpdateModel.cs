using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 更新租戶資訊時的數據傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型用於接收來自外部的請求，以更新現有租戶的資訊。
    /// 用於 HTTP PUT 請求中，在控制器層接收客戶端提供的更新項目。
    /// 所有屬性都是選填的，允許將只更新部分屬性的資料。
    /// 符合 RESTful API 設計原則，用於 PUT /v1/tenants/{tenant_id} 端點以更新現有租戶。
    /// </remarks>
    public class TenantsUpdateModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public TenantsUpdateModel()
        {
            contact_email = string.Empty;
            name = string.Empty;
            legal_address = new AddressModel
            {
                street = string.Empty,
                city = string.Empty,
                country = string.Empty,
                postalCode = string.Empty
            };
        }

        /// <summary>
        /// 租戶的主要聯絡電子郵件
        /// </summary>
        /// <remarks>
        /// 租戶的主要聯絡電子郵件地址，用於系統通知、帳單發送和重要更新等用途。
        /// 當提供此屬性時，控制器將更新租戶的聯絡電子郵件。
        /// 必須是有效的電子郵件格式，並將在控制器層進行驗證。
        /// 若不提供此屬性，現有的聯絡電子郵件將保持不變。
        /// </remarks>
        public string contact_email { get; set; }

        /// <summary>
        /// 租戶法定名稱
        /// </summary>
        /// <remarks>
        /// 租戶的正式法定名稱，用於帳單、合約和法律文件等場合。
        /// 當提供此屬性時，控制器將更新租戶的法定名稱。
        /// 應與租戶的商業登記或法律文件上的正式名稱相符。
        /// 若不提供此屬性，現有的租戶名稱將保持不變。
        /// </remarks>
        public string name { get; set; }

        /// <summary>
        /// 帳單計價幣別 ID
        /// </summary>
        /// <remarks>
        /// 租戶的帳單計價幣別，指向 currencies 表中對應貨幣的 currency_id。
        /// 當提供此屬性時，控制器將更新租戶的計價幣別。
        /// 預期為符合 ISO 4217 標準的貨幣 ID，如 TWD、USD 等對應的唯一識別碼。
        /// 若不提供此屬性，現有的計價幣別將保持不變。
        /// </remarks>
        public Guid billing_currency { get; set; }

        /// <summary>
        /// 租戶法律認證地址
        /// </summary>
        /// <remarks>
        /// 租戶的法律認證地址，以結構化的 AddressModel 物件形式存儲。
        /// 當提供此屬性時，控制器將更新租戶的法律地址資訊。
        /// 此資訊用於帳單、法律文件和合約等正式用途，應確保其準確性和完整性。
        /// 若不提供此屬性，現有的法律地址將保持不變。
        /// </remarks>
        public AddressModel legal_address { get; set; }

        /// <summary>
        /// 租戶帳戶狀態
        /// </summary>
        /// <remarks>
        /// 指示租戶帳戶是否處於活躍狀態的布林值。
        /// 当設置為 true 時，表示租戶是活躍的，可以使用系統的全部功能。
        /// 当設置為 false 時，表示租戶被禁用，可能因為帳單問題、合約終止或其他原因。
        /// 若不提供此屬性（留為 null），則現有的活躍狀態將保持不變。
        /// </remarks>
        public bool? is_active { get; set; }
    }
}
