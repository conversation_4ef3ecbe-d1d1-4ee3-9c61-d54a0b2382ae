using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 創建通知管道時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了創建新的通知管道記錄時需要提供的必要資訊。
    /// 用於 HTTP POST 請求中，接收管理員或租戶設定的通知管道資料並進行驗證與處理。
    /// 符合 RESTful API 設計原則，用於 POST /v1/notification-channels 端點以創建新的通知管道。
    /// 通知管道用於定義系統如何將通知發送給用戶，如電子郵件、簡訊、應用程序內部提醒等。
    /// </remarks>
    public class NotificationChannelsCreateModel
    {
        /// <summary>
        /// 通知管道的類型
        /// </summary>
        /// <remarks>
        /// 指定通知管道的類型，如 "email"(電子郵件)、"sms"(簡訊)、"in_app"(應用程序內部)、"push"(推送通知) 等。
        /// 此欄位為必填項，用於確定通知的發送方式和相關設定需求。
        /// 不同的通知管道類型將需要不同的設定資訊，如電子郵件需要 SMTP 服務器設定，簡訊需要 SMS 服務提供商設定等。
        /// 系統可能會限制可用的通知管道類型，控制器應驗證此屬性符合系統支援的類型清單。
        /// </remarks>
        public required string type { get; set; }

        /// <summary>
        /// 通知管道的詳細設定
        /// </summary>
        /// <remarks>
        /// 指定通知管道的細節配置資訊，如服務器設定、軟體密鑰、樣板設定等。
        /// 此欄位為必填項，使用 NotificationChannelsConfig 物件存儲結構化的設定資訊。
        /// 實際設定內容必須符合所選通知管道類型（type）的需求，如電子郵件需要 SMTP 主機和帳號資訊。
        /// 所有敏感資訊（如密碼、API 密鑰）應適當加密存儲，控制器應處理並驗證配置符合安全標準。
        /// </remarks>
        public required NotificationChannelsConfig config { get; set; }

        /// <summary>
        /// 通知管道的啟用狀態
        /// </summary>
        /// <remarks>
        /// 指示此通知管道是否處於啟用狀態的布林值。
        /// 此欄位預設為 true，表示管道在創建後立即啟用。
        /// 當設置為 true 時，通知將使用此管道發送，並根據管道設定進行處理。
        /// 當設置為 false 時，此管道將不會用於發送通知，即使其設定正確有效。
        /// 此設定允許管理員暂時关閉通知管道而不需要刪除它，方便未來再次啟用。
        /// </remarks>
        public bool is_active { get; set; } = true;
    }
}
