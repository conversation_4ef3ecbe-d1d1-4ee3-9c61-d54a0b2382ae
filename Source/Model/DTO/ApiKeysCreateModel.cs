using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 創建 API 金鑰時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了創建新 API 金鑰時需要提供的必要資訊。
    /// 用於 HTTP POST 請求中，接收客戶端提供的資料並進行驗證與處理。
    /// 符合 RESTful API 設計原則，用於 POST /v1/api-keys 端點以創建新的 API 存取金鑰。
    /// API 金鑰是客戶端識別和授權的主要機制，用於安全地存取 API 服務。
    /// </remarks>
    public class ApiKeysCreateModel
    {
        /// <summary>
        /// 金鑰所屬的租戶 ID
        /// </summary>
        /// <remarks>
        /// 指定 API 金鑰所屬的租戶唯一識別碼，連結到 tenants 表中的現有租戶。
        /// 此欄位為必填項，金鑰將與此租戶關聯並受到相應的設定與限制。
        /// 此識別碼使系統能追蹤和管理每個租戶的 API 請求和使用情況。
        /// 控制器會驗證此 ID 對應到一個有效的、处於活躍狀態的租戶。
        /// </remarks>
        public required Guid tenant_id { get; set; }

        /// <summary>
        /// API 存取金鑰字串
        /// </summary>
        /// <remarks>
        /// API 金鑰的實際值，用於請求認證和授權。
        /// 此欄位為必填項，應該是一個足夠強大的穩定字串，通常是自動生成的隨機字串。
        /// 存取金鑰應該在系統中保持唯一性，並应遵循安全維守策略(如適當的長度和複雜性)。
        /// 此金鑰一旦創建，將用於 API 請求的授權頭信息中進行認證。
        /// </remarks>
        public required string api_key { get; set; }

        /// <summary>
        /// API 存取範圍與權限設定
        /// </summary>
        /// <remarks>
        /// 定義此 API 金鑰的存取權限範圍，指定其可以存取的 API 服務和操作。
        /// 此欄位為必填項，使用 Scopes 物件定義細粒度的權限控制。
        /// 範圍可以限制金鑰可以存取的資源、操作和功能，如唯讀、讀寫或管理權限。
        /// 遠端系統會在授權時驗證請求是否符合這些指定的範圍權限。
        /// </remarks>
        public required Scopes scopes { get; set; }

        /// <summary>
        /// API 金鑰的過期時間
        /// </summary>
        /// <remarks>
        /// 指定 API 金鑰的過期時間，超過此時間後，金鑰將失效且不能再用於認證。
        /// 此欄位為可選填項，如果設置為 null，表示金鑰永不過期。
        /// 為了安全考慮，建議設置適當的過期時間，以減少金鑰浪費或遭受到危害的風險。
        /// 系統會定期檢查金鑰的過期狀態，與自動清理或通知管理員更新過期的金鑰。
        /// </remarks>
        public DateTimeOffset? expiration_at { get; set; }
    }
}
