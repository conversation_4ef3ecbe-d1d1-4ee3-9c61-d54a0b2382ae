
namespace Paas.Model
{
    /// <summary>
    /// 系統通知查詢時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了查詢系統通知記錄時需要提供的查詢條件參數。
    /// 用於 HTTP GET 請求中，在 URL 查詢參數中接收客戶端提供的查詢條件。
    /// 此模型繼承自 BaseQueryModel，因此也包含了分頁相關的參數（Limit、Offset）。
    /// 符合 RESTful API 設計原則，用於 GET /v1/notifications 端點以查詢系統通知記錄。
    /// 查詢結果將根據提供的查詢參數進行篩選，並以分頁格式返回結果。
    /// </remarks>
    public class NotificationsQueryModel : BaseQueryModel
    {
        /// <summary>
        /// 通知所屬的租戶識別碼
        /// </summary>
        /// <remarks>
        /// 指定要查詢的通知所屬的租戶識別碼，連結到 tenants 表中的租戶記錄。
        /// 此欄位為可選填項，用於篩選特定租戶的通知。
        /// 如果設置為 null，表示查詢所有租戶的通知（前提是查詢者具有足夠的權限）。
        /// 控制器會驗證使用者的權限，確保其只能查詢其有權存取的租戶的通知。
        /// </remarks>
        public Guid? tenant_id { get; set; }
        /// <summary>
        /// 通知接收者的使用者識別碼
        /// </summary>
        /// <remarks>
        /// 指定要查詢的通知接收者的使用者識別碼，連結到使用者表中的記錄。
        /// 此欄位為可選填項，用於篩選特定使用者的通知。
        /// 如果設置為 null，表示查詢所有使用者的通知（前提是查詢者具有足夠的權限）。
        /// 在一般情況下，使用者只能查詢自己的通知，而管理員可能有權限查看其他使用者的通知。
        /// </remarks>
        public Guid? user_id { get; set; }
        /// <summary>
        /// 通知的閱讀狀態
        /// </summary>
        /// <remarks>
        /// 指定要查詢的通知閱讀狀態，可選值為 "read"（已讀）或 "unread"（未讀）。
        /// 此欄位為可選填項，用於篩選已讀或未讀的通知。
        /// 如果設置為 null，表示查詢所有閱讀狀態的通知。
        /// 使用 AllowedValues 屬性限制此欄位只能接受指定的值，確保查詢的有效性。
        /// </remarks>
        [AllowedValues("read", "unread")]
        public string? status { get; set; }
        /// <summary>
        /// 通知的優先級別
        /// </summary>
        /// <remarks>
        /// 指定要查詢的通知優先級別，可選值為 "low"（低）、"medium"（中）或 "high"（高）。
        /// 此欄位為可選填項，用於篩選特定優先級別的通知。
        /// 如果設置為 null，表示查詢所有優先級別的通知。
        /// 使用 AllowedValues 屬性限制此欄位只能接受指定的值，確保查詢的有效性。
        /// 優先級別可用於排序和篩選重要通知，如查詢所有高優先級通知。
        /// </remarks>
        [AllowedValues("low", "medium", "high")]
        public string? priority { get; set; }
    }
}
