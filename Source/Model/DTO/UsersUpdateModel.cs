using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 更新系統使用者資訊時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型用於接收來自外部的請求，以更新現有使用者的資訊。
    /// 用於 HTTP PUT 請求中，在控制器層接收管理員或使用者本人提供的更新項目。
    /// 所有屬性都是選填的，允許只更新部分屬性的資料。
    /// 符合 RESTful API 設計原則，用於 PUT /v1/users/{user_id} 端點以更新現有使用者。
    /// 此模型有意讓再沒有包含 username，因為使用者名稱通常不允許更改，也沒有包含 password_hash，因為密碼更新應該通過專用的密碼重設功能處理。
    /// </remarks>
    public class UsersUpdateModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public UsersUpdateModel()
        {
            email = string.Empty;
        }

        /// <summary>
        /// 使用者的電子郵件地址
        /// </summary>
        /// <remarks>
        /// 使用者的電子郵件地址，用於系統登入、身份驗證和通知發送。
        /// 此欄位為可選填項，如果提供，控制器將更新使用者的電子郵件地址。
        /// 變更電子郵件地址後，系統可能需要重新驗證新的電子郵件地址，以確保它的有效性和真實性。
        /// 電子郵件地址必須在整個系統中保持唯一性，控制器應驗證新的電子郵件地址不會與其他使用者重複。
        /// </remarks>
        public string email { get; set; }

        /// <summary>
        /// 使用者的角色識別碼
        /// </summary>
        /// <remarks>
        /// 指定使用者的新系統角色，連結到角色表中的記錄。
        /// 此欄位為可選填項，如果提供，控制器將更新使用者的系統角色。
        /// 角色更新將直接影響使用者的權限和存取範圍，對系統安全和組織涵構至關重要。
        /// 控制器必須檢查權限升級請求，確保只有體限充分的系統管理員才能指配更高的權限角色。
        /// 若不提供此屬性，現有的角色設定將保持不變。
        /// </remarks>
        public Guid role_id { get; set; }
    }
}
