using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 創建 API 服務時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了創建新 API 服務時需要提供的必要資訊。
    /// 用於 HTTP POST 請求中，接收客戶端提供的資料並進行驗證與處理。
    /// 符合 RESTful API 設計原則，只包含創建 API 服務時需要提供的欄位，不包含系統自動生成的值如 api_id。
    /// 用於 POST /v1/apis 端點，以建立新的 API 服務資源。
    /// </remarks>
    public class ApisCreateModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public ApisCreateModel()
        {
            name = string.Empty;
            description = string.Empty;
            version = string.Empty;
        }

        /// <summary>
        /// 所屬租戶的唯一識別碼
        /// </summary>
        /// <remarks>
        /// 指定 API 服務所屬的租戶 ID，連結到 tenants 表中的現有租戶。
        /// 此欄位為必填項，用於建立 API 服務與租戶之間的關聯關係。
        /// 此識別碼將用於授權、計費和其他控制操作，確定 API 服務屬於哪個租戶。
        /// 控制器會驗證此 ID 對應到一個有效的、处於活躍狀態的租戶。
        /// </remarks>
        public Guid tenant_id { get; set; }

        /// <summary>
        /// API 服務的名稱
        /// </summary>
        /// <remarks>
        /// API 服務的顯示名稱，用於界面顯示和識別。
        /// 此欄位為必填項，應簡明且清楚地描述 API 服務的用途或功能。
        /// 名稱應具有描述性但不宜過長，如「用戶認證 API」、「支付網關 API」等。
        /// 使用者和管理員將使用此名稱在系統中尋找和管理此 API 服務。
        /// </remarks>
        public required string name { get; set; }

        /// <summary>
        /// API 服務的功能描述
        /// </summary>
        /// <remarks>
        /// 詳細的 API 服務功能描述，用於提供更具體的信息說明此 API 的用途和功能。
        /// 此欄位為必填項，可包含水平、優勢、使用情境等詳細信息。
        /// 一個好的描述可以幫助使用者或其他開發者理解此 API 的功能與限制。
        /// 此描述將顯示在開發者門戶和 API 管理界面中，作為說明文檔。
        /// </remarks>
        public required string description { get; set; }

        /// <summary>
        /// API 服務的版本號
        /// </summary>
        /// <remarks>
        /// API 服務的版本號，用於識別不同版本的 API 實現。
        /// 此欄位為必填項，建議使用語意化版本編號（SemVer）格式，如 1.0.0、2.1.3 等。
        /// 例如：主版本.minor版本.patch版本，以區分不同程度的變更。
        /// 版本號在 API 管理中至關重要，將用於跟蹤 API 演進和向後相容性。
        /// </remarks>
        public required string version { get; set; }

        /// <summary>
        /// API 每分鐘的請求限制數
        /// </summary>
        /// <remarks>
        /// 每分鐘允許的最大 API 請求數量，用於限制和管理 API 的使用率。
        /// 此欄位為必填項，必須是正整數，建議基於 API 資源負荷和服務層級設定合理的值。
        /// 這個限制將由系統強制執行，以確保 API 服務和底層系統的穩定性。
        /// 該限制在租戶端適用，不同的租戶可以設置不同的限制。
        /// </remarks>
        public int rate_limit_per_minute { get; set; }

    }
}
