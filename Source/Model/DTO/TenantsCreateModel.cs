using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 創建租戶時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了創建新租戶時需要提供的必要資訊。
    /// 用於 HTTP POST 請求中，接收客戶端提供的資料並進行驗證與處理。
    /// 符合 RESTful API 設計原則，只包含創建租戶時需要提供的欄位，不包含系統自動生成的值如 tenant_id。
    /// 用於 POST /v1/tenants 端點，以建立新的租戶資源。
    /// </remarks>
    public class TenantsCreateModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public TenantsCreateModel()
        {
            contact_email = string.Empty;
            name = string.Empty;
            legal_address = new AddressModel
            {
                street = string.Empty,
                city = string.Empty,
                country = string.Empty,
                postalCode = string.Empty
            };
        }

        /// <summary>
        /// 租戶的主要聯絡電子郵件
        /// </summary>
        /// <remarks>
        /// 租戶的主要聯絡電子郵件地址，用於系統通知、帳單發送和重要更新等用途。
        /// 此欄位為必填項，必須是有效的電子郵件格式，並將於控制器層進行驗證。
        /// 常設置為租戶管理員、財務主管或公司通用聯絡郵箱。
        /// 在商業構通和系統相關通知中作為預設的聯絡管道。
        /// </remarks>
        public required string contact_email { get; set; }

        /// <summary>
        /// 租戶法定名稱
        /// </summary>
        /// <remarks>
        /// 租戶的正式法定名稱，用於帳單、合約和法律文件等場合。
        /// 此欄位為必填項，應與租戶的商業登記或法律文件上的正式名稱相符。
        /// 在建立租戶時，此名稱將用於系統中的顯示和參考，以識別不同的商業實體。
        /// 名稱應明確且有辨識度，避免與其他租戶混淆。
        /// </remarks>
        public required string name { get; set; }

        /// <summary>
        /// 帳單計價幣別 ID
        /// </summary>
        /// <remarks>
        /// 租戶預設的帳單計價幣別，指向 currencies 表中對應貨幣的 currency_id。
        /// 此欄位為必填項，決定了租戶的所有帳單和付款將使用的預設貨幣。
        /// 預期為符合 ISO 4217 標準的貨幣 ID，如 TWD、USD 等對應的唯一識別碼。
        /// 由客戶端提供，但應在控制器層驗證它對應到有效的貨幣資料。
        /// </remarks>
        public Guid billing_currency { get; set; }

        /// <summary>
        /// 租戶法律認證地址
        /// </summary>
        /// <remarks>
        /// 租戶的法律認證地址，以結構化的 AddressModel 物件形式存儲。
        /// 此欄位包含街道名稱、城市、國家和郵遞編碼等詳細的地址資訊。
        /// 此資訊用於帳單、法律文件和合約等正式用途，應確保其準確性和完整性。
        /// 在建立租戶時，此結構化地址將被序列化為 JSON 格式存儲在數據庫中。
        /// </remarks>
        public required AddressModel legal_address { get; set; }
    }
}
