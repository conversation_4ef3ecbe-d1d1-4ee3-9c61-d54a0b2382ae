using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 創建系統使用者時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了創建新的系統使用者記錄時需要提供的必要資訊。
    /// 用於 HTTP POST 請求中，接收管理員或租戶管理員提供的使用者資料並進行驗證與處理。
    /// 符合 RESTful API 設計原則，用於 POST /v1/users 端點以創建新的系統使用者。
    /// 使用者記錄用於識別和認證存取系統的个體，如管理員、使用者和系統整合帳號等。
    /// </remarks>
    public class UsersCreateModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public UsersCreateModel()
        {
            username = string.Empty;
            email = string.Empty;
            password_hash = string.Empty;
        }

        /// <summary>
        /// 使用者所屬的租戶識別碼
        /// </summary>
        /// <remarks>
        /// 指定使用者所屬的租戶識別碼，連結到 tenants 表中的租戶記錄。
        /// 此欄位可以設置為預設值(如 Guid.Empty)來表示此使用者是系統管理員，不屬於任何租戶。
        /// 在多租戶系統中，此欄位實現了數據和操作的隊租戶難。
        /// 控制器會驗證此 ID 對應到一個有效的租戶，或驗證它是空值以創建系統管理員。
        /// </remarks>
        public Guid tenant_id { get; set; }

        /// <summary>
        /// 使用者的登入帳號名稱
        /// </summary>
        /// <remarks>
        /// 使用者的帳號名稱或識別名稱，用於系統登入和識別。
        /// 此欄位為必填項，應在租戶內保持唯一性（或在系統管理員層級保持唯一性）。
        /// 使用者名稱通常會有格式限制，如只允許英文字母、數字和某些特殊符號，控制器應進行驗證。
        /// 雖然大多數情況下使用者將通過郵件登入，但保留獨立的用戶名欄位以支援多種登入方式。
        /// </remarks>
        public required string username { get; set; }

        /// <summary>
        /// 使用者的電子郵件地址
        /// </summary>
        /// <remarks>
        /// 使用者的電子郵件地址，用於系統登入、身份驗證和通知發送。
        /// 此欄位為必填項，必須是有效的電子郵件格式，且應在整個系統中保持唯一性。
        /// 電子郵件地址通常用於發送帳戶激活鏈接、密碼重設通知和系統事件通知等。
        /// 控制器應驗證電子郵件的格式和唯一性，並可能需要執行電子郵件驗證流程。
        /// </remarks>
        public required string email { get; set; }

        /// <summary>
        /// 使用者密碼的加密雜湊值
        /// </summary>
        /// <remarks>
        /// 使用者密碼的加密雜湊值，而非原始密碼閉文。
        /// 此欄位為必填項，必須使用安全的雜湊算法（如 bcrypt、PBKDF2）生成，包含雜湊鹫和工作因結。
        /// 出於安全考慮，控制器應確保僅接受已經加密的雜湊值，而不是原始密碼，以避免在傳輸過程中暸露密碼。
        /// 在創建使用者通常會設置自動生成的臨時密碼，然後引導使用者變更或使用使用密碼重設功能。
        /// </remarks>
        public required string password_hash { get; set; }

        /// <summary>
        /// 使用者的角色識別碼
        /// </summary>
        /// <remarks>
        /// 指定使用者的系統角色，連結到角色表中的記錄，如管理員、一般使用者、唯讀用戶等。
        /// 此欄位為必填項，用於確定使用者的權限和存取的系統功能。
        /// 角色定義了使用者在系統中能現什麼操作和存取什麼資源，是基於角色的存取控制(RBAC)模型的基礎。
        /// 控制器會驗證此 ID 對應到一個有效的角色，並確保執行角色指配的使用者有關聯的權限。
        /// </remarks>
        public Guid role_id { get; set; }
    }
}
