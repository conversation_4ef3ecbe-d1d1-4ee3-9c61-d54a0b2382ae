using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// API 使用紀錄建立模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了建立 API 使用紀錄所需的資料結構。
    /// 用於記錄系統中 API 調用的相關資訊，如調用的 API、使用的金鑰、來源 IP、響應時間和狀態碼等。
    /// 這些資料用於系統監控、效能分析、計費和安全審核等目的。
    /// 此模型通常由系統自動記錄，作為 API 調用監控的一部分，以協助系統管理員追蹤和分析 API 的使用模式。
    /// </remarks>
    public class ApiUsageCreateModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public ApiUsageCreateModel()
        {
            request_ip = string.Empty;
        }

        /// <summary>
        /// 被調用的 API 的唯一識別碼
        /// </summary>
        /// <remarks>
        /// 此屬性存儲被調用的 API 的唯一識別碼，用於識別使用者存取了哪個 API 端點。
        /// 這個資訊對於追蹤 API 使用情況、分析熱門 API 和生成使用報表至關重要。
        /// </remarks>
        public Guid api_id { get; set; }

        /// <summary>
        /// 用於該 API 調用的 API 金鑰的唯一識別碼
        /// </summary>
        /// <remarks>
        /// 此屬性指定了用於驗證該 API 調用的 API 金鑰的唯一識別碼。
        /// 這個資訊可用於追蹤不同客戶端的 API 使用情況，計算每個客戶端的調用配額和進行安全審核等。
        /// </remarks>
        public Guid key_id { get; set; }

        /// <summary>
        /// API 請求的來源 IP 地址
        /// </summary>
        /// <remarks>
        /// 此屬性記錄發起 API 請求的客戶端的 IP 地址。
        /// 這個資訊用於安全分析、地理分佈追蹤和潛在潛在安全威脅的識別，如爆力式請求或異常使用模式。
        /// 應注意這個欄位可能包含個人資料，應根據適用的資料保護規定適當處理。
        /// </remarks>
        public string request_ip { get; set; }

        /// <summary>
        /// API 調用的響應時間，以毫秒為單位
        /// </summary>
        /// <remarks>
        /// 此屬性記錄處理 API 請求所需的時間，以毫秒為單位。
        /// 這個指標對於評估 API 的效能、監控系統負載和識別可能需要優化的端點至關重要。
        /// 高響應時間可能表明系統負載問題、網路延遲或數據庫效能環瓶頸等。
        /// </remarks>
        public decimal response_time { get; set; }

        /// <summary>
        /// API 調用的 HTTP 狀態碼
        /// </summary>
        /// <remarks>
        /// 此屬性記錄 API 請求的 HTTP 狀態碼，表示請求的結果。
        /// 常見的狀態碼包括：200（成功）、400（客戶端錯誤）、401（未經授權）、403（禁止存取）、404（找不到資源）、500（伺服器內部錯誤）等。
        /// 這個資訊用於追蹤 API 調用的成功率、分析常見錯誤和監控系統健康狀態。
        /// 顯著的狀態碼錯誤率可能表示需要調查的安全或功能問題。
        /// </remarks>
        public int status_code { get; set; }
    }
}
