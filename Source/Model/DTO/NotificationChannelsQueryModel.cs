using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 通知管道查詢時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型定義了查詢系統通知管道記錄時需要提供的查詢條件參數。
    /// 用於 HTTP GET 請求中，在 URL 查詢參數中接收客戶端提供的查詢條件。
    /// 此模型繼承自 BaseQueryModel，因此也包含了分頁相關的參數（Limit、Offset）。
    /// 符合 RESTful API 設計原則，用於 GET /v1/notification-channels 端點以查詢系統通知管道記錄。
    /// 查詢結果將根據提供的查詢參數進行篩選，並以分頁格式返回結果。
    /// </remarks>
    public class NotificationChannelsQueryModel : BaseQueryModel
    {
        /// <summary>
        /// 通知管道的啟用狀態
        /// </summary>
        /// <remarks>
        /// 指定要查詢的通知管道的啟用狀態。
        /// 此欄位為可選填項，可用於篩選啟用狀態為啟用或禁用的通知管道。
        /// 如果設置為 true，表示查詢所有啟用中的通知管道。
        /// 如果設置為 false，表示查詢所有已禁用的通知管道。
        /// 如果設置為 null，表示查詢所有通知管道，不考慮其啟用狀態。
        /// 此參數在管理介面中特別有用，可用於查看所有已停用的通知管道。
        /// </remarks>
        public bool? is_active { get; set; }
    }
}
