using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 更新 API 服務資訊時的數據傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型用於接收來自外部的請求，以更新現有 API 服務的資訊。
    /// 用於 HTTP PUT 請求中，在控制器層接收客戶端提供的更新項目。
    /// 所有屬性都是選填的，允許只更新部分屬性的資料。
    /// 符合 RESTful API 設計原則，用於 PUT /v1/apis/{api_id} 端點以更新現有 API 服務。
    /// </remarks>
    public class ApisUpdateModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public ApisUpdateModel()
        {
            name = string.Empty;
            version = string.Empty;
        }

        /// <summary>
        /// API 服務的名稱
        /// </summary>
        /// <remarks>
        /// API 服務的顯示名稱，用於界面顯示和識別。
        /// 當提供此屬性時，控制器將更新 API 服務的名稱。
        /// 應簡明且清楚地描述 API 服務的用途或功能，如「用戶認證 API」、「支付網關 API」等。
        /// 若不提供此屬性，現有的 API 名稱將保持不變。
        /// </remarks>
        public string name { get; set; }

        /// <summary>
        /// API 服務的版本號
        /// </summary>
        /// <remarks>
        /// API 服務的版本號，用於識別不同版本的 API 實現。
        /// 當提供此屬性時，控制器將更新 API 服務的版本號。
        /// 建議使用語意化版本編號（SemVer）格式，如 1.0.0、2.1.3 等，以區分不同程度的變更。
        /// 當更新版本號時，應確保與其它相關系統和文檔保持同步。
        /// 若不提供此屬性，現有的版本號將保持不變。
        /// </remarks>
        public string version { get; set; }

        /// <summary>
        /// API 每分鐘的請求限制數
        /// </summary>
        /// <remarks>
        /// 每分鐘允許的最大 API 請求數量，用於限制和管理 API 的使用率。
        /// 當提供此屬性時，控制器將更新 API 服務的請求限制。
        /// 必須是正整數，建議基於 API 資源負荷和服務層級設定合理的值。
        /// 變更限制將立即生效，可能會影響現有的已評估或規劃的 API 使用氣場。
        /// </remarks>
        public int rate_limit_per_minute { get; set; }

        /// <summary>
        /// API 服務的啟用狀態
        /// </summary>
        /// <remarks>
        /// 指示 API 服務是否處於啟用狀態的布林值。
        /// 當設置為 true 時，表示 API 是活躍的，客戶端可以進行請求。
        /// 當設置為 false 時，表示 API 被停用，客戶端的請求將被拒絕處理。
        /// 此狀態的變更將影響所有連線到此 API 服務的客戶端，應謹慎考慮更改的影響。
        /// </remarks>
        public bool is_active { get; set; }
    }
}
