using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 更新通知管道資訊時的資料傳輸模型
    /// </summary>
    /// <remarks>
    /// 此模型用於接收來自外部的請求，以更新現有通知管道的資訊。
    /// 用於 HTTP PUT 請求中，在控制器層接收管理員或租戶管理員提供的更新項目。
    /// 所有屬性都是可選填的，允許只更新部分屬性的資料。
    /// 符合 RESTful API 設計原則，用於 PUT /v1/notification-channels/{channel_id} 端點以更新現有通知管道。
    /// 通知管道更新後，變更將立即影響系統發送通知的方式和目標。
    /// </remarks>
    public class NotificationChannelsUpdateModel
    {
        /// <summary>
        /// 通知管道的唯一識別碼
        /// </summary>
        /// <remarks>
        /// 要更新的通知管道的唯一 ID，用於進行管道的指定更新。
        /// 此欄位將在請求的網址路徑中提供，而非此模型中的資料內容。
        /// 控制器會驗證此 ID 對應到一個有效的存在通知管道記錄。
        /// 由於通知管道可能正在被系統使用，更新應謹慎監控，以確保通知功能的持續可用。
        /// </remarks>
        public Guid? channel_id { get; set; }

        /// <summary>
        /// 通知管道的詳細設定
        /// </summary>
        /// <remarks>
        /// 更新通知管道的細節配置資訊，如服務器設定、軟體密鑰、樣板設定等。
        /// 此欄位為可選填項，使用 NotificationChannelsConfig 物件存儲結構化的設定資訊。
        /// 更新後的設定必須符合通知管道類型（type）的需求，如電子郵件需要 SMTP 主機和帳號資訊。
        /// 所有敏感資訊（如密碼、API 密鑰）應適當加密存儲，控制器應處理並驗證配置符合安全標準。
        /// 若不提供此屬性，現有的通知管道設定將保持不變。
        /// </remarks>
        public NotificationChannelsConfig? config { get; set; }

        /// <summary>
        /// 通知管道的啟用狀態
        /// </summary>
        /// <remarks>
        /// 指示此通知管道是否處於啟用狀態的布林值。
        /// 此欄位為可選填項，用於控制通知管道的啟用或禁用。
        /// 當設置為 true 時，管道將被啟用，系統會使用此管道發送通知。
        /// 當設置為 false 時，管道將被停用，系統不會使用此管道發送通知。
        /// 若不提供此屬性，現有的啟用狀態設定將保持不變。
        /// 啟用狀態的變更將影響系統發送通知的方式跟空氣，變更前應先確定有其他可用的通知管道。
        /// </remarks>
        public bool? is_active { get; set; }
    }
}
