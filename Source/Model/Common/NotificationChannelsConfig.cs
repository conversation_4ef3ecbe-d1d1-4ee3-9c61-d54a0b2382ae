using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 通知箤道設定
    /// </summary>
    /// <remarks>
    /// 此類別定義了系統發送通知的箤道設定，如電子郵件伺服器、簡訊服務等的配置參數。
    /// 主要用於定義如何將通知發送給使用者，特別是系統生成的通知，如帳單提醒、安全警報等。
    /// 每種通知箤道可能有其特定的設定參數，目前此類別的屬性對應電子郵件通知箤道的設定。
    /// 在系統擴展時，此類別可能會新增其他箤道的配置項，如推播通知、Webhook 等。
    /// </remarks>
    public class NotificationChannelsConfig
    {
        /// <summary>
        /// SMTP 伺服器主機名稱或 IP 地址
        /// </summary>
        /// <remarks>
        /// 指定用於發送電子郵件通知的 SMTP 伺服器主機名稱或 IP 地址。
        /// 範例包括："smtp.gmail.com"、"smtp.office365.com" 或主機 IP 地址如 "*************"。
        /// 此設定對於電子郵件通知箱道至關重要，無法發送通知郵件。
        /// 在環境配置文件中應設定此值，以適應不同的開發、測試和生產環境。
        /// </remarks>
        public required string smtp_host { get; set; }
        /// <summary>
        /// SMTP 伺服器端口
        /// </summary>
        /// <remarks>
        /// 指定用於連線至 SMTP 伺服器的網路端口號。
        /// 標準 SMTP 端口為 25，但現代郵件系統通常使用加密的 587（STARTTLS）或 465（SSL/TLS）端口。
        /// 根據伺服器設定和企業網路政策，可能需要適當設置此值。
        /// 此設定與 smtp_host 一起決定了系統能否成功連線至郵件伺服器。
        /// </remarks>
        public int port { get; set; }
    }
}
