using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// API 存取範圍與權限設定
    /// </summary>
    /// <remarks>
    /// 此類別定義了 API 金鑰的存取權限範圍，用於授權控制和存取管理。
    /// 範圍指定了 API 金鑰可以執行的操作類型，如讀取、寫入等。
    /// 系統使用此類別實現細精度的存取控制，依照最小權限原則授予用戶所需的最少權限。
    /// 在 API 金鑰認證流程中，此類別定義的權限將決定請求者可以存取的端點和操作。
    /// </remarks>
    public class Scopes
    {
        /// <summary>
        /// 讀取權限
        /// </summary>
        /// <remarks>
        /// 指定 API 金鑰是否具有讀取資源的權限。
        /// 當設置為 true 時，此 API 金鑰可以存取 GET 型端點和查詢操作。
        /// 讀取權限通常用於資料擷取、報表生成和檢視操作等。
        /// 影響 HTTP GET、HEAD 和 OPTIONS 請求的授權決策。
        /// </remarks>
        public bool read { get; set; }
        /// <summary>
        /// 寫入權限
        /// </summary>
        /// <remarks>
        /// 指定 API 金鑰是否具有寫入或修改資源的權限。
        /// 當設置為 true 時，此 API 金鑰可以存取 POST、PUT、PATCH 和 DELETE 等修改型端點。
        /// 寫入權限通常使用於創建、更新和刪除操作，是一種更高級別的權限。
        /// 由於寫入操作有更高的安全風險，因此在授予此權限時應更加謹慎。
        /// </remarks>
        public bool write { get; set; }
    }
}
