using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 基礎查詢模型類別
    /// </summary>
    /// <remarks>
    /// 此類別為所有查詢相關模型的基礎類別，提供常用的查詢參數如分頁參數和資料限制。
    /// 為 HTTP 查詢參數提供一致的結構，美化控制器中的參數處理。
    /// 在適用 RESTful API 設計原則時，此類別確保系統全面支援分頁和適當的資源限制。
    /// 它可視為使用者請求的強類型容器，顯示性較 BaseModel 更為專注於請求參數。
    /// </remarks>
    public class BaseQueryModel
    {
        /// <summary>
        /// 查詢結果回傳的最大筆數限制
        /// </summary>
        /// <remarks>
        /// 此參數控制每次查詢返回的資料最大數量，為實現分頁功能的關鍵參數。
        /// 預設值為 10，表示查詢默認每頁僅返回 10 筆資料。
        /// 完全容帶自定義，客戶端可透過查詢參數指定其值，如 ?limit=20。
        /// 對於效能高度重要，讓客戶端及伺服器可以平衡資料傳輸量與請求時間。
        /// </remarks>
        public int Limit { get; set; } = 10;
        /// <summary>
        /// 查詢資料的跳過筆數
        /// </summary>
        /// <remarks>
        /// 此參數指定查詢時要略過的資料筆數，用於分頁查詢中計算起始位置。
        /// 預設值為 0，表示從資料集的開首開始查詢。
        /// 完全可自定義，客戶端可透過 URL 參數提供，如 ?offset=20 表示跳過前 20 筆資料。
        /// 配合 Limit 參數使用可實現完整的分頁導覽功能，如「從第 21 筆開始顯示 10 筆資料」。
        /// </remarks>
        public int Offset { get; set; } = 0;
    }
}
