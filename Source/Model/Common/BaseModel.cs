using Paas.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 基礎模型類別
    /// </summary>
    /// <remarks>
    /// 此類別為所有模型的基礎類別，提供通用的特性如分頁參數、時間戳記和基本資料映射管理。
    /// 透過讓其他模型繼承此基礎類別，可確保所有模型均維持一致的數據結構和操作要求。
    /// 這為系統實作了統一的查詢後端介面，一致的授權邏輯和數據標準化。
    /// </remarks>
    public class BaseModel
    {
        /// <summary>
        /// 查詢結果回傳的最大筆數限制
        /// </summary>
        /// <remarks>
        /// 此屬性用於限制每次查詢中返回的結果數量，為查詢分頁參數的一部分。
        /// 預設值為 10，表示若不指定則每頁僅返回 10 筆資料。
        /// 這有助於避免大量資料操作導致的效能問題，並支援前端的分頁展示。
        /// 可由客戶端參數覆寫，例如設定為 20、50 等值。
        /// </remarks>
        public int Limit { get; set; } = 10;
        /// <summary>
        /// 查詢資料的跳過筆數
        /// </summary>
        /// <remarks>
        /// 此屬性指定查詢時要跳過的資料筆數，為查詢分頁參數的另一部分。
        /// 預設值為 0，表示從第一筆資料開始查詢。
        /// 用於實現分頁功能，例如將 Offset 設為 10 代表第二頁（假設 Limit 為 10）。
        /// 結合 Limit 和 Offset 的使用可實現客戶端的「上一頁」和「下一頁」功能。
        /// </remarks>
        public int Offset { get; set; } = 0;

        /// <summary>
        /// 資料創建時間字串表示
        /// </summary>
        /// <remarks>
        /// 資料列的創建時間，以格式化字串形式返回。
        /// 透過導出為字串屬性，提供了一個易於在 API 回應中使用的標準格式時間表示。
        /// 使用 ToDateStr() 擴展方法將 DateTimeOffset 轉換為標準格式字串，確保一致的時間格式。
        /// 於 JSON 序列化過程中，此屬性會顯示得更為友善，如 "2025-01-01T00:00:00Z" 格式。
        /// </remarks>
        public string created_at => created_at_dt.ToDateStr();
        /// <summary>
        /// 資料更新時間字串表示
        /// </summary>
        /// <remarks>
        /// 資料列的最後更新時間，以格式化字串形式返回。
        /// 可用於追蹤資料的修改歷史，放入 API 回應中為客戶端提供更新時間。
        /// 如同 created_at，透過 ToDateStr() 擴展方法確保時間格式的一致性和可讀性。
        /// 在資料版本管理和綜合資料修改的時間戳記上給予關鍵支援。
        /// </remarks>
        public string updated_at => updated_at_dt.ToDateStr();

        /// <summary>
        /// 資料創建時間完整值
        /// </summary>
        /// <remarks>
        /// 資料列的創建時間，以 DateTimeOffset 形式存儲，包含時區資訊。
        /// 使用 DateTimeOffset 而非 DateTime 確保了跨時區的時間計算準確性，重要對跨地域系統。
        /// 此屬性保存原始完整的時間數據，允許進行時間比較、差別計算和各種時間運算。
        /// 當需要精確操作時間數據（如計算持續時間或時間範圍）時，應使用此屬性而非字串表示形式。
        /// </remarks>
        public DateTimeOffset created_at_dt { get; set; }
        /// <summary>
        /// 資料更新時間完整值
        /// </summary>
        /// <remarks>
        /// 資料列的最後更新時間，以 DateTimeOffset 形式存儲，包含時區資訊。
        /// 此屬性會在每次資料更新時自動更新，使得可以追蹤資料的更改歷史。
        /// 對於稍後修改 (Last-Modified) 存取策略和數據版本控制極其重要，特別是在多用戶協同環境中。
        /// 此屬性可用於時序性排序，如「最近更新」的資料先顯示，以及實現跨端的資料同步。
        /// </remarks>
        public DateTimeOffset updated_at_dt { get; set; }
    }
}
