using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 安全性日誌事件詳細資訊
    /// </summary>
    /// <remarks>
    /// 此類別用於描述安全性日誌事件的詳細資訊，如安全事件的行為、原因和其他相關詳情。
    /// 系統的安全日誌功能會記錄不同類型的安全事件，如登入失敗、權限變更、API 金鑰管理等。
    /// 此物件模型可以以 JSON 格式存儲在安全日誌表中，也可作為輸出到第三方安全監控系統的模型。
    /// 事件詳細資訊對於安全審計、問題診斷和法規遵循報告等方面非常重要。
    /// </remarks>
    public class SecurityLogsDetails
    {
        /// <summary>
        /// 安全事件的行為簡述
        /// </summary>
        /// <remarks>
        /// 指定發生安全事件時的行為類型或操作。
        /// 常見的行為包括："login"、"logout"、"create_api_key"、"revoke_api_key"、"change_permission" 等。
        /// 此欄位對於安全審計分類和統計分析相當重要，可用於生成各類型行為的週期性報表。
        /// 系統可能會強制限制為一組預定義的行為值，以確保分類標準化和報表清晰度。
        /// </remarks>
        public required string action { get; set; }
        /// <summary>
        /// 安全事件的詳細原因
        /// </summary>
        /// <remarks>
        /// 描述安全事件發生的詳細原因或附加資訊。
        /// 如登入失敗的原因可能是「密碼錯誤」、「帳號鎖定」或「無效的兩因素驗證碼」等。
        /// 此欄位存儲更詳細的事件說明，可能包含技術細節、錯誤訊息或其他診斷資訊。
        /// 在安全審計報表中，此欄位的資訊可提供更具體的分析和判斷依據。
        /// 特別重要的是，此欄位不應記錄敏感的個人資訊，如密碼、測試密碼等。
        /// </remarks>
        public required string reason { get; set; }
    }
}
