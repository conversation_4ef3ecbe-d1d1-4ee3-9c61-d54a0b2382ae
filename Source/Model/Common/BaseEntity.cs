using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 基礎實體類別
    /// </summary>
    /// <remarks>
    /// 此類別為所有資料庫實體的基礎類別，提供共用的屬性如時間戳記。
    /// 透過讓其他實體類別繼承此基礎類別，可確保所有實體皆維持一致的資料結構和審計屬性。
    /// 此基礎類別為資料庫實體程式碰定義了必要的約束，使得在資料處理、審計和追蹤變更方面保持一致的方法。
    /// 在使用 ORM 框架時，此類別的繼承者會被直接映射到資料庫表，其屬性對應表中的欄位。
    /// </remarks>
    public  class BaseEntity
    {
        /// <summary>
        /// 資料創建時間
        /// </summary>
        /// <remarks>
        /// 記錄資料列的初始創建時間，使用 DateTimeOffset 型別以保留時區信息。
        /// 此屬性在資料首次加入到資料庫時自動設置，且在存儲後不應再修改。
        /// 在資料庫結構中，通常實作為不可更新的資料欄位，可能會建立索引以加速時間相關的查詢。
        /// 此屬性在記錄資料的完整生命週期、審計資料變更以及管理資料維護上有重要作用。
        /// </remarks>
        public DateTimeOffset created_at { get; set; }

        /// <summary>
        /// 資料更新時間
        /// </summary>
        /// <remarks>
        /// 記錄資料列最後一次的修改時間，使用 DateTimeOffset 型別以保留時區信息。
        /// 此屬性在資料創建時初始設置為與 created_at 相同的值，並在每次修改資料後自動更新為當前時間。
        /// 在資料庫層面，通常透過觸發器或 ORM 框架的自動機制確保此欄位的正確更新。
        /// 此屬性對於資料同步、版本控制和衝突解決非常重要，特別是在多用戶環境或分佈式系統中。
        /// 也可用於訂閱、掃描通知和「從上次諮詢之後的變更」等功能實現。
        /// </remarks>
        public DateTimeOffset updated_at { get; set; }
    }
}
