using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 地址模型類別
    /// </summary>
    /// <remarks>
    /// 此類別用於表示結構化的地址資訊，適用於租戶、用戶或其他需要地址資訊的實體。
    /// 地址模型可作為獨立屬性或以 JSON 格式存儲在其他模型中。
    /// 對於跨國家和地區的地址格式具有彈性，可適應不同地址標準。
    /// 常用於將該結構引用為法律文件、帳單和收賬文件等場景中的法律地址。
    /// </remarks>
    public class AddressModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public AddressModel()
        {
            street = string.Empty;
            city = string.Empty;
            country = string.Empty;
            postalCode = string.Empty;
        }

        /// <summary>
        /// 街道地址
        /// </summary>
        /// <remarks>
        /// 地址的街道或道路部分，包含街道名稱、道路編號、大樓名稱等詳細資訊。
        /// 例如：民生東路三段123號5樓、信義區松仁路100號等。
        /// 這是地址的最詳細部分，通常用於精確取得處所位置。
        /// 可能會因不同國家和地區而有不同的格式與表達方式。
        /// </remarks>
        public required string street { get; set; }

        /// <summary>
        /// 城市或南鄉鄉鎮
        /// </summary>
        /// <remarks>
        /// 地址所屬的城市、縣市或鄉鎮名稱。
        /// 在台灣地區可能是「台北市」、「新北市」、「臺中市」等。
        /// 此欄位與 country 和 postalCode 結合使用可指定完整的地理位置。
        /// 在國際地址中，此項對郵件分類和送達至關重要。
        /// </remarks>
        public required string city { get; set; }

        /// <summary>
        /// 國家或地區
        /// </summary>
        /// <remarks>
        /// 地址所屬的國家或地區名稱。
        /// 建議使用國家的標準代碼，如 ISO 3166-1 alpha-2 的二字母國家代碼（如 TW、US、JP 等）。
        /// 這有助於後端系統使用正確的國家信息進行分類和運算。
        /// 在多國家系統中，此欄位可能會控制更多的業務邏輯，如貨幣、稅率等。
        /// </remarks>
        public required string country { get; set; }

        /// <summary>
        /// 郵遞區號
        /// </summary>
        /// <remarks>
        /// 地址所屬的郵遞編碼或郵遞區號。
        /// 形式因國家而異，如台灣為 3-6 位數字（如 104、105、106），美國為 5 位數字（如 90210）。
        /// 保存為字串類型而非整數，因為某些國家的郵遞編碼可能包含字母或特殊字元。
        /// 用於郵件配送、地址驗證和地理分析等用途。
        /// </remarks>
        public required string postalCode { get; set; }
    }
}
