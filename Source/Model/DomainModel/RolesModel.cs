using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 角色資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸系統中角色的相關資訊，包含角色名稱、描述與唯一識別碼等。
    /// 角色模型是角色型存取控制（RBAC）架構的核心，定義了不同角色的權限集合。
    /// 此模型常用於用戶管理、權限設定與審計報表等場景。
    /// </remarks>
    public class RolesModel : BaseModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public RolesModel()
        {
            name = string.Empty;
            description = string.Empty;
        }

        /// <summary>
        /// 角色唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為角色的主鍵和對外識別用途。
        /// 此值在創建角色時自動指定，且在角色生命週期內保持不變。
        /// 在權限分配、用戶角色關聯與審計報表中皆會用到此識別碼。
        /// </remarks>
        public Guid role_id { get; set; }

        /// <summary>
        /// 角色名稱
        /// </summary>
        /// <remarks>
        /// 角色的顯示名稱，用於系統管理介面、權限設定及用戶角色分配。
        /// 建議名稱具描述性，如「管理員」、「審核者」、「一般用戶」等。
        /// 此名稱應具有唯一性，以避免混淆。
        /// </remarks>
        public string name { get; set; }

        /// <summary>
        /// 角色描述
        /// </summary>
        /// <remarks>
        /// 用於說明角色的用途、權限範圍或其他補充資訊。
        /// 方便系統管理員快速了解角色設計意圖與適用情境。
        /// 此欄位可用於顯示於管理後台、權限審查或自動化文件產生。
        /// </remarks>
        public string description { get; set; }
    }
}
