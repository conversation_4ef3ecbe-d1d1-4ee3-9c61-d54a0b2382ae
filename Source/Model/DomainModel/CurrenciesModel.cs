using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 幣別資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸系統支援的貨幣幣別相關資訊，包含幣別代碼、符號、匯率和啟用狀態等。
    /// 經由繼承 BaseModel，此模型支援基礎查詢功能如分頁和排序。
    /// 幣別資訊在多貨幣系統中的重要性高，尤其對商業記錄、帳單和费率計算等生成及處理操作。
    /// 常用於租戶設定、統一計價和多幣別報表等場景。
    /// </remarks>
    public class CurrenciesModel : BaseModel
    {
        /// <summary>
        /// 幣別唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為幣別的主鍵和對外識別用途。
        /// 此值在創建幣別時自動指定，且在整個生命週期內保持不變。
        /// 多用於引用和外鍵關聯，如租戶設定預設幣別、交易等場景。
        /// 使用 UUID 而非幣別代碼作主鍵，可將系統外部標準（如ISO 4217）與內部資料結構隔離。
        /// </remarks>
        public Guid currency_id { get; set; }
        /// <summary>
        /// 幣別代碼
        /// </summary>
        /// <remarks>
        /// 幣別的標準代碼，通常遵循 ISO 4217 標準，如 TWD（新台幣）、USD（美元）、EUR（歐元）等。
        /// 此字段必須提供（required），並應具獨特性，以確保系統中幣別沒有重複。
        /// 在實際使用中，此代碼在網站前端、API 介面和報表中廣泛顯示，如貨幣選擇器、交易記錄等。
        /// 通常為三個大寫字母，並應使用一致的大小寫格式（全大寫推薦）。
        /// </remarks>
        public required string code { get; set; }

        /// <summary>
        /// 幣別符號
        /// </summary>
        /// <remarks>
        /// 貨幣的簡擲符號，如 $（美元）、€（歐元）、¥（日元）、NT$（新台幣）等。
        /// 此字段必須提供（required），用於在系統客戶端顯示貨幣金額時使用。
        /// 在前端顯示中，符號通常用於可視化貨幣金額，如價格標簽、帳單總額等。
        /// 由於不同地區符號使用位置不同（如前置或後置），顯示格式應在前端適當處理。
        /// </remarks>
        public required string symbol { get; set; }

        /// <summary>
        /// 匯率
        /// </summary>
        /// <remarks>
        /// 此貨幣相對於系統基準貨幣的匯率值。
        /// 使用 decimal 類型以確保高精度計算，避免浮點數精度問題。
        /// 此字段必須提供（required），用於貨幣換算和多幣別報表生成。
        /// 系統可能需要定期更新此值，以反映國際匯率市場的變化。
        /// </remarks>
        public required decimal exchange_rate { get; set; }

        /// <summary>
        /// 幣別啟用狀態
        /// </summary>
        /// <remarks>
        /// 指示此幣別在系統中是否可用。
        /// 此字段必須提供（required），預設值應考慮系統需求。
        /// 當值為 true 時，此幣別可用於交易、租戶設定和其他貨幣相關操作。
        /// 當值為 false 時，此幣別將不顯示於選擇清單中，並可能限制新的交易或設定使用此幣別。
        /// </remarks>
        public required bool is_active { get; set; }
        
    }
}
