using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 監控指標資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸系統性能和資源使用的監控指標數據，包含指標的類型、數值和時間戳等。
    /// 經由繼承 BaseModel，此模型支援基礎查詢功能如分頁和排序。
    /// 監控指標數據在系統健康度監控、效能分析和資源規劃上擁有关鍵作用。
    /// 常用於平台性能儀錶板、警報系統、統計報表和容量規劃等場景。
    /// </remarks>
    public class MonitoringMetricsModel : BaseModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public MonitoringMetricsModel()
        {
            tags = string.Empty;
        }

        /// <summary>
        /// 指標數據唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為監控指標數據項的主鍵和識別用途。
        /// 此值在創建指標記錄時自動指定，且在整個生命週期內保持不變。
        /// 用於指標數據的唯一識別、查詢和對外參照。
        /// 此識別碼簡化了數據儲存、查詢和更新的操作。
        /// </remarks>
        public Guid data_id { get; set; }

        /// <summary>
        /// 指標類型識別碼
        /// </summary>
        /// <remarks>
        /// 指定此數據所屬的監控指標類型，為外鍵指向指標類型定義表。
        /// 常見的指標類型包括：CPU 使用率、內存使用率、API 請求率、韌性時間等。
        /// 可空屬性允許存在不綁定到特定類型的數據，或查詢時不限定類型。
        /// 根據不同的指標類型，系統可能會應用不同的分析算法和警報閾值。
        /// </remarks>
        public Guid? metric_type_id { get; set; }

        /// <summary>
        /// 租戶識別碼
        /// </summary>
        /// <remarks>
        /// 指定此數據相關的租戶，為外鍵指向 Tenants 表的 tenant_id。
        /// 若為 null，表示此數據為系統層級的指標，而非特定租戶的數據。
        /// 用於對監控數據進行租戶隔離和權限控制，確保平台管理員可查看所有數據，而租戶管理員僅能變視其租戶的數據。
        /// 在生成分析報表和儀錶板時，此屬性允許依租戶分組和篩選數據。
        /// </remarks>
        public Guid? tenant_id { get; set; }

        /// <summary>
        /// API識別碼
        /// </summary>
        /// <remarks>
        /// 指定此數據相關的 API 服務，為外鍵指向 Apis 表的 api_id。
        /// 若為 null，表示此數據不特定關聯任何 API，或為非 API 相關的系統指標。
        /// 用於 API 服務的效能分析、負載監控和健康狀態判斷。
        /// 此資訊結合 tenant_id 可用於產生查看特定租戶的特定 API 使用效能分析報表。
        /// </remarks>
        public Guid? api_id { get; set; }

        /// <summary>
        /// 指標值
        /// </summary>
        /// <remarks>
        /// 記錄監控指標的數值，使用 decimal 類型以支援高精度的數值。
        /// 整型指標（如請求計數）和浮點數指標（如 CPU 使用率百分比）均可使用此欄位存儲。
        /// 系統會根據指標類型來正確解釋和展示該值，如百分比、毫秒或件數等。
        /// 通常用於變化趨勢圖表的繪製、警報閾值的判斷和統計分析計算。
        /// </remarks>
        public decimal value { get; set; }

        /// <summary>
        /// JSON格式的標籤資訊
        /// </summary>
        /// <remarks>
        /// 存儲指標的附加標籤資訊，以 JSON 字符串形式表示。
        /// 標籤可用於提供更多詳細的分類和分組資訊，如環境類型（production、staging）、服務器實例、地理區域等。
        /// JSON 格式允許彈性和擴展性，可存儲复杂的結構化標籤數據。
        /// 常見格式如：{"environment": "production", "region": "asia-east1", "instance": "worker-01"}
        /// </remarks>
        public string tags { get; set; }

        /// <summary>
        /// 開始時間
        /// </summary>
        /// <remarks>
        /// 指標記錄的開始時間，使用 DateTimeOffset 類型表示，包含時區資訊。
        /// 彈醤設計可支援不同類型的指標：點數據（特定時間點的數據）或期間數據（在時間範圍內的山章值）。
        /// 對於期間數據，此屬性與 end_time 一起定義時間範圍。
        /// 可空屬性允許表示點數據，其中僅 timestamp 屬性指定精準的時間點。
        /// </remarks>
        public DateTimeOffset? start_time { get; set; }
        /// <summary>
        /// 結束時間
        /// </summary>
        /// <remarks>
        /// 指標記錄的結束時間，使用 DateTimeOffset 類型表示，包含時區資訊。
        /// 與 start_time 配合使用，定義期間數據的時間範圍，例如「此期間內的平均 CPU 使用率」。
        /// 可空屬性允許表示點數據，其中僅 timestamp 屬性指定精準的時間點。
        /// 在查詢時，此屬性也可用於指定要查詢的指標數據的時間範圍。
        /// </remarks>
        public DateTimeOffset? end_time { get; set; }


        /// <summary>
        /// 資料時間戳記
        /// </summary>
        /// <remarks>
        /// 指標數據的精準時間戳記，使用 DateTimeOffset 類型表示，包含時區資訊。
        /// 對於點數據，此屬性當指指標數據的確切時間點。
        /// 對於期間數據，此屬性可表示數據收集或計算完成的時間。
        /// 對時間序列數據非常重要，用於排序、統計分析和時間趨勢的圖表生成。
        /// 使用 DateTimeOffset 而非 DateTime 可確保跨時區的時間比較正確性，特別是在跨地理區域部署的分散式系統中。
        /// </remarks>
        public DateTimeOffset timestamp { get; set; }
    }
}
