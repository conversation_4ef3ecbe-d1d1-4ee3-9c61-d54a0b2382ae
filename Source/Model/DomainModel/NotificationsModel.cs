using Newtonsoft.Json;
using Paas.Common;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 通知資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸系統通知的相關資訊，包含通知的基本資料、狀態、優先級和有效期等。
    /// 經由繼承 BaseModel，此模型支援基礎查詢功能如分頁和排序。
    /// 此模型起著連結租戶、用戶和通知類型的關鍵作用，確保通知都能正確地發送給適當的接收者。
    /// 常用於系統通知、事件提醒、警報通知和用戶互動等場景。
    /// </remarks>
    public class NotificationsModel : BaseModel
    {
        /// <summary>
        /// 通知唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為通知的主鍵和對外識別用途。
        /// 此值在創建通知時自動指定，且在通知生命週期內保持不變。
        /// 用於通知的追蹤、查詢、更新狀態和與其他系統整合等場景。
        /// </remarks>
        public Guid notification_id { get; set; }
        /// <summary>
        /// 通知類型識別碼
        /// </summary>
        /// <remarks>
        /// 指定通知的類型，為外鍵指向通知類型表。
        /// 不同的通知類型可能有不同的題面、內容模板和過期規則。
        /// 此屬性允許在不同情境下根據類型自動化生成通知。
        /// 作為可空值（nullable）允許存在不綁定到特定類型的通知。
        /// </remarks>
        public Guid? type_id { get; set; }
        /// <summary>
        /// 租戶識別碼
        /// </summary>
        /// <remarks>
        /// 指定通知所屬的租戶識別碼，為外鍵指向 Tenants 表的 tenant_id。
        /// 若為 null，可能表示此通知是全域系統通知，適用於所有租戶。
        /// 在多租戶架構下用於通知的級別分離和權限限制。
        /// 此屬性與 user_id 結合使用可清楚定義通知的接收範圍。
        /// </remarks>
        public Guid? tenant_id { get; set; }
        /// <summary>
        /// 使用者識別碼
        /// </summary>
        /// <remarks>
        /// 指定通知的目標使用者，為外鍵指向 Users 表的 user_id。
        /// 若為 null，可能表示此通知是對租戶內所有用戶的通知，或是全域通知。
        /// 用於權限控制和通知的投遠處理，確保只有指定的用戶能看到相關通知。
        /// 經常與 tenant_id 結合使用，以定義通知的接收范圍。
        /// </remarks>
        public Guid? user_id { get; set; }
        /// <summary>
        /// 通知標題
        /// </summary>
        /// <remarks>
        /// 通知的標題或主題，用於簡要說明通知的目的或內容。
        /// 在用戶介面上通常以醒目的方式展示，幫助用戶快速理解通知的重要性和性質。
        /// 可空屬性允許某些通知可能僅有內容而無標題。
        /// </remarks>
        public string? title { get; set; }
        /// <summary>
        /// 通知內容
        /// </summary>
        /// <remarks>
        /// 通知的詳細內容或主要訊息。
        /// 可能包含細節說明、指導性資訊或需要用戶注意的議題。
        /// 此字段可支援純文字、HTML 格式或其他標記語言，依系統設計而定。
        /// 可空屬性表示某些通知可能只有標題和其他訊息，而無需詳細內容。
        /// </remarks>
        public string? message { get; set; }
        /// <summary>
        /// JSON格式的事件詳細資訊
        /// </summary>
        /// <remarks>
        /// 存儲通知相關的事件詳細資訊，以 JSON 字串格式存儲。
        /// 可能包含事件類型、觸發條件、關聯數據等附加資訊。
        /// 當需要在客戶端呈現更詳細的通知資訊時，可直接幫助渲染複雜的互動式通知。
        /// JSON 的結構將根據通知類型而有所不同，需要在程式中適當解析。
        /// </remarks>
        public string? status { get; set; }
        /// <summary>
        /// 通知優先級
        /// </summary>
        /// <remarks>
        /// 指定通知的重要性或緊急程度，影響展示順序及提醒方式。
        /// 常見值包括："high"、"medium"、"low" 或數字式優先級別。
        /// 高優先級通知可能會以特殊形式呈現（如鍵亮顏色、置頂展示）或觸發更積極的提醒機制。
        /// 此屬性師助用戶關注最需要關注的通知，提升用戶體驗。
        /// </remarks>
        public string? priority { get; set; }
        /// <summary>
        /// 通知過期時間(DateTime)
        /// </summary>
        /// <remarks>
        /// 指定通知的過期時間，以 DateTimeOffset 格式存儲，包含時區資訊。
        /// 系統可能會根據此時間自動處理或移除過期通知。
        /// 在介面上可能顯示倒計時或過期時間，幫助用戶了解通知的有效性。
        /// 使用 DateTimeOffset 而非 DateTime 可確保時區處理正確，适用於跨區域部署的系統。
        /// </remarks>
        public DateTimeOffset expires_at_dt { get; set; }
        /// <summary>
        /// 通知過期時間(顯示格式)
        /// </summary>
        /// <remarks>
        /// 計算屬性，將 expires_at_dt 轉換為格式化的字串呈現方式。
        /// 使用 ToDateStr() 操作確保日期格式一致性並根據系統設定適當顯示。
        /// 主要用於前端接口和使用者介面展示，方便用戶閱讀。
        /// 可空屬性以支援無過期時間的通知情境。
        /// </remarks>
        public string? expires_at => expires_at_dt.ToDateStr();
    }
}
