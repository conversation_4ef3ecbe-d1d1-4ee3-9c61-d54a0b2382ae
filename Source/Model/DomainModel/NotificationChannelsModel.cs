using Newtonsoft.Json;
using Paas.Common;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 通知管道資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸系統通知管道的相關資訊，包含管道類型、配置和狀態等。
    /// 經由繼承 BaseModel，此模型支援基礎查詢功能如分頁和排序。
    /// 通知管道定義了系統如何將通知發送給使用者，例如透過電子郵件、簡訊或應用程式內通知等。
    /// 常用於通知設定、系統警報和用戶自定義通知偏好等場景。
    /// </remarks>
    public class NotificationChannelsModel : BaseModel
    {
        /// <summary>
        /// 通知管道唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為通知管道的主鍵和對外識別用途。
        /// 此值在創建通知管道時自動指定，且在整個生命週期內保持不變。
        /// 可空屬性允許在創建新管道時暂時不指定該值，由系統自動生成。
        /// 在取得或查詢現有管道時，此值項將被填充。
        /// </remarks>
        public Guid? channel_id { get; set; }
        /// <summary>
        /// 管道類型
        /// </summary>
        /// <remarks>
        /// 指定通知管道的類型，如「email」（電子郵件）、「sms」（簡訊）、「push」（推播通知）、「webhook」等。
        /// 此類型決定了管道的配置須包含哪些參數，以及系統如何處理單向此管道的通知。
        /// 可空屬性允許呈現未設定類型的管道，或在查詢時不限定類型。
        /// 系統應設有一組預定義的有效類型，並在創建時驗證傳入的類型值是否有效。
        /// </remarks>
        public string? type { get; set; }
        /// <summary>
        /// 管道設定
        /// </summary>
        /// <remarks>
        /// 存儲管道的詳細配置參數，使用 NotificationChannelsConfig 類型封裝。
        /// 依據管道類型的不同，此設定可能包含不同的參數。例如，電子郵件管道會包含 SMTP 伺服器和端口設定。
        /// 可空屬性允許在創建或查詢管道時不提供配置詳細資訊。
        /// 此管道配置資訊應該安全正當地存儲，因為可能包含敞感的認證資訊，如郵件伺服器密碼或 API 金鑰。
        /// </remarks>
        public NotificationChannelsConfig? config { get; set; }
        /// <summary>
        /// 啟用狀態
        /// </summary>
        /// <remarks>
        /// 指示此通知管道是否目前啟用中。
        /// 當值為 true 時，表示管道正常啟用，可用於發送通知。
        /// 當值為 false 時，表示管道已停用，系統不會使用此管道發送通知。
        /// 可空屬性允許存在尚未設定啟用狀態的管道，或在查詢時不限定啟用狀態。
        /// 此狀態可用於臨時停用特定管道，而無需刪除其配置，例如當郵件伺服器需要維護時。
        /// 注意：系統應確保至少有一個啟用中的通知管道，以免系統失去發送重要通知的能力。
        /// </remarks>
        public bool? is_active { get; set; }
    }
}
