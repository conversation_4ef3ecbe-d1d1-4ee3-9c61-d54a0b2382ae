using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Paas.Common;

namespace Paas.Model
{
    /// <summary>
    /// 帳單記錄模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸帳單記錄的相關資訊，包含帳單的金額、帳單狀態、付款方式和到期日期等。
    /// 經由繼承 BaseModel，此模型支援基礎查詢功能如分頁和排序。
    /// 帳單記錄用於追蹤租戶的付款情況、生成財務報表和管理收入流。
    /// </remarks>
    public class BillingModel : BaseModel
    {
        /// <summary>
        /// 帳單記錄唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為帳單記錄的主鍵。
        /// 此值在創建帳單記錄時由系統自動指定，用於唯一識別每筆帳單。
        /// 作為可空值 Guid 允許在建立新帳單時暫時不指定該值，由資料庫自動生成。
        /// 帳單記錄的識別碼在財務報表、收据和客戶調解中都會被參考。
        /// </remarks>
        public Guid? record_id { get; set; }

        /// <summary>
        /// 帳單所屬租戶識別碼
        /// </summary>
        /// <remarks>
        /// 此帳單記錄所屬的租戶識別碼，指向 tenants 表中的 tenant_id。
        /// 用於將帳單與特定租戶關聯，以便追蹤每個租戶的付款和帳單歷史。
        /// 它是該表的外鍵之一，用於建立與 Tenants 表的關聯關係。
        /// 可空值主要用於查詢目的，允許無需指定特定租戶就可查詢所有帳單。
        /// </remarks>
        public Guid? tenant_id { get; set; }

        /// <summary>
        /// 帳單金額
        /// </summary>
        /// <remarks>
        /// 該帳單的金額數值，以 decimal 類型存儲以確保財務計算的精確性。
        /// 此項用於計算租戶應支付的金額，通常與 currency_id 屬性一起使用以指定帳單的貨幣。
        /// 典型值範例如 150.75，表示 150 元 75 分。使用 decimal 類型避免浮點數常見的四捨五入問題。
        /// 在財務計算和報表中，此金額將用於生成總額、余額和繳付狀態。
        /// </remarks>
        public decimal amount { get; set; }

        /// <summary>
        /// 帳單幣別識別碼
        /// </summary>
        /// <remarks>
        /// 此帳單使用的貨幣識別碼，指向 currencies 表中的 currency_id。
        /// 用於指定帳單金額的貨幣單位，如 TWD、USD 或 EUR 等。
        /// 貨幣資訊對於多國家或跨國服務的租戶至關重要，也影響匯率計算和報表顯示。
        /// 此識別碼與 amount 屬性一起確定了帳單的實際價值。
        /// </remarks>
        public Guid currency_id { get; set; }

        /// <summary>
        /// 付款方式識別碼
        /// </summary>
        /// <remarks>
        /// 此帳單指定的付款方式識別碼，可能指向付款方式相關表中的 ID。
        /// 用於記錄帳單的付款方式，如信用卡、銀行轉帳、純電子付款或其他支付平台。
        /// 此資訊對於處理自動收款、付款提醒和帳單對貼等流程至關重要。
        /// 不同的付款方式可能有不同的處理流程和手續費計算方式。
        /// </remarks>
        public Guid payment_method_id { get; set; }

        /// <summary>
        /// 帳單狀態
        /// </summary>
        /// <remarks>
        /// 此帳單的目前狀態，以字串形式存儲。
        /// 常見的狀態值可能包括：
        /// - pending：待付款，帳單已生成但尚未支付
        /// - paid：已付款，帳單已成功付款
        /// - canceled：已取消，帳單已被取消不需要支付
        /// - overdue：逾期，帳單超過付款期限尚未支付
        /// - refunded：已退款，帳單支付後已退款
        /// 此狀態用於在使用者介面和報表中顯示帳單的當前狀態，並驅動不同的處理流程。
        /// 可空值主要用於查詢目的，允許搜尋不指定特定狀態的帳單。
        /// </remarks>
        public string? status { get; set; }

        /// <summary>
        /// 帳單到期時間
        /// </summary>
        /// <remarks>
        /// 此帳單的付款到期日期和時間，使用 DateTimeOffset 類型包含時區資訊。
        /// 此日期表示客戶應在什麼時間前完成付款，超過此時間後帳單將被標記為逾期。
        /// 常用於生成付款提醒和逾期警報，以及計算逾期費用和應收帳款的財務報表。
        /// 使用 DateTimeOffset 而非 DateTime 可以確保跨時區的正確性，特別是處理國際租戶時。
        /// </remarks>
        public DateTimeOffset due_date { get; set; }

        /// <summary>
        /// 帳單支付時間
        /// </summary>
        /// <remarks>
        /// 此帳單被支付的確切日期和時間，使用 DateTimeOffset 類型包含時區資訊。
        /// 當帳單狀態為 paid 時，此欄位記錄實際的付款時間，用於付款記錄和客戶付款行為分析。
        /// 對於未支付的帳單，此欄位可能會是系統預設值（如平台默認的最小日期值）或可能實際上應設計為可空值。
        /// 重要的是要注意它與 status 欄位的一致性，並在實際實現中確保它只有在帳單狀態為 paid 時才有意義。
        /// </remarks>
        public DateTimeOffset paid_at { get; set; }
    }
}
