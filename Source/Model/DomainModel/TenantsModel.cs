using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 租戶資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸租戶的相關資訊，包含租戶的基本資料、狀態和財務設定等。
    /// 經由繼承 BaseModel，此模型更能支援基礎查詢功能如分頁和排序。
    /// 該模型被用於控制器和服務層之間的數據傳輸，以及與數據庫實體的映射。
    /// </remarks>
    public class TenantsModel : BaseModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public TenantsModel()
        {
            contact_email = string.Empty;
            name = string.Empty;
            legal_address = new AddressModel
            {
                street = string.Empty,
                city = string.Empty,
                country = string.Empty,
                postalCode = string.Empty
            };
        }

        /// <summary>
        /// 租戶是否有效
        /// </summary>
        /// <remarks>
        /// 此屬性指示租戶帳戶的啟用狀態，用於控制租戶的訪問權限和系統功能。
        /// 當值為 true 時，租戶可正常使用所有功能。
        /// 當值為 false 時，租戶被停用，通常是因為付款問題、合約過期或管理員手動停用。
        /// 可空值，用於查詢用途，允許不指定狀態進行查詢。
        /// </remarks>
        public bool? is_active { get; set; }

        /// <summary>
        /// 租戶唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為租戶的主鍵和對外識別用途。
        /// 此值在創建租戶時自動指定，且在租戶生命週期內保持不變。
        /// 作為可空值 Guid 允許在建立新租戶時暫時不指定該值。
        /// </remarks>
        public Guid? tenant_id { get; set; }

        /// <summary>
        /// 租戶主要聯絡電子郵件
        /// </summary>
        /// <remarks>
        /// 租戶的主要聯絡信箱，用於系統通知、帳單發送、重要更新和客戶支援等用途。
        /// 此欄位必須是有效的電子郵件地址格式，且在系統中應有相應的驗證機制。
        /// 通常為租戶管理員或財務主管的聯絡信箱。
        /// </remarks>
        public required string contact_email { get; set; }

        /// <summary>
        /// 租戶法定名稱
        /// </summary>
        /// <remarks>
        /// 租戶的正式法定名稱，用於帳單、合約和法律文件等場合。
        /// 此名稱應與租戶的商業登記或法律文件上的正式名稱相符。
        /// 在系統畫面和使用者介面中，此名稱將作為租戶的主要顯示標識。
        /// </remarks>
        public required string name { get; set; }

        /// <summary>
        /// 帳單計價幣別 ID
        /// </summary>
        /// <remarks>
        /// 租戶預設的帳單計價幣別，指向 currencies 表中對應貨幣的 currency_id。
        /// 此屬性決定了租戶的所有帳單和付款將使用的預設貨幣。
        /// 符合 ISO 4217 標準的貨幣單位，如 TWD、USD 等。
        /// </remarks>
        public Guid billing_currency { get; set; }

        

        /// <summary>
        /// 租戶法律認證地址模型
        /// </summary>
        /// <remarks>
        /// 租戶的法律認證地址，以結構化的 AddressModel 物件形式存儲。
        /// 包含街道名稱（street）、城市（city）、國家（country）和郵遞編碼（postalCode）等詳細的地址資訊。
        /// 該地址將用於帳單、法律文件和合約等正式用途，應確保其準確性和完整性。
        /// 這是推薦的地址存儲方式，相較於 legalAddress 字串屬性更為結構化和類型安全。
        /// </remarks>
        public required AddressModel legal_address { get; set; }



    }


}
