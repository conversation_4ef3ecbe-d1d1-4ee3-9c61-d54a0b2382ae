using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// API 使用紀錄模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸 API 的使用紀錄資訊，包含 API 請求的相關數據如來源、效能和結果等。
    /// 經由繼承 BaseModel，此模型支援基礎查詢功能如分頁和排序。
    /// 這些紀錄用於效能分析、資源規劃、監控告警和使用計費等目的。
    /// </remarks>
    public class ApiUsageModel : BaseModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public ApiUsageModel()
        {
            request_ip = string.Empty;
        }

        /// <summary>
        /// API 使用紀錄唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為 API 使用紀錄的主鍵。
        /// 此值在創建使用紀錄時由系統自動指定，用於唯一識別和查詢每筆 API 請求的詳細資訊。
        /// 作為可空值 Guid 允許在建立新紀錄時暫時不指定該值，由資料庫自動生成。
        /// </remarks>
        public Guid? usage_id { get; set; }

        /// <summary>
        /// 所屬 API 識別碼
        /// </summary>
        /// <remarks>
        /// 此使用紀錄相關的 API 識別碼，指向 apis 表中的 api_id。
        /// 用於將使用紀錄與特定 API 服務關聯，以便分析和計算每個 API 的使用情況。
        /// 它是該表的外鍵之一，用於建立與 APIs 表的關聯關係。
        /// 可空值主要用於查詢目的，允許無需指定特定 API 就可查詢所有使用紀錄。
        /// </remarks>
        public Guid? api_id { get; set; }

        /// <summary>
        /// API 金鑰識別碼
        /// </summary>
        /// <remarks>
        /// 用於發起 API 請求的金鑰識別碼，指向 api_keys 表中的 key_id。
        /// 此識別碼用於追蹤哪個客戶端或應用程式執行了請求，對於計費、授權和稽核等功能至關重要。
        /// 金鑰與特定的客戶應用程式和租戶關聯，是驗證和存取控制的重要依據。
        /// 該屬性不可為空，因為任何 API 請求必須使用有效的 API 金鑰進行驗證。
        /// </remarks>
        public Guid key_id { get; set; }

        /// <summary>
        /// 請求來源 IP 地址
        /// </summary>
        /// <remarks>
        /// 發起 API 請求的客戶端 IP 地址，以字串形式存儲。
        /// 此資訊用於安全分析、地理位置追蹤和權限驗證等安全監控目的。
        /// 可以記錄 IPv4 或 IPv6 格式的地址，如 *********** 或 2001:0db8:85a3:0000:0000:8a2e:0370:7334。
        /// 透過記錄來源 IP，系統可以照到地理位置限制和偵測潛在的安全威脅，如非限定國家地區的存取嘗試。
        /// </remarks>
        public string request_ip { get; set; }

        /// <summary>
        /// API 響應時間（毫秒）
        /// </summary>
        /// <remarks>
        /// API 請求的處理時間，以毫秒為單位。代表從接收請求到返回響應的完整處理時間。
        /// 使用 decimal 類型以保存高精度的時間值，包含小數點後多位數字，如 150.5 表示 150.5 毫秒。
        /// 此數據對於效能監控、資源優化和服務籤小判斷與訊息分析至關重要。
        /// 系統可能會根據此時間設定警報閾值，當響應時間超過預期時觸發員工警報。
        /// </remarks>
        public decimal response_time { get; set; }

        /// <summary>
        /// HTTP 狀態碼
        /// </summary>
        /// <remarks>
        /// API 請求回傳的 HTTP 狀態碼，如 200（成功）、400（請求錯誤）、500（伺服器錯誤）等。
        /// 此屬性用於分析 API 請求的成功率、錯誤率和常見問題模式。
        /// 在監控工具和平臺中，狀態碼可分析出 API 的健康狀態和請求辦理质量。
        /// 系統可能會設定錯誤狀態碼（如 4xx 層級和 5xx 層級）的監控警報，以快速看出系統問題。
        /// </remarks>
        public int status_code { get; set; }

        /// <summary>
        /// 查詢時間範圍的開始時間
        /// </summary>
        /// <remarks>
        /// 查詢 API 使用紀錄時指定的時間範圍起始位置。
        /// 主要用於資料查詢和篩選，而非 API 請求的實際開始時間。
        /// 此欄位設為可空值，因為查詢時可能不需要指定範圍的開始時間。
        /// 典型用法是搜尋特定時間範圍內的 API 使用情況，如「過去 30 天的請求紀錄」。
        /// </remarks>
        public DateTime? start_time { get; set; }

        /// <summary>
        /// 查詢時間範圍的結束時間
        /// </summary>
        /// <remarks>
        /// 查詢 API 使用紀錄時指定的時間範圍結束位置。
        /// 與 start_time 搭配使用，定義一個完整的時間範圍進行資料查詢和分析。
        /// 此欄位同樣設為可空值，因為查詢時可能不需要指定範圍的結束時間。
        /// 常用於產生特定時間範圍的報表和分析，如「2025年第一季度的 API 使用情況」。
        /// </remarks>
        public DateTime? end_time { get; set; }
    }
}
