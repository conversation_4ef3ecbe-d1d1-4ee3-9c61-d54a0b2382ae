using Newtonsoft.Json;
using Paas.Common;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// API金鑰資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸系統中API金鑰的相關資訊，包含金鑰值、狀態、權限範圍和有效期等。
    /// 經由繼承 BaseModel，此模型支援基礎查詢功能如分頁和排序。
    /// API金鑰用於第三方應用程式或服務與系統API進行身份驗證和授權，確保安全的資料存取。
    /// 此模型記錄了金鑰的完整生命週期，從生成到過期或撤銷，並支援租戶隔離和權限控制。
    /// </remarks>
    public class ApiKeysModel : BaseModel
    {
        /// <summary>
        /// API金鑰唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為API金鑰的主鍵和識別用途。
        /// 此值在創建API金鑰時自動指定，且在金鑰的整個生命週期內保持不變。
        /// 用於金鑰管理、查詢、撤銷和審計日誌等多種場景中。
        /// 與實際的API金鑰值（api_key）不同，此ID僅用於內部識別，不對外暴露。
        /// </remarks>
        public Guid key_id { get; set; }
        /// <summary>
        /// 所屬租戶識別碼
        /// </summary>
        /// <remarks>
        /// 指定API金鑰所屬的租戶識別碼，為外鍵指向 Tenants 表的 tenant_id。
        /// 若為 null，可能表示此API金鑰是系統級別的，非特定租戶所有。
        /// 用於多租戶架構下的資源隔離和存取控制，確保租戶只能存取其自身的資源。
        /// 在API請求授權過程中，系統會驗證金鑰對應的租戶權限。
        /// </remarks>
        public Guid? tenant_id { get; set; }
        /// <summary>
        /// API金鑰值，須確保系統唯一性
        /// </summary>
        /// <remarks>
        /// 實際用於API請求認證的金鑰字符串值，通常為高熵亂數生成的字符串。
        /// 系統必須確保此值在所有租戶中的全局唯一性，以防止金鑰衝突和安全問題。
        /// 金鑰值應進行安全存儲，在資料庫中可考慮採用雜湊處理以增強安全性。
        /// 此值在API請求的授權頭或參數中使用，作為身份驗證的憑證。
        /// </remarks>
        public required string api_key { get; set; }
        /// <summary>
        /// 金鑰狀態，僅允許 'active' 或 'revoked'
        /// </summary>
        /// <remarks>
        /// 指定API金鑰的當前狀態，控制金鑰是否可用於API認證。
        /// 狀態值限制為兩種：
        /// - 'active'：金鑰處於活躍狀態，可正常使用於API認證。
        /// - 'revoked'：金鑰已被撤銷，不再有效，任何使用此金鑰的API請求將被拒絕。
        /// 撤銷操作通常是不可逆的，基於安全考慮，被撤銷的金鑰不應重新啟用。
        /// 系統會在驗證過程中檢查此狀態，拒絕已撤銷金鑰的所有請求。
        /// </remarks>
        public required string status { get; set; }
        /// <summary>
        /// API存取範圍與權限設定
        /// </summary>
        /// <remarks>
        /// 指定API金鑰的權限範圍，定義金鑰可存取的資源和執行的操作類型。
        /// 使用 Scopes 類別封裝權限設定，包含 read 和 write 權限。
        /// 用於精細挖控制API金鑰的權限，確保安全性和最小權限原則。
        /// 若為 null，可能表示此金鑰沒有特定的權限範圍限制，或使用預設的權限設定。
        /// </remarks>
        public Scopes? scopes { get; set; }
        /// <summary>
        /// 金鑰過期時間，null表示永不過期
        /// </summary>
        /// <remarks>
        /// 指定API金鑰的過期時間，以 DateTimeOffset 格式存儲，包含時區資訊。
        /// 系統於授權驗證時會檢查此屬性，自動拒絕使用過期金鑰的請求。
        /// 金鑰過期機制可增強安全性，防止乾卡API金鑰長期使用造成的安全風險。
        /// 可空屬性（null）表示此金鑰永不過期，適用於系統內部或高度信任的整合情境，但應謹慎使用此功能。
        /// </remarks>
        public DateTimeOffset? expiration_at_dt { get; set; }
        /// <summary>
        /// 金鑰生成時間
        /// </summary>
        /// <remarks>
        /// 記錄API金鑰的初始生成時間，以 DateTimeOffset 格式存儲，包含時區資訊。
        /// 此時間戳用於追蹤金鑰的完整生命週期，並助於審計和診斷目的。
        /// 宜存儲為非空屬性，因為每個金鑰都必須有索生成時間。
        /// 用於計算金鑰年齡、生成報表和分析金鑰使用模式等。
        /// </remarks>
        public DateTimeOffset generated_at_dt { get; set; }
        /// <summary>
        /// 最後使用時間
        /// </summary>
        /// <remarks>
        /// 記錄API金鑰最近一次成功認證使用的時間，以 DateTimeOffset 格式存儲，包含時區資訊。
        /// 此屬性師助追蹤金鑰的使用活躍度，用於識別閒置或不再使用的金鑰。
        /// 可空屬性（null）表示金鑰尚未被使用過，或系統未記錄使用情況。
        /// 定期檢查閒置金鑰可提高系統安全性，並可作為自動撤銷閒置金鑰的依據。
        /// </remarks>
        public DateTimeOffset? last_used_at_dt { get; set; }
        /// <summary>
        /// 金鑰過期時間，null表示永不過期
        /// </summary>
        /// <remarks>
        /// 計算屬性，將 expiration_at_dt 轉換為格式化的字符串以便於展示。
        /// 使用 ToDateStr() 延伸方法確保日期格式一致性並依系統設定適當顯示。
        /// 主要用於前端介面和 API 回應中，方便使用者或客戶端理解金鑰有效期。
        /// 當 expiration_at_dt 為 null 時，此屬性也會返回 null，表示金鑰永不過期。
        /// </remarks>
        public string? expiration_at => expiration_at_dt?.ToDateStr();
        /// <summary>
        /// 金鑰生成時間
        /// </summary>
        /// <remarks>
        /// 計算屬性，將 generated_at_dt 轉換為格式化的字符串以便於展示。
        /// 使用 ToDateStr() 延伸方法將 DateTimeOffset 轉換為易讀的日期格式。
        /// 主要用於前端介面、報表和 API 回應中的呈現。
        /// 因為 generated_at_dt 為非空屬性，此屬性將始終有值。
        /// </remarks>
        public string generated_at => generated_at_dt.ToDateStr();
        /// <summary>
        /// 最後使用時間(顯示格式)
        /// </summary>
        /// <remarks>
        /// 計算屬性，將 last_used_at_dt 轉換為格式化的字符串以便於展示。
        /// 使用 ToDateStr() 延伸方法將 DateTimeOffset 轉換為易讀的日期格式。
        /// 主要用於前端介面顯示和管理工具中，協助管理員追蹤金鑰使用情況。
        /// 當 last_used_at_dt 為 null 時，此屬性也會返回 null，表示金鑰尚未被使用。
        /// </remarks>
        public string? last_used_at => last_used_at_dt?.ToDateStr();
    }
}
