using Newtonsoft.Json;
using Paas.Common;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 安全日誌資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸系統安全相關事件的日誌資訊，包含事件類型、發生時間、來源和相關使用者等。
    /// 經由繼承 BaseModel，此模型支援基礎查詢功能如分頁和排序。
    /// 安全日誌在系統審計、安全分析和法規遵循上有重要作用，提供系統安全事件的完整記錄。
    /// 常用於安全審計、安全事件調查、异常行為偵測和合規性報表等場景。
    /// </remarks>
    public class SecurityLogsModel : BaseModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public SecurityLogsModel()
        {
            ip_address = IPAddress.Any;
        }

        /// <summary>
        /// 日誌唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為安全日誌紀錄的主鍵和識別用途。
        /// 此值在創建日誌時自動指定，且在整個生命週期內保持不變。
        /// 用於日誌查詢、關聯分析和安全事件追踪等場景。
        /// 此識別碼沒有故意的外部意義，僅為系統內部使用的標識符。
        /// </remarks>
        public Guid log_id { get; set; }
        /// <summary>
        /// 租戶識別碼
        /// </summary>
        /// <remarks>
        /// 指定日誌相關的租戶識別碼，為外鍵指向 Tenants 表的 tenant_id。
        /// 若為 null，可能表示此事件與系統層級相關，而非特定租戶的行為。
        /// 强化多租戶環境中的安全日誌隔離，確保適當的存取控制和數據隱私。
        /// 在日誌查詢和報表中，常作為重要的篩選條件。
        /// </remarks>
        public Guid? tenant_id { get; set; }
        /// <summary>
        /// 使用者識別碼
        /// </summary>
        /// <remarks>
        /// 指定觸發安全事件的使用者識別碼，為外鍵指向 Users 表的 user_id。
        /// 若為 null，可能表示此事件不涉及特定用戶，或由系統自動觸發。
        /// 用於追踪用戶行為、存取模式分析和安全診斷。
        /// 在安全審計和使用者活動追踪中有重要作用，可追溯到特定用戶的行為和操作。
        /// </remarks>
        public Guid? user_id { get; set; }
        /// <summary>
        /// 事件類型識別碼
        /// </summary>
        /// <remarks>
        /// 指定安全事件的類型或分類，為外鍵指向事件類型定義表。
        /// 常見的安全事件類型包括登入失敗、密碼重設、權限變更、API金鑰管理等。
        /// 此屬性師助系統組織和分類安全日誌，方便分析和報表生成。
        /// 若為 null，可能表示無法正確分類的事件類型或系統未配置的事件。
        /// </remarks>
        public Guid? event_type_id { get; set; }
        /// <summary>
        /// 事件來源IP地址
        /// </summary>
        /// <remarks>
        /// 記錄觸發安全事件的來源IP地址，使用 System.Net.IPAddress 類型存儲。
        /// 此屬性對安全分析和威脅偵測至關重要，可補助識別可疑活動和事件關聯性。
        /// 精確記錄IP地址可用於追踪攻擊來源、地理位置分析和網路遭受濻湯判斷。
        /// 系統處理時應適當考慮 IPv4 和 IPv6 地址格式。
        /// </remarks>
        public IPAddress ip_address { get; set; }
        /// <summary>
        /// 事件來源用戶代理
        /// </summary>
        /// <remarks>
        /// 記錄觸發安全事件的客戶端用戶代理字符串（User-Agent）。
        /// 包含客戶端的瀏覽器、作業系統、設備等資訊，幫助識別使用的程式和環境。
        /// 可空屬性，因為某些系統內部事件或API請求可能無用戶代理資訊。
        /// 用於無人操作威脃偵測、用戶行為分析和異常存取識別。
        /// </remarks>
        public string? user_agent { get; set; }
        /// <summary>
        /// JSON格式的事件詳細資訊
        /// </summary>
        /// <remarks>
        /// 存儲安全事件的詳細資訊，使用 SecurityLogsDetails 類型封裝。
        /// 包含事件的具體行為和原因，提供事件的上下文和細節。
        /// 可空屬性，因為某些簡單事件可能不需要額外的詳細資訊。
        /// 使用「對象-關聯對象對應」而非純粗字符串格式存儲，方便系統進行深度診斷和分析。
        /// </remarks>
        public SecurityLogsDetails? details { get; set; }
        /// <summary>
        /// 開始時間
        /// </summary>
        /// <remarks>
        /// 記錄安全事件的開始時間，以 DateTimeOffset 格式存儲，包含時區資訊。
        /// 對於跨時區或分散式系統的事件時序追踪和相關性分析至關重要。
        /// 使用 DateTimeOffset 而非 DateTime 可確保跨時區的時間一致性和比較正確性。
        /// 用於時序分析、事件關聯性判斷和安全事件時間線建立。
        /// </remarks>
        public DateTimeOffset start_time { get; set; }
        /// <summary>
        /// 結束時間
        /// </summary>
        /// <remarks>
        /// 記錄安全事件的結束時間，以 DateTimeOffset 格式存儲，包含時區資訊。
        /// 對於長時間運行的事件，如批次處理、長時數据區區操作等，此屬性能記錄整個運行時間。
        /// 結合 start_time 可計算事件持續時間，判斷事件效率和效能獨表。
        /// 在安全事件分析中，此屬性與 start_time 的差別可提供活動持續時間的重要診斷資訊。
        /// </remarks>
        public DateTimeOffset end_time { get; set; }
    }
}
