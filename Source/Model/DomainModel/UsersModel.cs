using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 使用者資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸系統使用者的相關資訊，包括帳號、所屬租戶、角色、電子郵件等。
    /// 支援以租戶為單位的多租戶架構，並可擴展至系統管理員等特殊角色。
    /// 常用於用戶管理、權限控管、登入認證與審計報表等場景。
    /// </remarks>
    public class UsersModel : BaseModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public UsersModel()
        {
            username = string.Empty;
            email = string.Empty;
            password_hash = string.Empty;
        }

        /// <summary>
        /// 使用者唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為使用者的主鍵和對外識別用途。
        /// 此值在建立使用者帳號時自動指定，且在帳號生命週期內保持不變。
        /// 用於用戶查詢、權限分配、審計日誌等多種場景。
        /// </remarks>
        public Guid user_id { get; set; }

        /// <summary>
        /// 所屬租戶識別碼，可為空表示系統管理員
        /// </summary>
        /// <remarks>
        /// 指定使用者所屬的租戶識別碼，為外鍵指向 Tenants 表的 tenant_id。
        /// 若為 null，表示此帳號為全域系統管理員，不隸屬於任何租戶。
        /// 用於多租戶架構下的用戶隔離與權限控管。
        /// </remarks>
        public Guid? tenant_id { get; set; }

        /// <summary>
        /// 使用者名稱
        /// </summary>
        /// <remarks>
        /// 使用者的登入帳號名稱，應具唯一性。
        /// 用於登入驗證、管理介面顯示及審計記錄。
        /// 建議命名規則包含英數字，並避免特殊符號。
        /// </remarks>
        public string username { get; set; }

        /// <summary>
        /// 使用者電子郵件
        /// </summary>
        /// <remarks>
        /// 使用者的主要聯絡電子郵件地址。
        /// 用於系統通知、密碼重設、帳號驗證等功能。
        /// 此欄位應驗證格式正確且具唯一性。
        /// </remarks>
        public string email { get; set; }

        /// <summary>
        /// 密碼雜湊值
        /// </summary>
        /// <remarks>
        /// 儲存經過加鹽雜湊處理的密碼，不應以明文存放。
        /// 僅用於系統內部登入認證，不可對外公開。
        /// 建議採用業界標準演算法（如 bcrypt、PBKDF2 等）進行雜湊。
        /// </remarks>
        public string password_hash { get; set; }

        /// <summary>
        /// 使用者角色識別碼
        /// </summary>
        /// <remarks>
        /// 指定使用者所屬角色，為外鍵指向 Roles 表的 role_id。
        /// 用於實現以角色為基礎的存取控制（RBAC），決定使用者擁有的權限集合。
        /// 若為 null，需根據系統邏輯指定預設角色或限制存取。
        /// </remarks>
        public Guid? role_id { get; set; }
    }
}
