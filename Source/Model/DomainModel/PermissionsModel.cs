using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 權限資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸權限的相關資訊，包含權限的基本資料、角色關聯和存取控制設定等。
    /// 經由繼承 BaseModel，此模型支援基礎查詢功能如分頁和排序。
    /// 該模型被用於控制器和服務層之間的數據傳輸，以及與數據庫實體的映射。
    /// 權限模型在實現以角色為基礎的存取控制（RBAC）中有重要的作用，定義了角色可以對特定資源執行的操作。
    /// </remarks>
    public class PermissionsModel : BaseModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public PermissionsModel()
        {
            resource = string.Empty;
            action = string.Empty;
        }

        /// <summary>
        /// 權限唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為權限的主鍵和對外識別用途。
        /// 此值在創建權限時自動指定，且在權限的整個生命週期內保持不變。
        /// 在數據庫中此欄位設置為主鍵，並建立索引以加速查詢。
        /// 此 ID 在權限管理、審計日誌和角色權限映射中廣泛使用。
        /// </remarks>
        public Guid permission_id { get; set; }

        /// <summary>
        /// 角色識別碼
        /// </summary>
        /// <remarks>
        /// 指定此權限所屬的角色識別碼，作為外鍵指向 Roles 表的 role_id。
        /// 此欄位建立了權限與角色之間的直接關聯，實現以角色為基礎的存取控制機制。
        /// 在授權檢查時，系統將根據用戶的角色確定其所有的權限，進而決定用戶是否可以存取特定資源。
        /// 一個角色可以有多個權限，建立一對多的關聯，允許靈活的權限管理。
        /// </remarks>
        public Guid role_id { get; set; }

        /// <summary>
        /// 資源名稱
        /// </summary>
        /// <remarks>
        /// 指定此權限適用的資源名稱或資源類型。
        /// 資源可以是系統中的圖形介面元素、API 端點、數據表或其他可操作的物件。
        /// 範例包括："users"、"apis"、"billing"、"reports" 等，代表不同的資源定義。
        /// 資源名稱可能使用路徑格式或层次結構，如 "api/keys" 或 "reports/financial"，以支援更精細的權限定義。
        /// </remarks>
        public string resource { get; set; }

        /// <summary>
        /// 操作類型
        /// </summary>
        /// <remarks>
        /// 指定此權限允許對資源執行的操作類型。
        /// 操作類型通常對應 CRUD 操作，如："read"、"create"、"update"、"delete"，也可能有其他特定操作如 "approve"、"reject"、"export" 等。
        /// 結合 resource 欄位，權限模型定義了「對特定資源可以執行的操作」，構成完整的權限定義。
        /// 操作類型可能會映射至 HTTP 方法，如 "read" 對應 GET，"create" 對應 POST 等。
        /// </remarks>
        public string action { get; set; }
    }
}
