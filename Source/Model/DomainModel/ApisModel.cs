using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// API 服務資訊模型
    /// </summary>
    /// <remarks>
    /// 此模型用於存儲和傳輸 API 服務的相關資訊，包含 API 的基本資料、所屬租戶、版本與流量限制等。
    /// 經由繼承 BaseModel，此模型支援基礎查詢功能如分頁和排序。
    /// 該模型被用於控制器和服務層之間的數據傳輸，以及與數據庫實體的映射。
    /// </remarks>
    public class ApisModel : BaseModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public ApisModel()
        {
            name = string.Empty;
            description = string.Empty;
            version = string.Empty;
        }

        /// <summary>
        /// API 唯一識別碼
        /// </summary>
        /// <remarks>
        /// 系統自動生成的全局唯一識別碼（UUID），作為 API 服務的主鍵和對外識別用途。
        /// 此值在創建 API 服務時自動指定，且在服務生命週期內保持不變。
        /// 作為可空值 Guid 允許在建立新 API 服務時暫時不指定該值。
        /// </remarks>
        public Guid? api_id { get; set; }

        /// <summary>
        /// 所屬租戶識別碼
        /// </summary>
        /// <remarks>
        /// 此 API 服務所屬的租戶識別碼，指向 tenants 表中的 tenant_id。
        /// 用於定義 API 服務與租戶之間的所有關係，以及適用不同租戶的權限控制和資源隔離。
        /// 作為外鍵參考，確保每個 API 服務都必須屬於一個有效的租戶。
        /// 可空值主要用於查詢目的，允許使用者搜尋不特定租戶的 API。
        /// </remarks>
        public Guid? tenant_id { get; set; }

        /// <summary>
        /// API 服務名稱
        /// </summary>
        /// <remarks>
        /// API 服務的顯示名稱，用於在使用者介面、文檔和開發者入口中顯示。
        /// 此名稱應簡潔明確地反映 API 的主要功能或用途，如「付款 API」、「訂單 API」等。
        /// 名稱應具有辨識度並避免與其他 API 重名，以減少用戶混淆。
        /// </remarks>
        public required string name { get; set; }

        /// <summary>
        /// API 功能描述
        /// </summary>
        /// <remarks>
        /// 對 API 服務功能和用途的詳細描述，用於文檔、開發者入口和管理界面。
        /// 該描述應清晰地說明 API 的主要功能、適用場景、主要特點和限制等信息。
        /// 好的描述可以幫助開發者與管理員更好地理解 API 的用途和功能範圍。
        /// </remarks>
        public required string description { get; set; }

        /// <summary>
        /// API 版本號
        /// </summary>
        /// <remarks>
        /// API 的版本號，用於指定 API 的訂約、相容性和變更追蹤。
        /// 通常使用語義化版本號格式（主版本.次版本.修訂版本），如 1.0.0、2.1.3 等。
        /// - 主版本（Major）：表示不兼容的 API 變更
        /// - 次版本（Minor）：表示向下兼容的功能改進
        /// - 修訂版本（Patch）：表示向下兼容的錯誤修復
        /// 版本號將用於 API 的生命週期管理和不同版本之間的轉換。
        /// </remarks>
        public required string version { get; set; }

        /// <summary>
        /// 每分鐘最大請求次數
        /// </summary>
        /// <remarks>
        /// 此 API 每分鐘允許的最大請求數量，用於流量控制和資源保護。
        /// 超過此限制的請求將導致端點回傳 429 Too Many Requests 狀態碼。
        /// 適當的速率限制可保護伺服器免受過重負載，同時允許正常的使用場景。
        /// 根據 API 的用途和資源需求，典型值可能從每分鐘數百次到數千次不等。
        /// </remarks>
        public int rate_limit_per_minute { get; set; }

        /// <summary>
        /// API 啟用狀態
        /// </summary>
        /// <remarks>
        /// 指示 API 服務的目前啟用狀態，控制 API 是否可用於客戶端。
        /// 當值為 true 時，表示 API 已啟用且可接受請求。
        /// 當值為 false 時，表示 API 已停用，所有請求將被拒絕並回傳適當的錯誤狀態碼。
        /// 可空值主要用於查詢目的，允許使用者搜尋 API 而不指定啟用狀態。
        /// 這使得管理員可以暫時停用 API 而不需要完全刪除它，方便於維護和版本轉換。
        /// </remarks>
        public bool? is_active { get; set; }
    }
}
