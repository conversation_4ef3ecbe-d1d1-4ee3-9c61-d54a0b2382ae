using AutoMapper;
using Newtonsoft.Json;
using Paas.Common;
using System.Web;

namespace Paas.Model
{
    public class PaasProfile : Profile
    {
        /// <summary>
        /// 進行物件Mapping
        /// </summary>
        public PaasProfile()
        {
            #region Common

            CreateMap<BaseEntity, BaseModel>()
                .ForMember(dest => dest.created_at_dt, opt => opt.MapFrom(src => src.created_at))
                .ForMember(dest => dest.updated_at_dt, opt => opt.MapFrom(src => src.updated_at))
                .ReverseMap();

            CreateMap<BaseQueryModel, BaseModel>();

            #endregion Common

            #region Tenants 租戶

            CreateMap<TenantsCreateModel, TenantsModel>();

            CreateMap<TenantsUpdateModel, TenantsModel>(); 

            CreateMap<BaseQueryModel, TenantsModel>();

            CreateMap<Tenants, TenantsModel>()
                .ForMember(dest => dest.legal_address, opt => opt.MapFrom(src => JsonConvert.DeserializeObject<AddressModel>(src.legal_address)))
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap()
                .ForMember(dest => dest.legal_address, opt => opt.MapFrom(src => JsonConvert.SerializeObject(src.legal_address)));

            CreateMap<TenantsModel, TenantsViewModel>()
                .ForMember(dest => dest.legal_address, opt => opt.MapFrom(src => src.legal_address));


            #endregion Tenants

            #region Apis API閘道管理

            CreateMap<ApisCreateModel, ApisModel>();

            CreateMap<ApisUpdateModel, ApisModel>();

            CreateMap<BaseQueryModel, ApisModel>();

            CreateMap<Apis, ApisModel>()
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap();

            CreateMap<ApisModel, ApisViewModel>();

            #endregion Apis

            #region Currencies 幣別

            CreateMap<Currencies, CurrenciesModel>()
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap();

            CreateMap<CurrenciesModel, CurrenciesViewModel>()
                .ForMember(dest => dest.exchange_rate, opt => opt.MapFrom(src => Math.Round(src.exchange_rate,6)));

            CreateMap<CurrenciesCreateModel, CurrenciesModel>();

            CreateMap<CurrenciesUpdateModel, CurrenciesModel>();

            #endregion Currencies

            #region ApiUsage API 使用紀錄

            CreateMap<BaseQueryModel, ApiUsageModel>();

            CreateMap<ApiUsageCreateModel, ApiUsageModel>();

            CreateMap<ApiUsage, ApiUsageModel>()
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap();

            CreateMap<ApiUsageModel, ApiUsageViewModel>();

            #endregion ApiUsage

            #region Billing API 帳單與付款管理

            CreateMap<BaseQueryModel, BillingModel>();

            CreateMap<BillingCreateModel, BillingModel>()
                .ForMember(dest => dest.due_date, opt => opt.MapFrom(src => Utils.NormalizeToUtc(src.due_date)));

            CreateMap<BillingUpdateModel, BillingModel>()
                .ForMember(dest => dest.paid_at, opt => opt.MapFrom(src => Utils.NormalizeToUtc(src.paid_at)));

            CreateMap<BillingRecords, BillingModel>()
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap();

            CreateMap<BillingModel, BillingViewModel>();

            #endregion Billing

            #region ApiKeys API金鑰

            CreateMap<ApiKeys, ApiKeysModel>()
                .ForMember(dest => dest.scopes, opt => opt.MapFrom(src => 
                string.IsNullOrEmpty(src.scopes)
                    ? null
                    : JsonConvert.DeserializeObject<Scopes>(src.scopes)))
                .ForMember(dest => dest.generated_at_dt, opt => opt.MapFrom(src => src.generated_at))
                .ForMember(dest => dest.expiration_at_dt, opt => opt.MapFrom(src => src.expiration_at))
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap()
                .ForPath(dest => dest.scopes, opt => opt.MapFrom(src => 
                    src.scopes == null
                    ? null
                    : JsonConvert.SerializeObject(src.scopes)));

            CreateMap<ApiKeysQueryModel, ApiKeysModel>();

            CreateMap<ApiKeysModel, ApiKeysViewModel>();

            CreateMap<ApiKeysCreateModel, ApiKeysModel>()
            .ForMember(dest => dest.expiration_at_dt,
                opt => opt.MapFrom(src => src.expiration_at.HasValue
                    ? Utils.NormalizeToUtc(src.expiration_at.Value)
                    : (DateTimeOffset?)null));

            CreateMap<ApiKeysUpdateModel, ApiKeysModel>()
            .ForMember(dest => dest.expiration_at_dt,
                opt => opt.MapFrom(src => src.expiration_at.HasValue
                    ? Utils.NormalizeToUtc(src.expiration_at.Value)
                    : (DateTimeOffset?)null));

            #endregion ApiKeys API金鑰

            #region SecurityLogs 安全性日誌

            CreateMap<SecurityLogs, SecurityLogsModel>()
                .ForMember(dest => dest.details, opt => opt.MapFrom(src =>
                string.IsNullOrEmpty(src.details)
                    ? null
                    : JsonConvert.DeserializeObject<SecurityLogsDetails>(src.details)))
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap();

            CreateMap<SecurityLogsQueryModel, SecurityLogsModel>();

            CreateMap<SecurityLogsModel, SecurityLogsViewModel>()
                .ForMember(dest => dest.ip_address, opt => opt.MapFrom(src => src.ip_address.ToString()));

            #endregion SecurityLogs 安全性日誌

            #region MonitoringMetrics 監控指標

            CreateMap<BaseQueryModel, MonitoringMetricsModel>();

            CreateMap<MetricsData, MonitoringMetricsModel>()
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap();

            CreateMap<MonitoringMetricsModel, MonitoringMetricsViewModel>()
                .ForMember(dest => dest.tags, opt => opt.MapFrom(src => JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(src.tags)));

            #endregion MonitoringMetrics

            #region Users 使用者管理 

            CreateMap<UsersCreateModel, UsersModel>();

            CreateMap<UsersUpdateModel, UsersModel>();

            CreateMap<BaseQueryModel, UsersModel>();

            CreateMap<Users, UsersModel>()
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap();

            CreateMap<UsersModel, UsersViewModel>();

            #endregion Users 

            #region Roles 角色管理

            CreateMap<RolesCreateModel, RolesModel>();

            CreateMap<Roles, RolesModel>()
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap();

            CreateMap<RolesModel, RolesViewModel>();

            #endregion Roles

            #region Permissions 權限管理

            CreateMap<Permissions, PermissionsModel>()
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap();

            CreateMap<PermissionsModel, PermissionsViewModel>();

            #endregion Permissions

            #region NotificationChannels 通知管道設定

            CreateMap<NotificationChannels, NotificationChannelsModel>()
                .ForMember(dest => dest.config, opt => opt.MapFrom(src =>
                string.IsNullOrEmpty(src.config)
                    ? null
                    : JsonConvert.DeserializeObject<NotificationChannelsConfig>(src.config ?? "")))
                .IncludeBase<BaseEntity, BaseModel>()
                .ReverseMap()
                .ForPath(dest => dest.config, opt => opt.MapFrom(src =>
                    src.config == null
                    ? null
                    : JsonConvert.SerializeObject(src.config)));

            CreateMap<NotificationChannelsQueryModel, NotificationChannelsModel>();

            CreateMap<NotificationChannelsModel, NotificationChannelsViewModel>();

            CreateMap<NotificationChannelsCreateModel, NotificationChannelsModel>();

            CreateMap<NotificationChannelsUpdateModel, NotificationChannelsModel>();

            #endregion NotificationChannels 通知管道設定

            #region Notifications 通知

            CreateMap<Notifications, NotificationsModel>()
               .ForMember(dest => dest.expires_at_dt, opt => opt.MapFrom(src => src.expires_at))
               .IncludeBase<BaseEntity, BaseModel>()
               .ReverseMap()
               .ForMember(dest => dest.expires_at, opt => opt.MapFrom(src => src.expires_at_dt));

            CreateMap<NotificationsQueryModel, NotificationsModel>();

            CreateMap<NotificationsModel, NotificationsViewModel>();

            CreateMap<NotificationsCreateModel, NotificationsModel>()
                .ForMember(dest => dest.expires_at_dt, opt => opt.MapFrom(src => Utils.NormalizeToUtc(src.expires_at)));

            #endregion Notifications 通知
        }
    }
}
