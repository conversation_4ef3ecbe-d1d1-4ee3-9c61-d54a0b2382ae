using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    public class ApisViewModel : BaseViewModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public ApisViewModel()
        {
            name = string.Empty;
            description = string.Empty;
            version = string.Empty;
        }

        /// <summary>
        /// API唯一識別碼
        /// </summary>
        public Guid api_id { get; set; }

        /// <summary>
        /// 所屬租戶識別碼
        /// </summary>
        public Guid tenant_id { get; set; }

        /// <summary>
        /// API服務名稱
        /// </summary>
        public required string name { get; set; }

        /// <summary>
        /// API功能描述
        /// </summary>
        public required string description { get; set; }

        /// <summary>
        /// API版本號
        /// 範例：1.0.0、2.1.3
        /// </summary>
        public required string version { get; set; }

        /// <summary>
        /// 每分鐘最大請求次數
        /// </summary>
        public int rate_limit_per_minute { get; set; }

        /// <summary>
        /// API啟用狀態
        /// </summary>
        public bool is_active { get; set; }
    }
}
