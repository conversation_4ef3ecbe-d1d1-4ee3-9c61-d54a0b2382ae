using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 安全性日誌 View Model
    /// </summary>
    public class SecurityLogsViewModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public SecurityLogsViewModel()
        {
            ip_address = string.Empty;
            details = new SecurityLogsDetails
            {
                action = string.Empty,
                reason = string.Empty
            };
            created_at = string.Empty;
        }

        /// <summary>
        /// 日誌唯一識別碼
        /// </summary>
        public Guid log_id { get; set; }
        /// <summary>
        /// 租戶識別碼
        /// </summary>
        public Guid? tenant_id { get; set; }
        /// <summary>
        /// 使用者識別碼
        /// </summary>
        public Guid? user_id { get; set; }
        /// <summary>
        /// 事件類型識別碼
        /// </summary>
        public Guid event_type_id { get; set; }
        /// <summary>
        /// 事件來源IP地址
        /// </summary>
        public string ip_address { get; set; }
        /// <summary>
        /// 事件來源用戶代理
        /// </summary>
        public string? user_agent { get; set; }
        /// <summary>
        /// JSON格式的事件詳細資訊
        /// </summary>
        public SecurityLogsDetails details { get; set; }
        /// <summary>
        /// 資料新增時間
        /// </summary>
        public string created_at { get; set; }
    }
}
