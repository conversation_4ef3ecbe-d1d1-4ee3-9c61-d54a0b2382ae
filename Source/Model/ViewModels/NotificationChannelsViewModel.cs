using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 通知管道設定 View Model
    /// </summary>
    public class NotificationChannelsViewModel : BaseViewModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public NotificationChannelsViewModel()
        {
            type = string.Empty;
            config = new NotificationChannelsConfig
            {
                smtp_host = string.Empty
            };
        }

        /// <summary>
        /// 通知管道唯一識別碼
        /// </summary>
        public Guid channel_id { get; set; }
        /// <summary>
        /// 管道類型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 管道設定
        /// </summary>
        public NotificationChannelsConfig config { get; set; }
        /// <summary>
        /// 啟用狀態，僅允許 'active' 或 'revoked'
        /// </summary>
        public bool is_active { get; set; }
    }
}
