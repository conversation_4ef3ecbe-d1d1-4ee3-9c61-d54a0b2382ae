using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// API金鑰資訊 View Model
    /// </summary>
    public class ApiKeysViewModel : BaseViewModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public ApiKeysViewModel()
        {
            api_key = string.Empty;
            status = string.Empty;
            scopes = new Scopes();
        }

        /// <summary>
        /// API金鑰唯一識別碼
        /// </summary>
        public Guid key_id { get; set; }
        /// <summary>
        /// 所屬租戶識別碼
        /// </summary>
        public Guid tenant_id { get; set; }
        /// <summary>
        /// API金鑰值，須確保系統唯一性
        /// </summary>
        public string api_key { get; set; }
        /// <summary>
        /// 金鑰狀態，僅允許 'active' 或 'revoked'
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// API存取範圍與權限設定
        /// </summary>
        public Scopes scopes { get; set; }
        /// <summary>
        /// 金鑰過期時間，null表示永不過期
        /// </summary>
        public string? expiration_at { get; set; }
        /// <summary>
        /// 金鑰生成時間
        /// </summary>
        public string? generated_at { get; set; }
        /// <summary>
        /// 最後使用時間
        /// </summary>
        public string? last_used_at { get; set; }
    }
}
