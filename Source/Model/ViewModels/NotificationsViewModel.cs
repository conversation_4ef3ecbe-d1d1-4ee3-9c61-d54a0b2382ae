using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    /// <summary>
    /// 通知 View Model
    /// </summary>
    public class NotificationsViewModel 
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public NotificationsViewModel()
        {
            expires_at = string.Empty;
            created_at = string.Empty;
        }

        /// <summary>
        /// 通知唯一識別碼
        /// </summary>
        public Guid notification_id { get; set; }
        /// <summary>
        /// 通知類型識別碼
        /// </summary>
        public Guid? type_id { get; set; }
        /// <summary>
        /// 租戶識別碼
        /// </summary>
        public Guid? tenant_id { get; set; }
        /// <summary>
        /// 使用者識別碼
        /// </summary>
        public Guid? user_id { get; set; }
        /// <summary>
        /// 通知標題
        /// </summary>
        public string? title { get; set; }
        /// <summary>
        /// 通知內容
        /// </summary>
        public string? message { get; set; }
        /// <summary>
        /// JSON格式的事件詳細資訊
        /// </summary>
        public string? status { get; set; }
        /// <summary>
        /// 通知優先級
        /// </summary>
        public string? priority { get; set; }
        /// <summary>
        /// 通知過期時間
        /// </summary>
        public string expires_at { get; set; }
        /// <summary>
        ///  新增時間
        /// </summary>
        public string created_at { get; set; }
    }
}
