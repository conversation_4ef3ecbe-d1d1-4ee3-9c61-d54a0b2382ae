using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    public class PermissionsViewModel : BaseViewModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public PermissionsViewModel()
        {
            resource = string.Empty;
            action = string.Empty;
        }

        /// <summary>
        /// 權限唯一識別碼
        /// </summary>
        public Guid permission_id { get; set; }

        /// <summary>
        /// 角色識別碼
        /// </summary>
        public Guid role_id { get; set; }

        /// <summary>
        /// 資源名稱
        /// </summary>
        public required string resource { get; set; }

        /// <summary>
        /// 操作類型
        /// </summary>
        public required string action { get; set; }
    }
}
