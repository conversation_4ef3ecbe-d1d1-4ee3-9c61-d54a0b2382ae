using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    public class TenantsViewModel : BaseViewModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public TenantsViewModel()
        {
            contact_email = string.Empty;
            name = string.Empty;
            legal_address = new AddressModel
            {
                street = string.Empty,
                city = string.Empty,
                country = string.Empty,
                postalCode = string.Empty
            };
        }

        /// <summary>
        /// 自動生成的UUID
        /// </summary>
        public Guid tenant_id { get; set; }

        /// <summary>
        /// 主要聯絡信箱
        /// </summary>
        public string contact_email { get; set; }

        /// <summary>
        /// 租戶法定名稱
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 帳號啟用狀態
        /// </summary>
        public bool is_active { get; set; }

        /// <summary>
        /// 計價幣別
        /// </summary>
        public Guid billing_currency { get; set; }

        /// <summary>
        /// 法律認證地址
        /// </summary>
        public AddressModel legal_address { get; set; }
    }
}
