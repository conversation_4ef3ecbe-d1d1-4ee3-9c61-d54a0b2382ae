using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    public class ApiUsageViewModel : BaseViewModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public ApiUsageViewModel()
        {
            request_ip = string.Empty;
        }

        /// <summary>
        /// 自動生成的UUID
        /// </summary>
        public Guid? usage_id { get; set; }

        /// <summary>
        /// API
        /// </summary>
        public Guid? api_id { get; set; }

        /// <summary>
        /// 金鑰識別碼
        /// </summary>
        public Guid key_id { get; set; }

        /// <summary>
        /// 請求來源IP地址
        /// </summary>
        public string request_ip { get; set; }

        /// <summary>
        /// API響應時間（毫秒）
        /// </summary>
        public decimal response_time { get; set; }

        /// <summary>
        /// HTTP狀態碼
        /// </summary>
        public int status_code { get; set; }
    }
}
