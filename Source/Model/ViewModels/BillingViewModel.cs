using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Model
{
    public class BillingViewModel : BaseViewModel
    {
        /// <summary>
        /// 默認構造函數，初始化所有必要屬性
        /// </summary>
        public BillingViewModel()
        {
            status = string.Empty;
        }

        /// <summary>
        /// 自動生成的UUID
        /// </summary>
        public Guid? record_id { get; set; }

        /// <summary>
        /// 租戶編號
        /// </summary>
        public Guid tenant_id { get; set; }

        /// <summary>
        /// 帳單金額
        /// </summary>
        public decimal amount { get; set; }

        /// <summary>
        /// 幣別識別碼
        /// </summary>
        public Guid currency_id { get; set; }

        /// <summary>
        /// 付款方式識別碼
        /// </summary>
        public Guid payment_method_id { get; set; }

        /// <summary>
        /// 帳單狀態
        /// </summary>
        public string status { get; set; }

        /// <summary>
        /// 帳單到期時間
        /// </summary>
        public DateTimeOffset due_date { get; set; }

        /// <summary>
        /// 帳單支付時間
        /// </summary>
        public DateTimeOffset paid_at { get; set; }
    }
}
