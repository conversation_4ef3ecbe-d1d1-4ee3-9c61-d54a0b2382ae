using Serilog;
using Serilog.Events;
using System;
using System.IO;
using System.Runtime.CompilerServices;

namespace Paas.Common
{
    /// <summary>
    /// 系統日誌管理器
    /// </summary>
    /// <remarks>
    /// 此靜態類別提供集中式的日誌記錄功能，使用 Serilog 作為底層框架。
    /// 支援不同級別的日誌記錄（除錯、資訊、警告、錯誤），並自動包含呼叫者的位置資訊。
    /// 日誌會同時輸出到控制台和按日期滾動的檔案中，檔案依日誌級別分類儲存。
    /// 此類別封裝了 Serilog 的功能，提供簡化且一致的介面，方便系統各部分進行日誌記錄。
    /// 所有日誌會以結構化格式儲存，便於後續分析和查詢。
    /// </remarks>
    public static class Logger
    {
        static Logger()
        {
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()  // 設定最小日誌層級為 Debug
                .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)  // 只記錄ASP.NET Core 的警告及以上級別
                .Enrich.FromLogContext()
                .WriteTo.Console()
                // Debug only
                .WriteTo.Logger(lc => lc
                    .Filter.ByIncludingOnly(le => le.Level == LogEventLevel.Debug)
                    .WriteTo.File("Logs/debug/debug_.log", rollingInterval: RollingInterval.Day))
                // Information only
                .WriteTo.Logger(lc => lc
                    .Filter.ByIncludingOnly(le => le.Level == LogEventLevel.Information)
                    .WriteTo.File("Logs/information/information_.log", rollingInterval: RollingInterval.Day))
                // Error only
                .WriteTo.Logger(lc => lc
                    .Filter.ByIncludingOnly(le => le.Level == LogEventLevel.Error)
                    .WriteTo.File("Logs/error/error_.log", rollingInterval: RollingInterval.Day))
                .CreateLogger();
        }

        /// <summary>
        /// 記錄除錯級別的日誌
        /// </summary>
        /// <remarks>
        /// 此方法用於記錄詳細的除錯資訊，通常僅在開發或疑難排解階段啟用。
        /// 會自動記錄呼叫此方法的檔案、方法和行號資訊，無需手動提供。
        /// 只有在系統日誌層級設為 Debug 或更低時，此方法才會實際寫入日誌。
        /// 除錯日誌會存放在 Logs/debug 目錄下，並依日期分檔。
        /// </remarks>
        /// <param name="msg">要記錄的日誌訊息</param>
        /// <param name="lineNumber">自動獲取的呼叫者行號</param>
        /// <param name="caller">自動獲取的呼叫者檔案路徑</param>
        /// <param name="memberName">自動獲取的呼叫者方法名稱</param>
        public static void Debug(string msg, [CallerLineNumber] int lineNumber = 0,[CallerFilePath] string caller = "", 
            [CallerMemberName] string memberName = "")
        {
            if (Log.IsEnabled(LogEventLevel.Debug))
            {
                Log.Debug(@"[{Namespace}.{Caller}.{MemberName}:{LineNumber}] - {Message}",
                    msg.GetType().Namespace,
                    Path.GetFileNameWithoutExtension(caller),
                    memberName,
                    lineNumber,
                    msg);
            }
        }

        /// <summary>
        /// 記錄資訊級別的日誌
        /// </summary>
        /// <remarks>
        /// 此方法用於記錄系統正常運作時的重要資訊，如操作完成、主要流程節點等。
        /// 會自動記錄呼叫此方法的檔案、方法和行號資訊，無需手動提供。
        /// 只有在系統日誌層級設為 Information 或更低時，此方法才會實際寫入日誌。
        /// 資訊日誌會存放在 Logs/information 目錄下，並依日期分檔。
        /// </remarks>
        /// <param name="msg">要記錄的日誌訊息</param>
        /// <param name="lineNumber">自動獲取的呼叫者行號</param>
        /// <param name="caller">自動獲取的呼叫者檔案路徑</param>
        /// <param name="memberName">自動獲取的呼叫者方法名稱</param>
        public static void Info(string msg, [CallerLineNumber] int lineNumber = 0,[CallerFilePath] string caller = "",
            [CallerMemberName] string memberName = "")
        {
            if (Log.IsEnabled(LogEventLevel.Information))
            {
                Log.Information(@"[{Namespace}.{Caller}.{MemberName}:{LineNumber}] - {Message}",
                    msg.GetType().Namespace,
                    Path.GetFileNameWithoutExtension(caller),
                    memberName,
                    lineNumber,
                    msg);
            }
        }

        /// <summary>
        /// 記錄警告級別的日誌
        /// </summary>
        /// <remarks>
        /// 此方法用於記錄可能導致問題但不影響系統正常運行的情況，如資源使用率過高、非致命性錯誤等。
        /// 會自動記錄呼叫此方法的檔案、方法和行號資訊，無需手動提供。
        /// 只有在系統日誌層級設為 Warning 或更低時，此方法才會實際寫入日誌。
        /// 警告日誌會同時輸出到主日誌檔案和控制台，方便及時注意潛在問題。
        /// </remarks>
        /// <param name="msg">要記錄的警告訊息</param>
        /// <param name="lineNumber">自動獲取的呼叫者行號</param>
        /// <param name="caller">自動獲取的呼叫者檔案路徑</param>
        /// <param name="memberName">自動獲取的呼叫者方法名稱</param>
        public static void Warn(string msg, [CallerLineNumber] int lineNumber = 0,[CallerFilePath] string caller = "",
            [CallerMemberName] string memberName = "")
        {
            if (Log.IsEnabled(LogEventLevel.Warning))
            {
                Log.Warning(@"[{Namespace}.{Caller}.{MemberName}:{LineNumber}] - {Message}",
                    msg.GetType().Namespace,
                    Path.GetFileNameWithoutExtension(caller),
                    memberName,
                    lineNumber,
                    msg);
            }
        }

        /// <summary>
        /// 記錄錯誤級別的日誌
        /// </summary>
        /// <remarks>
        /// 此方法用於記錄系統錯誤和異常情況，通常代表功能無法正常執行或發生意外情況。
        /// 除了錯誤訊息外，還會記錄完整的異常資訊，包含堆疊追蹤和內部異常。
        /// 會自動記錄呼叫此方法的檔案、方法和行號資訊，無需手動提供。
        /// 只有在系統日誌層級設為 Error 或更低時，此方法才會實際寫入日誌。
        /// 錯誤日誌會存放在 Logs/error 目錄下，並依日期分檔，方便追蹤和分析系統錯誤。
        /// </remarks>
        /// <param name="msg">錯誤的描述訊息</param>
        /// <param name="ex">要記錄的異常物件</param>
        /// <param name="lineNumber">自動獲取的呼叫者行號</param>
        /// <param name="caller">自動獲取的呼叫者檔案路徑</param>
        /// <param name="memberName">自動獲取的呼叫者方法名稱</param>
        public static void Error(string msg, Exception ex, [CallerLineNumber] int lineNumber = 0,[CallerFilePath] string caller = "",
            [CallerMemberName] string memberName = "")
        {
            if (Log.IsEnabled(LogEventLevel.Error))
            {
                Log.Error(ex, @"[{Namespace}.{Caller}.{MemberName}:{LineNumber}] - {Message}",
                    msg.GetType().Namespace,
                    Path.GetFileNameWithoutExtension(caller),
                    memberName,
                    lineNumber,
                    msg);
            }
        }
    }
}
