using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Common
{
    /// <summary>
    /// 常用工具方法類別
    /// </summary>
    /// <remarks>
    /// 此類別提供一系列常用的工具方法，主要處理日期時間格式轉換和標準化。
    /// 所有方法都設計為非侵入性的，不會修改原始資料，而是返回新的轉換結果。
    /// 此類別定義為靜態類，提供適合在整個應用程式中使用的跨功能方法。
    /// 主要用於期占傳輸、資料庫存取和 API 介面的日期時間轉換。
    /// </remarks>
    public static class Utils
    {
        /// <summary>
        /// 將 DateTimeOffset 轉換為 UTC 格式的字串
        /// </summary>
        /// <remarks>
        /// 此方法將 DateTimeOffset 類型的時間轉換為符合 ISO 8601 標準的 UTC 時間字串。
        /// 結果字串格式為 'yyyy-MM-ddTHH:mm:ssZ'，其中 Z 表示 UTC 時區。
        /// 此方法就可讓不同時區的時間表示方式一致，利於資料庫存取、API 傳輸和日誌記錄等場景。
        /// 這是一個擴充方法，可直接在 DateTimeOffset 物件上呼叫。
        /// </remarks>
        /// <param name="dt">要轉換的 DateTimeOffset 時間</param>
        /// <returns>符合 ISO 8601 標準的 UTC 格式字串</returns>
        public static string ToDateStr(this DateTimeOffset dt)
        {
            return ToDateStr(dt.UtcDateTime);
        }

        /// <summary>
        /// 將 DateTime 轉換為 UTC 格式的字串
        /// </summary>
        /// <remarks>
        /// 此方法將 DateTime 類型的時間轉換為符合 ISO 8601 標準的字串表示形式。
        /// 結果字串格式為 'yyyy-MM-ddTHH:mm:ssZ'，其中 Z 表示 UTC 時區。
        /// 即使原始 DateTime 不是 UTC 時間，也會使用若其是 UTC 時間的格式輸出，因此執行此方法前應確保時間已適當轉換。
        /// 這是一個擴充方法，可直接在 DateTime 物件上呼叫。
        /// </remarks>
        /// <param name="dt">要轉換的 DateTime 時間</param>
        /// <returns>符合 ISO 8601 標準的字串，以 Z 結尾表示 UTC 時間</returns>
        public static string ToDateStr(this DateTime dt)
        {
            return $"{dt:s}Z";
        }

        /// <summary>
        /// 將任意時區的時間標準化為 UTC 時間
        /// </summary>
        /// <remarks>
        /// 此方法檢查輸入的 DateTimeOffset 是否已經是 UTC 時間，如果是，則直接返回輸入值。
        /// 如果輸入的時間不是 UTC 時間（Offset 不等於 TimeSpan.Zero），則將其轉換為等價的 UTC 時間。
        /// 此方法確保程式中使用的時間經過標準化，不論來源為何，都可以一致以 UTC 時間處理。
        /// 這對於跨時區應用程式的時間計算、排序和比較特別重要。
        /// </remarks>
        /// <param name="inputDate">要標準化的時間</param>
        /// <returns>等價的 UTC 時間（DateTimeOffset 格式且 Offset 為 TimeSpan.Zero）</returns>
        public static DateTimeOffset NormalizeToUtc(DateTimeOffset inputDate)
        {
            // 如果輸入的時間已經是 UTC，則直接回傳
            if (inputDate.Offset == TimeSpan.Zero)
            {
                return inputDate;
            }

            // 如果不是 UTC，轉換為 UTC
            return inputDate.ToUniversalTime();
        }
    }
}
