services:
  paas-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: paas-api-dev
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      # Swagger UI 控制 (開發環境預設啟用)
      - EnableSwagger=true
      # 資料庫連線字串 (請根據實際情況調整)
      - ConnectionStrings__PostgreConnection=Host=postgres;Database=paasdb;Username=paasuser;Password=paaspass
      # JWT 設定 (開發環境使用較短的密鑰)
      - JwtSettings__SecretKey=dev-secret-key-for-development-only-32-chars
      - JwtSettings__Issuer=PaasAPI-Dev
      - JwtSettings__Audience=PaasClient-Dev
      - JwtSettings__ExpirationMinutes=120
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - paas-network
    volumes:
      # 掛載 log 目錄到主機，方便查看日誌
      - ./logs:/app/log
      # 開發環境可以掛載原始碼進行熱重載 (可選)
      # - ./Paas:/app/source:ro

  postgres:
    image: postgres:15-alpine
    container_name: paas-postgres-dev
    environment:
      - POSTGRES_DB=paasdb
      - POSTGRES_USER=paasuser
      - POSTGRES_PASSWORD=paaspass
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    networks:
      - paas-network

volumes:
  postgres_dev_data:

networks:
  paas-network:
    driver: bridge
