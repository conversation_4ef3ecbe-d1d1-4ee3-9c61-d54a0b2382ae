version: '3.8'

services:
  paas-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: paas-api
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      # Swagger UI 控制 (設為 true 啟用，false 停用)
      - EnableSwagger=true
      # 資料庫連線字串 (請根據實際情況調整)
      - ConnectionStrings__PostgreConnection=Host=postgres;Database=paasdb;Username=paasuser;Password=paaspass
      # JWT 設定 (請使用安全的密鑰)
      - JwtSettings__SecretKey=your-super-secret-jwt-key-here-must-be-at-least-32-characters
      - JwtSettings__Issuer=PaasAPI
      - JwtSettings__Audience=PaasClient
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - paas-network

  postgres:
    image: postgres:15-alpine
    container_name: paas-postgres
    environment:
      - POSTGRES_DB=paasdb
      - POSTGRES_USER=paasuser
      - POSTGRES_PASSWORD=paaspass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    networks:
      - paas-network

volumes:
  postgres_data:

networks:
  paas-network:
    driver: bridge
