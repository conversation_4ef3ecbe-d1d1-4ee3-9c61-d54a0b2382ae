using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using Paas.Service;

namespace Paas.Controllers
{
    /// <summary>
    /// 登入身分驗證控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理使用者的登入身分驗證請求，並發放 JWT 令牌。
    /// 提供了登入處理和測試受保護組件的端點。
    /// 基於 JWT（JSON Web Token）的驗證機制，為穩健、可擴展的身分驗證系統提供基礎。
    /// 登入成功後，使用者將獲得一個 JWT 令牌，用於後續存取受保護的 API 端點。
    /// 作為系統安全的前線，本控制器需要定期的安全審核與升級，以確保其符合最新的安全最佳做法。
    /// </remarks>
    [Route("v1/[controller]")]
    [ApiController]
    public class LoginController : ControllerBase
    {
        /// <summary>
        /// 用於記錄控制器日誌的記錄器實例
        /// </summary>
        private readonly ILogger<LoginController> logger;
        
        /// <summary>
        /// JWT 令牌生成與驗證的服務實例
        /// </summary>
        private readonly IJwtService jwtService;

        /// <summary>
        /// 初始化 LoginController 的新實例
        /// </summary>
        /// <param name="logger">用於記錄控制器操作日誌的記錄器</param>
        /// <param name="jwtService">用於生成和驗證 JWT 令牌的服務</param>
        public LoginController(ILogger<LoginController> logger, IJwtService jwtService)
        {
            this.logger = logger;
            this.jwtService = jwtService;
        }

        /// <summary>
        /// 處理使用者登入認證並發放 JWT 令牌
        /// </summary>
        /// <remarks>
        /// 此方法接收使用者的身分驗證資訊，包括電子郵件和密碼，並進行驗證。
        /// 當驗證成功時，方法會調用 JWT 服務產生一個新的 JWT 令牌。
        /// 當驗證失敗時，方法會返回 401 Unauthorized 狀態碼和適當的錯誤訊息。
        /// 注意：在目前的實作中，驗證邏輯為模擬實作，在正式產品環境中應由資料庫驗證機制替代。
        /// </remarks>
        /// <param name="request">包含使用者登入資訊的請求模型，包括電子郵件和密碼</param>
        /// <returns>
        /// 成功時：200 OK 狀態碼和包含 JWT 令牌的響應主體。
        /// 失敗時：401 Unauthorized 狀態碼和錯誤訊息。
        /// </returns>
        [HttpPost]
        public IActionResult Login([FromBody] LoginRequest request)
        {
            // 模擬驗證帳號 (實務上應從 DB 查詢)
            if (request.Email == "string" && request.Password == "string")
            {
                var token = jwtService.GenerateToken(request.Email);
                return Ok(new { Token = token });
            }
            return Unauthorized(new { Message = "帳號或密碼錯誤" });
        }

        /// <summary>
        /// 測試受 JWT 保護的 API 端點
        /// </summary>
        /// <remarks>
        /// 此端點只能由已經進行驗證的使用者進行存取，需要在請求相關的 Authorization 標頭中包含有效的 JWT 令牌。
        /// 主要用於測試和驗證 JWT 驗證機制是否正常運作。
        /// 如果請求中沒有有效的 JWT 令牌，或者令牌已過期，系統會自動返回 401 Unauthorized 狀態碼。
        /// </remarks>
        /// <returns>成功時返回 200 OK 狀態碼和確認訊息</returns>
        [Authorize]  // 需要 JWT Token 才能訪問
        [HttpGet("protected")]
        public IActionResult ProtectedEndpoint()
        {
            return Ok(new { Message = "你已成功訪問受保護的 API！" });
        }
    }
}
