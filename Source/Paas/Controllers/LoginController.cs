using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using Paas.Service;

namespace Paas.Controllers
{
    /// <summary>
    /// 登入身分驗證控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理使用者的登入身分驗證請求，並發放 JWT 令牌。
    /// 提供了登入處理和測試受保護組件的端點。
    /// 基於 JWT（JSON Web Token）的驗證機制，為穩健、可擴展的身分驗證系統提供基礎。
    /// 登入成功後，使用者將獲得一個 JWT 令牌，用於後續存取受保護的 API 端點。
    /// 作為系統安全的前線，本控制器需要定期的安全審核與升級，以確保其符合最新的安全最佳做法。
    /// </remarks>
    [Route("v1/[controller]")]
    [ApiController]
    public class LoginController : ControllerBase
    {
        /// <summary>
        /// 用於記錄控制器日誌的記錄器實例
        /// </summary>
        private readonly ILogger<LoginController> logger;
        
        /// <summary>
        /// JWT 令牌生成與驗證的服務實例
        /// </summary>
        private readonly IJwtService jwtService;

        /// <summary>
        /// 身分驗證服務實例
        /// </summary>
        private readonly IAuthService authService;

        /// <summary>
        /// 初始化 LoginController 的新實例
        /// </summary>
        /// <param name="logger">用於記錄控制器操作日誌的記錄器</param>
        /// <param name="jwtService">用於生成和驗證 JWT 令牌的服務</param>
        /// <param name="authService">用於使用者身分驗證的服務</param>
        public LoginController(ILogger<LoginController> logger, IJwtService jwtService, IAuthService authService)
        {
            this.logger = logger;
            this.jwtService = jwtService;
            this.authService = authService;
        }

        /// <summary>
        /// 處理使用者登入認證並發放 JWT 令牌
        /// </summary>
        /// <remarks>
        /// 此方法接收使用者的身分驗證資訊，包括電子郵件和密碼，並進行驗證。
        /// 使用 AuthService 進行真實的資料庫驗證，包括 BCrypt 密碼比對。
        /// 當驗證成功時，方法會調用 JWT 服務產生一個新的 JWT 令牌。
        /// 當驗證失敗時，方法會返回 401 Unauthorized 狀態碼和適當的錯誤訊息。
        /// 支援預設管理員帳號 (<EMAIL> / gssadmin) 和其他資料庫中的使用者。
        /// </remarks>
        /// <param name="request">包含使用者登入資訊的請求模型，包括電子郵件和密碼</param>
        /// <returns>
        /// 成功時：200 OK 狀態碼和包含 JWT 令牌的響應主體。
        /// 失敗時：401 Unauthorized 狀態碼和錯誤訊息。
        /// </returns>
        [HttpPost]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                // 使用 AuthService 進行真實的資料庫驗證
                var user = await authService.ValidateUserAsync(request.Email, request.Password);

                if (user != null)
                {
                    // 驗證成功，生成 JWT Token
                    var token = jwtService.GenerateToken(user.email);

                    logger.LogInformation("使用者 {Email} 登入成功", request.Email);

                    return Ok(new {
                        token = token,
                        user = new {
                            user_id = user.user_id,
                            username = user.username,
                            email = user.email,
                            role_id = user.role_id
                        }
                    });
                }

                // 驗證失敗，記錄日誌並返回錯誤
                logger.LogWarning("使用者 {Email} 登入失敗", request.Email);
                return Unauthorized(new { message = "帳號或密碼錯誤" });
            }
            catch (Exception ex)
            {
                // 發生異常，記錄錯誤並返回通用錯誤訊息
                logger.LogError(ex, "登入過程中發生錯誤，使用者: {Email}", request.Email);
                return StatusCode(500, new { message = "登入過程中發生錯誤，請稍後再試" });
            }
        }

        /// <summary>
        /// 測試受 JWT 保護的 API 端點
        /// </summary>
        /// <remarks>
        /// 此端點只能由已經進行驗證的使用者進行存取，需要在請求相關的 Authorization 標頭中包含有效的 JWT 令牌。
        /// 主要用於測試和驗證 JWT 驗證機制是否正常運作。
        /// 如果請求中沒有有效的 JWT 令牌，或者令牌已過期，系統會自動返回 401 Unauthorized 狀態碼。
        /// </remarks>
        /// <returns>成功時返回 200 OK 狀態碼和確認訊息</returns>
        [Authorize]  // 需要 JWT Token 才能訪問
        [HttpGet("protected")]
        public IActionResult ProtectedEndpoint()
        {
            return Ok(new { Message = "你已成功訪問受保護的 API！" });
        }
    }
}
