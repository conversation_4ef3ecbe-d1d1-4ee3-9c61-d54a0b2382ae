using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Paas.Model;
using Paas.Service;
using System.Net.Http.Json;
using System.Reflection;
using System.Text.Json.Serialization;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Paas.Controllers
{
    /// <summary>
    /// 租戶管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與租戶（Tenants）相關的操作，包括查詢、新增、更新和刪除租戶資料。
    /// 所有端點皆使用版本管理，以 `/v1/` 作為前綴，並需要適當的認證與授權。
    /// 實作 RESTful API 設計原則，提供統一的介面處理租戶資源。
    /// </remarks>
    [Route("v1/[controller]")]
    [ApiController]
    [Authorize]
    public class TenantsController : ControllerBase
    {
        /// <summary>
        /// 租戶服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理租戶相關的業務邏輯，如查詢、新增、更新和刪除租戶資料。
        /// </remarks>
        protected readonly ITenantsService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化租戶控制器的新執行個體
        /// </summary>
        /// <param name="service">租戶服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化租戶服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public TenantsController(ITenantsService service,IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得所有租戶列表
        /// </summary>
        /// <param name="is_active">租戶狀態過濾條件，若為 null 則不過濾</param>
        /// <param name="model">基礎查詢模型，包含分頁和排序參數</param>
        /// <returns>包含租戶列表的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢系統中所有的租戶資料，支援透過 `is_active` 條件進行過濾，
        /// 並支援分頁和排序功能，適用於管理介面的租戶列表頁面。
        /// 
        /// 範例請求：GET /v1/tenants?is_active=true&amp;limit=20&amp;offset=0
        /// 
        /// 成功回應：200 OK 與租戶資料陣列
        /// 錯誤回應：401 Unauthorized 或 403 Forbidden
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> Get([FromQuery] bool? is_active, [FromQuery] BaseQueryModel model)
        {
            // 進行model Mapping處理
            TenantsModel queryModel = mapper.Map<TenantsModel>(model);
            // 放入 is_active
            queryModel.is_active = is_active;
            // 取得租戶列表資料
            IList<TenantsModel> data = await service.GetTenantsDataList(queryModel);

            return Ok(mapper.Map<IList<TenantsViewModel>>(data));
        }

        /// <summary>
        /// 根據租戶 ID 獲取特定租戶資訊
        /// </summary>
        /// <param name="tenant_id">要查詢的租戶 ID</param>
        /// <returns>包含特定租戶詳細資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端根據 tenant_id 查詢特定租戶的詳細資訊。
        /// 
        /// 範例請求：GET /v1/tenants/b836f4ec-3b77-4972-bccf-1234567890ab
        /// 
        /// 成功回應：200 OK 與租戶詳細資訊
        /// 錯誤回應：404 Not Found 或 403 Forbidden
        /// </remarks>
        [HttpGet("{tenant_id}")]
        public async Task<IActionResult> GetDataById(Guid tenant_id)
        {
            // 取得租戶資料
            TenantsModel data = await service.GetTenantsData(tenant_id);

            if (data == null)
            {
                return NotFound();
            }

            return Ok(mapper.Map<TenantsViewModel>(data));
        }

        /// <summary>
        /// 建立新租戶
        /// </summary>
        /// <param name="model">包含新租戶資料的建立模型</param>
        /// <returns>包含新建立租戶資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端建立新的租戶資料。客戶端需要提供必要的租戶資訊，
        /// 如名稱、聯絡郵箱、帳單貨幣和法律地址等。
        /// 
        /// 範例請求：POST /v1/tenants 與 JSON 格式的租戶資料
        /// 
        /// 成功回應：201 Created 與新建立的租戶資訊
        /// 錯誤回應：400 Bad Request 或 401 Unauthorized / 403 Forbidden
        /// </remarks>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] TenantsCreateModel model)
        {
            // 進行model Mapping處理
            TenantsModel queryModel = mapper.Map<TenantsModel>(model);

            // 判斷欄位
            if (queryModel == null)
            {
                return BadRequest("Invalid data.");
            }

            // 新增租戶資料
            TenantsModel data = await service.CreateTenantsData(queryModel);

            if (data is null) return StatusCode(500, "新增失敗");

            return CreatedAtAction(nameof(GetDataById), new { tenant_id = data.tenant_id }, mapper.Map<TenantsViewModel>(data));
        }

        /// <summary>
        /// 更新指定租戶資訊
        /// </summary>
        /// <param name="tenant_id">要更新的租戶 ID</param>
        /// <param name="model">包含更新資料的模型</param>
        /// <returns>包含更新後租戶資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端更新指定租戶的資訊。支援部分欄位更新，
        /// 僅需提供要更新的欄位，未提供的欄位將保持原值不變。
        /// 
        /// 範例請求：PUT /v1/tenants/{tenant_id} 與 JSON 格式的更新資料
        /// 
        /// 成功回應：200 OK 與更新後的租戶資訊
        /// 錯誤回應：404 Not Found 或 400 Bad Request
        /// </remarks>
        [HttpPut("{tenant_id}")]
        public async Task<IActionResult> Update(Guid tenant_id, [FromBody] TenantsUpdateModel model)
        {
            // 進行model Mapping處理
            TenantsModel queryModel = mapper.Map<TenantsModel>(model);

            // 檢核更新欄位
            if (queryModel == null)
            {
                return BadRequest("Invalid data.");
            }

            // 放入tenant_id
            queryModel.tenant_id = tenant_id;

            // 更新租戶資料
            TenantsModel data = await service.UpdateTenantsData(queryModel);

            if (data is null) return StatusCode(500, "查無資料，更新失敗");

            return Ok(mapper.Map<TenantsViewModel>(data));
        }

        /// <summary>
        /// 刪除指定租戶或將其標記為停用
        /// </summary>
        /// <param name="tenant_id">要刪除或停用的租戶 ID</param>
        /// <returns>表示操作結果的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端刪除指定的租戶，或依據業務需求將其標記為停用。
        /// 實際實作中是透過更新租戶的 is_active 狀態為 false 來實現「軟刪除」，
        /// 而非直接從資料庫中移除資料，以保留歷史記錄。
        /// 
        /// 範例請求：DELETE /v1/tenants/{tenant_id}
        /// 
        /// 成功回應：204 No Content
        /// 錯誤回應：404 Not Found 或 403 Forbidden
        /// </remarks>
        [HttpDelete("{tenant_id}")]
        public async Task<IActionResult> Delete(Guid tenant_id)
        {
            // 更新使用者狀態
            bool isDeleted =  await service.UpdateTenantsStatus(tenant_id);

            if (!isDeleted) return NotFound();

            return NoContent();
        }
    }
}
