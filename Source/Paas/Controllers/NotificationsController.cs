using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;

namespace Paas.Controllers
{
    /// <summary>
    /// 通知管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與通知（Notifications）相關的操作，包括查詢、新增和管理通知。
    /// 所有端點皆使用版本管理，以 `/v1/` 作為前綴，並需要適當的身分驗證與授權。
    /// 實作 RESTful API 設計原則，提供統一的介面處理通知資源。
    /// 通知功能用於向用戶發送重要的系統訊息，如帳單到期提醒、安全警報和服務狀態更新等。
    /// </remarks>
    [Route("v1/[controller]")]
    [ApiController]
    [Authorize]
    public class NotificationsController : ControllerBase
    {
        /// <summary>
        /// 通知服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理通知相關的業務邏輯，如查詢、新增和管理通知。
        /// </remarks>
        protected readonly INotificationsService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化通知控制器的新執行個體
        /// </summary>
        /// <param name="service">通知服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化通知服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public NotificationsController(INotificationsService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 獲取所有通知
        /// </summary>
        /// <param name="queryModel">包含查詢條件的模型，如接收者、狀態和日期範圍等</param>
        /// <returns>包含通知清單的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢系統中的通知資訊，支援多種條件過濾。
        /// 包括按接收者、狀態（已讀/未讀）、類型和時間範圍等條件進行過濾。
        /// 
        /// 範例請求：GET /v1/notifications?recipient_id=12345&amp;status=unread&amp;from_date=2023-01-01T00:00:00Z&amp;limit=20
        /// 
        /// 成功回應：200 OK 與通知資料陣列
        /// 錯誤回應：401 Unauthorized 或 403 Forbidden
        /// 
        /// 系統支援分頁和排序功能，以便於管理大量通知資料。
        /// 預設情況下，通知按建立時間降序排列，最新的通知會先顯示。
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> GetNotifications([FromQuery] NotificationsQueryModel queryModel)
        {
            NotificationsModel model = mapper.Map<NotificationsModel>(queryModel);
            IList<NotificationsModel> data = await service.GetNotifications(model);
            return Ok(mapper.Map<IList<NotificationsViewModel>>(data));
        }

        /// <summary>
        /// 根據通知 ID 獲取特定通知的詳細資訊
        /// </summary>
        /// <param name="notification_id">要查詢的通知的唯一識別碼</param>
        /// <returns>包含特定通知詳細資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端根據 notification_id 查詢特定通知的詳細資料。
        /// 如果用戶點選基本通知列表中的通知後，可使用此端點查詢完整的通知詳情。
        /// 
        /// 範例請求：GET /v1/notifications/aaaabbbb-cccc-dddd-eeee-ffffffffffff
        /// 
        /// 成功回應：200 OK 與通知詳細資訊
        /// 錯誤回應：404 Not Found
        /// 
        /// 查看特定通知時，系統可根據設定自動將該通知標記為已讀狀態。
        /// </remarks>
        [HttpGet("{notification_id}")]
        public async Task<IActionResult> GetNotification(Guid notification_id)
        {
            NotificationsModel? data = await service.GetNotification(notification_id);

            if (data is null) return NotFound();

            return Ok(mapper.Map<NotificationsViewModel>(data));
        }

        /// <summary>
        /// 創建新的通知
        /// </summary>
        /// <param name="model">包含新通知資料的建立模型</param>
        /// <returns>包含新建立通知資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端建立新的通知資料。客戶端需要提供必要的通知資訊，
        /// 如標題、內容、接收者 ID、類型和優先等級等。
        /// 
        /// 範例請求：POST /v1/notifications 與 JSON 格式的通知資料
        /// 
        /// 成功回應：201 Created 與新建立的通知資訊
        /// 錯誤回應：400 Bad Request 或 500 Internal Server Error
        /// 
        /// 此端點充當多種系統事件的觸發點。從自動化流程到手動通知創建，都可使用此 API。
        /// 通知擁有不同的級別，從擦遙通知到緊急警報，以反映不同的重要性和緊急性。
        /// </remarks>
        [HttpPost]
        public async Task<IActionResult> InsertNotification(NotificationsCreateModel model)
        {
            NotificationsModel? createdData =
                await service.InsertNotification(mapper.Map<NotificationsModel>(model));

            if (createdData is null) return StatusCode(500, "新增失敗");

            return StatusCode(201, mapper.Map<NotificationsViewModel>(createdData));
        }
    }
}
