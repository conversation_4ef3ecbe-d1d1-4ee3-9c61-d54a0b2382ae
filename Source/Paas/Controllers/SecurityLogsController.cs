using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;

namespace Paas.Controllers
{
    /// <summary>
    /// 安全日誌管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與安全日誌（Security Logs）相關的操作，包括查詢和存取特定安全日誌。
    /// 所有端點皆使用版本管理，以 `/v1/` 作為前綴，並需要適當的身分驗證與授權。
    /// 實作 RESTful API 設計原則，提供統一的介面處理安全日誌資源。
    /// 安全日誌系統用於記錄和審核系統中的安全相關活動，如登入嘗試、權限定澩變更和敏感資源存取等，是系統安全和審核合規性的重要組成部分。
    /// </remarks>
    [Route("v1/security_logs")]
    [ApiController]
    [Authorize]
    public class SecurityLogsController : ControllerBase
    {
        /// <summary>
        /// 安全日誌服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理安全日誌相關的業務邏輯，如查詢和存取安全日誌資料。
        /// </remarks>
        protected readonly ISecurityLogsService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化安全日誌控制器的新執行個體
        /// </summary>
        /// <param name="service">安全日誌服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化安全日誌服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public SecurityLogsController(ISecurityLogsService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 獲取所有安全日誌，可依租戶、事件類型、時間區間等條件篩選與分頁
        /// </summary>
        /// <param name="queryModel">包含查詢條件的模型，如租戶 ID、事件類型和時間範圍等過濾條件</param>
        /// <returns>包含安全日誌清單的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢系統中的安全日誌資料，支援多種過濾條件，
        /// 如租戶 ID、事件類型、時間範圍等，適用於管理介面的安全日誌列表頁面和審核報告。
        /// 
        /// 範例請求：GET /v1/security_logs?tenant_id=abcd1234&amp;event_type_id=login_attempt
        /// 
        /// 成功回應：200 OK 與安全日誌資料陣列
        /// 錯誤回應：401 Unauthorized 或 403 Forbidden
        /// 
        /// 從安全觀點來看，此端點應受到嚴格的存取控制，僅允許有適當權限的管理者存取。
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> GetSecurityLogs([FromQuery] SecurityLogsQueryModel queryModel)
        {
            SecurityLogsModel model = mapper.Map<SecurityLogsModel>(queryModel);
            IList<SecurityLogsModel> data = await service.GetSecurityLogs(model);
            return Ok(mapper.Map<IList<SecurityLogsViewModel>>(data));
        }

        /// <summary>
        /// 根據日誌 ID 獲取特定安全日誌的詳細資料
        /// </summary>
        /// <param name="log_id">要查詢的安全日誌的唯一識別碼</param>
        /// <returns>包含特定安全日誌詳細資料的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端根據 log_id 查詢特定安全日誌的詳細資料。
        /// 安全日誌詳細資料包含事件類型、時間、相關用戶、IP 地址、事件詳情和其他相關文章。
        /// 
        /// 範例請求：GET /v1/security_logs/b836f4ec-3b77-4972-bccf-1234567890ab
        /// 
        /// 成功回應：200 OK 與安全日誌的詳細資料
        /// 錯誤回應：404 Not Found 或 403 Forbidden
        /// 
        /// 從安全觀點來看，存取特定安全日誌的操作本身也應被記錄，以便追蹤誰在查看敏感的安全事件。
        /// </remarks>
        [HttpGet("{log_id}")]
        public async Task<IActionResult> GetSecurityLog(Guid log_id)
        {
            SecurityLogsModel? data = await service.GetSecurityLog(log_id);

            if (data is null) return NotFound();

            return Ok(mapper.Map<SecurityLogsViewModel>(data));
        }
    }
}
