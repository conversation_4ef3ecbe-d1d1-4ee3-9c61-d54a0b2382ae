using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;

namespace Paas.Controllers
{
    /// <summary>
    /// 角色管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與角色（Roles）相關的操作，包括查詢和新增角色資料。
    /// 所有端點皆使用版本管理，以 `/api/` 作為前綴，並需要適當的身分驗證與授權。
    /// 實作 RESTful API 設計原則，提供統一的介面處理角色資源。
    /// 角色管理為系統提供了以角色為基礎的存取控制機制，每個角色可與特定的權限集合關聯。
    /// </remarks>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RolesController : ControllerBase
    {
        /// <summary>
        /// 角色服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理角色相關的業務邏輯，如查詢、建立和管理角色。
        /// </remarks>
        private readonly IRolesService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化角色控制器的新執行個體
        /// </summary>
        /// <param name="service">角色服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化角色服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public RolesController(IRolesService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得所有角色資料
        /// </summary>
        /// <returns>包含角色清單的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點用於獲取系統中所有可用的角色資訊。
        /// 適用於需要顯示角色選項的場景，如用戶管理頁面、權限設定介面等。
        /// 
        /// 範例請求：GET /api/roles
        /// 
        /// 成功回應：200 OK 與角色資料陣列
        /// 錯誤回應：404 Not Found 或 401 Unauthorized / 403 Forbidden
        /// 
        /// 返回的角色資料包含每個角色的完整資訊，但不包含特定的權限細節。
        /// 如需查詢特定角色的權限詳情，應使用權限相關的 API。
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            IList<RolesModel> result = await service.GetRolesList();

            if (result.Count == 0)
            {
                return NotFound();
            }

            return Ok(mapper.Map<IList<RolesViewModel>>(result));
        }

        /// <summary>
        /// 創建新的角色資料
        /// </summary>
        /// <param name="model">包含新角色資料的建立模型</param>
        /// <returns>包含新建立角色資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端建立新的角色資料。客戶端需要提供必要的角色資訊，
        /// 如名稱、描述和初始權限設定等。
        /// 
        /// 範例請求：POST /api/roles 與 JSON 格式的角色資料
        /// 
        /// 成功回應：201 Created 與新建立的角色資訊
        /// 錯誤回應：400 Bad Request 或 500 Internal Server Error
        /// 
        /// 創建角色時，應仔細考慮權限分配，確保遵循最小權限原則，僅授予完成特定功能所需的最小權限集合。
        /// 新建立的角色可立即指派給用戶，並在整個系統中生效。
        /// </remarks>
        [HttpPost]
        public async Task<IActionResult> Create(RolesCreateModel model)
        {
            // 進行model Mapping處理
            RolesModel queryModel = mapper.Map<RolesModel>(model);
            // 新增角色資料
            RolesModel result = await service.CreateRolesData(queryModel);

            if (result is null) return StatusCode(500, "新增失敗");

            return StatusCode(201, mapper.Map<RolesViewModel>(result));
        }
    }
}
