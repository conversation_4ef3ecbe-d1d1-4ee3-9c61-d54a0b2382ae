using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;
using System.Reflection;

namespace Paas.Controllers
{
    /// <summary>
    /// API 閥道管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與 API 閥道管理相關的操作，包括查詢、新增、更新和刪除 API 服務。
    /// 所有端點皆使用版本管理，以 `/v1/` 作為前綴，並需要適當的認證與授權。
    /// 實作 RESTful API 設計原則，提供統一的介面處理 API 服務資源。
    /// </remarks>
    [Route("v1/[controller]")]
    [ApiController]
    [Authorize]
    public class ApisController : ControllerBase
    {
        /// <summary>
        /// API 服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理 API 相關的業務邏輯，如查詢、新增、更新和刪除 API 服務資料。
        /// </remarks>
        protected readonly IApisService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化 API 控制器的新執行個體
        /// </summary>
        /// <param name="service">API 服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化 API 服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public ApisController(IApisService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得 API 服務清單資料
        /// </summary>
        /// <param name="tenant_id">租戶 ID 過濾條件，若為 null 則不過濾</param>
        /// <param name="model">基礎查詢模型，包含分頁和排序參數</param>
        /// <returns>包含 API 服務列表的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢系統中的 API 服務列表，支援透過 `tenant_id` 條件進行過濾，
        /// 並支援分頁和排序功能，適用於管理介面的 API 服務列表頁面。
        /// 
        /// 範例請求：GET /v1/apis?tenant_id=b836f4ec-3b77-4972-bccf-1234567890ab&amp;limit=20&amp;offset=0
        /// 
        /// 成功回應：200 OK 與 API 資料陣列
        /// 錯誤回應：401 Unauthorized 或 403 Forbidden
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> Get([FromQuery] Guid? tenant_id, [FromQuery] BaseQueryModel model)
        {
            // 進行model Mapping處理
            ApisModel queryModel = mapper.Map<ApisModel>(model);
            // 放入 tenant_id
            queryModel.tenant_id = tenant_id;

            // 取得租戶列表資料
            List<ApisModel> data = await service.GetApisDataList(queryModel);

            // 回傳資料處理
            List<ApisViewModel> result = mapper.Map<List<ApisViewModel>>(data);

            return Ok(result);
        }

        /// <summary>
        /// 根據 API ID 取得特定 API 服務資料
        /// </summary>
        /// <param name="api_id">要查詢的 API ID</param>
        /// <returns>包含特定 API 服務詳細資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端根據 API ID 查詢特定 API 服務的詳細資訊。
        /// 
        /// 範例請求：GET /v1/apis/967f4ec0-b235-48fa-bddf-9876543210aa
        /// 
        /// 成功回應：200 OK 與 API 服務詳細資訊
        /// 錯誤回應：404 Not Found 或 403 Forbidden
        /// </remarks>
        [HttpGet("{api_id}")]
        public async Task<IActionResult> GetById(Guid api_id)
        {
            // 取得此筆API資料
            ApisModel? data = await service.GetApisData(api_id);

            if (data == null)
            {
                return NotFound();
            }

            // 進行model Mapping處理
            ApisViewModel result = mapper.Map<ApisViewModel>(data);

            return Ok(result);
        }

        /// <summary>
        /// 新增 API 服務資料
        /// </summary>
        /// <param name="model">包含新 API 服務資料的建立模型</param>
        /// <returns>包含新建立 API 服務資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端為指定租戶建立新的 API 服務。客戶端需要提供必要的 API 資訊，
        /// 如租戶 ID、名稱、描述、版本和流量限制等。
        /// 
        /// 範例請求：POST /v1/apis 與 JSON 格式的 API 服務資料
        /// 
        /// 成功回應：201 Created 與新建立的 API 服務資訊
        /// 錯誤回應：400 Bad Request 或 401 Unauthorized / 403 Forbidden
        /// </remarks>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] ApisCreateModel model)
        {
            // 進行model Mapping處理
            ApisModel queryModel = mapper.Map<ApisModel>(model);
            // 新增API服務
            ApisModel? data = await service.CreateApisData(queryModel);

            if (data is null) return StatusCode(500, "新增失敗");

            return CreatedAtAction(nameof(GetById), new { api_id = data.api_id }, mapper.Map<ApisViewModel>(data));
        }

        /// <summary>
        /// 更新特定 API 服務資料
        /// </summary>
        /// <param name="api_id">要更新的 API ID</param>
        /// <param name="model">包含更新資料的模型</param>
        /// <returns>包含更新後 API 服務資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端更新特定 API 的資訊，如版本、流量限制、啟用狀態等。
        /// 支援部分欄位更新，僅需提供要更新的欄位，未提供的欄位將保持原值不變。
        /// 
        /// 範例請求：PUT /v1/apis/967f4ec0-b235-48fa-bddf-9876543210aa 與 JSON 格式的更新資料
        /// 
        /// 成功回應：200 OK 與更新後的 API 服務資訊
        /// 錯誤回應：404 Not Found 或 400 Bad Request
        /// </remarks>
        [HttpPut("{api_id}")]
        public async Task<IActionResult> Update(Guid api_id, [FromBody] ApisUpdateModel model)
        {
            // 進行model Mapping處理
            ApisModel queryModel = mapper.Map<ApisModel>(model);
            // ap_id
            queryModel.api_id = api_id;
            // 更新API服務
            ApisModel? data = await service.UpdateApisData(queryModel);

            if (data is null) return StatusCode(500, "查無資料，更新失敗");

            // 進行model Mapping處理
            ApisViewModel result = mapper.Map<ApisViewModel>(data);

            return Ok(result);
        }

        /// <summary>
        /// 刪除特定 API 服務
        /// </summary>
        /// <param name="api_id">要刪除的 API ID</param>
        /// <returns>表示操作結果的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端刪除指定的 API 服務，或不同實作可考慮將其標記為停用。
        /// 實際實作中是透過更新 API 的 is_active 狀態來實現「軟刪除」。
        /// 
        /// 範例請求：DELETE /v1/apis/967f4ec0-b235-48fa-bddf-9876543210aa
        /// 
        /// 成功回應：204 No Content
        /// 錯誤回應：404 Not Found 或 403 Forbidden
        /// </remarks>
        [HttpDelete("{api_id}")]
        public async Task<IActionResult> Delete(Guid api_id)
        {
            // 更新API服務狀態
            bool isDeleted = await service.UpdateApisStatus(api_id);

            if (!isDeleted) return NotFound();

            return Ok();
        }
    }
}
