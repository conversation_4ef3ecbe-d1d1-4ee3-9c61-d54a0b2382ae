using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;
using System.Reflection;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Paas.Controllers
{
    /// <summary>
    /// 用戶管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與用戶（Users）相關的操作，包括查詢、新增、更新和刪除用戶資料。
    /// 所有端點皆使用版本管理，以 `/api/` 作為前綴，並需要適當的身分驗證與授權。
    /// 實作 RESTful API 設計原則，提供統一的介面處理用戶資源。
    /// 用戶管理提供了完整的用戶生命週期管理功能，包括建立、查詢、更新和停用等操作。
    /// </remarks>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UsersController : ControllerBase
    {
        /// <summary>
        /// 用戶服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理用戶相關的業務邏輯，如查詢、新增、更新和刪除用戶資料。
        /// </remarks>
        protected readonly IUsersService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化用戶控制器的新執行個體
        /// </summary>
        /// <param name="service">用戶服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化用戶服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public UsersController(IUsersService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得所有用戶資料
        /// </summary>
        /// <param name="tenant_id">租戶 ID 過濾條件，若為 null 則不過濾</param>
        /// <param name="role_id">角色 ID 過濾條件，若為 null 則不過濾</param>
        /// <param name="model">基礎查詢模型，包含分頁和排序參數</param>
        /// <returns>包含用戶清單的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢系統中的用戶資訊，支援透過 `tenant_id` 和 `role_id` 條件進行過濾，
        /// 並支援分頁和排序功能，適用於管理介面的用戶列表頁面。
        /// 
        /// 範例請求：GET /api/users?tenant_id=b836f4ec-3b77-4972-bccf-1234567890ab&amp;role_id=b836f4ec-3b77-4972-bccf-0987654321cd&amp;limit=20&amp;offset=0
        /// 
        /// 成功回應：200 OK 與用戶資料陣列
        /// 錯誤回應：404 Not Found 或 401 Unauthorized / 403 Forbidden
        /// 
        /// 返回的用戶資料不包含敏感信息，如密碼或安全憑證。如需要這些資訊，應使用特定的 API 達成。
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> Get([FromQuery] Guid? tenant_id, [FromQuery] Guid? role_id, [FromQuery] BaseQueryModel model)
        {
            // 進行model Mapping處理
            UsersModel queryModel = mapper.Map<UsersModel>(model);
            queryModel.tenant_id = tenant_id;
            queryModel.role_id = role_id;
            // 取得所有使用者資料
            IList<UsersModel> result = await service.GetUsersList(queryModel);

            if (result.Count == 0)
            {
                return NotFound();
            }

            return Ok(mapper.Map<IList<UsersViewModel>>(result));
        }

        /// <summary>
        /// 取得特定用戶的詳細資料
        /// </summary>
        /// <param name="user_id">要查詢的用戶的唯一識別碼</param>
        /// <returns>包含特定用戶詳細資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端根據 user_id 查詢特定用戶的詳細資料。
        /// 適用於需要查看特定用戶詳情的場景，如用戶詳細資料頁面和用戶編輯頁面。
        /// 
        /// 範例請求：GET /api/users/b836f4ec-3b77-4972-bccf-1234567890ab
        /// 
        /// 成功回應：200 OK 與用戶的詳細資訊
        /// 錯誤回應：404 Not Found 或 403 Forbidden
        /// 
        /// 返回的資料包含用戶的完整資訊，如用戶 ID、姓名、電子郵件、角色、租戶和狀態等。
        /// </remarks>
        [HttpGet("{user_id}")]
        public async Task<IActionResult> GetDataById(Guid user_id)
        {
            // 取得特定使用者資料
            UsersModel result = await service.GetUsersData(user_id);

            if (result == null)
            {
                return NotFound();
            }

            return Ok(mapper.Map<UsersViewModel>(result));
        }

        /// <summary>
        /// 建立新的用戶資料
        /// </summary>
        /// <param name="model">包含新用戶資料的建立模型</param>
        /// <returns>包含新建立用戶資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端建立新的用戶資料。客戶端需要提供必要的用戶資訊，
        /// 如姓名、電子郵件、密碼、角色 ID 和租戶 ID 等。
        /// 
        /// 範例請求：POST /api/users 與 JSON 格式的用戶資料
        /// 
        /// 成功回應：201 Created 與新建立的用戶資訊
        /// 錯誤回應：400 Bad Request 或 500 Internal Server Error
        /// 
        /// 強烈建議在建立用戶時使用適當的密碼策略，確保密碼強度和安全性。
        /// 新建立的用戶預設為啟用狀態，除非在請求中明確指定為停用。
        /// </remarks>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] UsersCreateModel model)
        {
            // 進行model Mapping處理
            UsersModel queryModel = mapper.Map<UsersModel>(model);
            // 取得所有使用者資料
            UsersModel result = await service.CreateUsers(queryModel);

            if (result is null) return StatusCode(500, "新增失敗");

            return CreatedAtAction(nameof(GetDataById), new { user_id = result.user_id }, mapper.Map<UsersViewModel>(result));
        }

        /// <summary>
        /// 更新特定用戶的資料
        /// </summary>
        /// <param name="user_id">要更新的用戶的唯一識別碼</param>
        /// <param name="model">包含更新資料的模型</param>
        /// <returns>包含更新後用戶資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端更新指定用戶的資料。支援部分欄位更新，
        /// 僅需提供要更新的欄位，未提供的欄位將保持原值不變。
        /// 
        /// 範例請求：PUT /api/users/b836f4ec-3b77-4972-bccf-1234567890ab 與 JSON 格式的更新資料
        /// 
        /// 成功回應：200 OK 與更新後的用戶資訊
        /// 錯誤回應：404 Not Found 或 400 Bad Request
        /// 
        /// 更新用戶資料時，應特別注意敏感欄位的變更，如電子郵件、角色和授權狀態等。
        /// 如需更新密碼，應考慮使用專門的密碼更新 API 以增強安全性。
        /// </remarks>
        [HttpPut("{user_id}")]
        public async Task<IActionResult> Update(Guid user_id, [FromBody] UsersUpdateModel model)
        {
            // 進行model Mapping處理
            UsersModel queryModel = mapper.Map<UsersModel>(model);
            queryModel.user_id = user_id;

            // 更新使用者資料
            UsersModel result = await service.UpdateUsers(queryModel);

            return Ok(mapper.Map<UsersViewModel>(result));
        }

        /// <summary>
        /// 刪除特定用戶資料
        /// </summary>
        /// <param name="user_id">要刪除的用戶的唯一識別碼</param>
        /// <returns>表示操作結果的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端刪除指定的用戶資料。
        /// 在實際實作中，大多數情況下不會真正刪除用戶資料，而是將其標記為停用狀態。
        /// 
        /// 範例請求：DELETE /api/users/b836f4ec-3b77-4972-bccf-1234567890ab
        /// 
        /// 成功回應：204 No Content
        /// 錯誤回應：404 Not Found 或 400 Bad Request
        /// 
        /// 將用戶設為停用狀態而非完全刪除，有助於維護數據完整性和歷史記錄。
        /// 如需要單純取消用戶存取權限而非完全刪除，应考慮使用专门的用户停用 API。
        /// </remarks>
        [HttpDelete("{user_id}")]
        public async Task<IActionResult> Delete(Guid user_id)
        {
            if (!Guid.TryParse(user_id.ToString(), out var parsedUserId))
            {
                return BadRequest("Invalid user ID format.");
            }

            // 更新使用者狀態
            bool flag = await service.DeleteUsers(user_id);

            if (!flag) return NotFound();

            return NoContent();
        }
    }
}
