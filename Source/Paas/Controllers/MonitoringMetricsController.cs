using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Paas.Controllers
{
    /// <summary>
    /// 監控指標管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理與監控指標（Monitoring Metrics）相關的操作，包括查詢和分析系統效能指標。
    /// 所有端點皆需要適當的身分驗證與授權，為系統的監控和效能跟蹤提供支援。
    /// 實作 RESTful API 設計原則，提供統一的介面處理監控指標資源。
    /// 監控指標系統用於跟蹤和分析系統的各種效能指標，如 API 響應時間、資源使用率、錯誤率等，
    /// 為系統管理員提供了重要的工具，用於識別效能問題和優化機會。
    /// </remarks>
    [Route("api/monitoring_metrics")]
    [ApiController]
    [Authorize]
    public class MonitoringMetricsController : ControllerBase
    {
        /// <summary>
        /// 監控指標服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理監控指標相關的業務邏輯，如查詢和存取監控指標資料。
        /// </remarks>
        protected readonly IMonitoringMetricsService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化監控指標控制器的新執行個體
        /// </summary>
        /// <param name="service">監控指標服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化監控指標服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public MonitoringMetricsController(IMonitoringMetricsService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 獲取監控指標清單，可根據多種條件進行過濾和分頁
        /// </summary>
        /// <param name="tenant_id">可選的租戶 ID 過濾條件</param>
        /// <param name="metric_type_id">可選的指標類型 ID 過濾條件</param>
        /// <param name="api_id">可選的 API ID 過濾條件，用於清除特定 API 的指標</param>
        /// <param name="start_time">可選的開始時間過濾條件</param>
        /// <param name="end_time">可選的結束時間過濾條件</param>
        /// <param name="model">基礎查詢模型，包含分頁和排序參數</param>
        /// <returns>包含監控指標清單的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢系統中的監控指標資料，支援多種過濾條件，
        /// 如租戶 ID、指標類型、API ID 和時間範圍等，適用於呈現系統效能儀表板、監控工具或報表系統。
        /// 
        /// 範例請求：GET /api/monitoring_metrics?tenant_id=abcd1234&amp;start_time=2025-01-01T00:00:00Z&amp;end_time=2025-04-01T00:00:00Z
        /// 
        /// 成功回應：200 OK 與監控指標資料陣列
        /// 錯誤回應：401 Unauthorized 或 403 Forbidden
        /// 
        /// 此端點返回的指標數據可用於分析系統調用率、效能跟蹤、資源使用模式與效率分析等用途。
        /// 結果會根據查詢參數進行過濾，並根據分頁參數決定返回的資料量。
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> Get([FromQuery] Guid? tenant_id, [FromQuery] Guid? metric_type_id, [FromQuery] Guid? api_id, 
            [FromQuery] DateTime? start_time, [FromQuery] DateTime? end_time, [FromQuery] BaseQueryModel model)
        {
            // 進行model Mapping處理
            MonitoringMetricsModel queryModel = mapper.Map<MonitoringMetricsModel>(model);
            // 放入條件
            queryModel.tenant_id = tenant_id;
            queryModel.metric_type_id = metric_type_id;
            queryModel.api_id = api_id;
            // 取得資料
            IList<MonitoringMetricsModel> result = await service.GetMetricsDataList(queryModel);

            return Ok(mapper.Map<IList<MonitoringMetricsViewModel>>(result));
        }

        /// <summary>
        /// 根據指標 ID 獲取特定監控指標的詳細資料
        /// </summary>
        /// <param name="metric_id">要查詢的監控指標的唯一識別碼</param>
        /// <returns>包含特定監控指標詳細資料的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端根據 metric_id 查詢特定監控指標的詳細資料。
        /// 適用於查看特定指標的詳細資訊，如時間序列數據、歷史趨勢或深入分析工具。
        /// 
        /// 範例請求：GET /api/monitoring_metrics/b836f4ec-3b77-4972-bccf-1234567890ab
        /// 
        /// 成功回應：200 OK 與監控指標的詳細資料
        /// 錯誤回應：404 Not Found 或 403 Forbidden
        /// 
        /// 結果包含監控指標的完整資訊，如指標 ID、指標名稱、指標類型、測量值、時間戳、租戶 ID 和相關資源 ID 等。
        /// 這些詳細的指標資料對於評估系統效能、識別潛在問題和改善系統至關重要。
        /// </remarks>
        [HttpGet("{metric_id}")]
        public async Task<IActionResult> GetDataById(Guid metric_id)
        {
            // 取得資料
            MonitoringMetricsModel? result = await service.GetMetricsDataById(metric_id);

            if (result is null) return NotFound();

            return Ok(mapper.Map<MonitoringMetricsViewModel>(result));
        }
    }
}
