using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;
using System.ComponentModel.DataAnnotations;

namespace Paas.Controllers
{
    /// <summary>
    /// 權限管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理與權限（Permissions）相關的操作，主要用於查詢角色所擁有的權限。
    /// 所有端點皆需要適當的身分驗證與授權，為系統的角色型存取控制（RBAC）模型提供支援。
    /// 權限管理是系統安全模型的重要組成部分，確保用戶只能存取其被授權的功能和資源。
    /// 由於權限資訊與系統安全直接相關，此控制器的存取應受到嚴格的控制和審核。
    /// </remarks>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PermissionsController : ControllerBase
    {
        /// <summary>
        /// 權限管理服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理權限相關的業務邏輯，如查詢角色擁有的權限清單。
        /// </remarks>
        private readonly IPermissionsService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化權限控制器的新執行個體
        /// </summary>
        /// <param name="service">權限服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化權限服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public PermissionsController(IPermissionsService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;   
        }

        /// <summary>
        /// 取得指定角色的所有權限清單
        /// </summary>
        /// <param name="role_id">要查詢權限的角色的唯一識別碼，必須提供</param>
        /// <returns>包含角色權限清單的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢特定角色擁有的所有權限。
        /// 適用於用戶端的角色管理介面、動態定裝使用者介面和權限檢查。
        /// 
        /// 範例請求：GET /api/permissions?role_id=b836f4ec-3b77-4972-bccf-1234567890ab
        /// 
        /// 成功回應：200 OK 與權限清單
        /// 錯誤回應：404 Not Found 或 401 Unauthorized / 403 Forbidden
        /// 
        /// 注意：role_id 是必須的查詢參數，如果未提供則會導致驗證失敗。
        /// 此端點返回的權限清單可用於功能控制、選單顯示與隔離以及動態介面調整等各種定制化客戶端需求。
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> Get([FromQuery, Required] Guid role_id)
        {
            // 取得權限列表資料
            IList<PermissionsModel> result = await service.GetPermissionsList(role_id);

            if (result.Count == 0)
            {
                return NotFound();
            }

            return Ok(mapper.Map<IList<PermissionsViewModel>>(result));
        }
    }
}
