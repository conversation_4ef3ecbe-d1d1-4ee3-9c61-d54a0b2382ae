using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;

namespace Paas.Controllers
{
    /// <summary>
    /// API 使用紀錄控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與 API 使用紀錄相關的操作，包括查詢和記錄 API 的使用情況。
    /// 所有端點皆使用版本管理，以 `/v1/` 作為前綴，並需要適當的認證與授權。
    /// 這些紀錄可用於分析 API 效能、監控使用情況以及生成帳單和報表。
    /// </remarks>
    [Route("v1/api_usage")]
    [ApiController]
    [Authorize]
    public class ApiUsageController : ControllerBase
    {
        /// <summary>
        /// API 使用紀錄服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理 API 使用紀錄相關的業務邏輯，如查詢、新增和分析 API 使用情況。
        /// </remarks>
        protected readonly IApiUsageService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化 API 使用紀錄控制器的新執行個體
        /// </summary>
        /// <param name="service">API 使用紀錄服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化 API 使用紀錄服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public ApiUsageController(IApiUsageService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 獲取所有 API 使用紀錄
        /// </summary>
        /// <param name="api_id">API ID 過濾條件，若為 null 則不過濾</param>
        /// <param name="start_time">時間範圍的開始時間</param>
        /// <param name="end_time">時間範圍的結束時間</param>
        /// <param name="model">基礎查詢模型，包含分頁和排序參數</param>
        /// <returns>包含 API 使用紀錄列表的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢系統中的 API 使用紀錄，支援透過 `api_id` 和時間範圍條件進行過濾，
        /// 並支援分頁和排序功能，適用於監控工具和分析報表。
        /// 
        /// 範例請求：GET /v1/api_usage?api_id=967f4ec0-b235-48fa-bddf-9876543210aa&amp;start_time=2025-01-01T00:00:00Z&amp;end_time=2025-01-31T23:59:59Z&amp;limit=20&amp;offset=0
        /// 
        /// 成功回應：200 OK 與 API 使用紀錄陣列
        /// 錯誤回應：401 Unauthorized 或 403 Forbidden
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> Get([FromQuery] Guid? api_id, [FromQuery] DateTime? start_time, [FromQuery] DateTime? end_time, [FromQuery] BaseQueryModel model)
        {
            // 進行model Mapping處理
            ApiUsageModel queryModel = mapper.Map<ApiUsageModel>(model);

            // 放入資料
            queryModel.api_id = api_id;
            queryModel.start_time = start_time;
            queryModel.end_time = end_time;

            // 取得租戶列表資料
            IList<ApiUsageModel> data = await service.GetApiUsageDataList(queryModel);
            // 回傳資料處理
            List<ApiUsageViewModel> result = mapper.Map<List<ApiUsageViewModel>>(data);
            return Ok(result);
        }

        /// <summary>
        /// 取得特定 API 使用紀錄詳細資料
        /// </summary>
        /// <param name="usage_id">要查詢的使用紀錄 ID</param>
        /// <returns>包含特定 API 使用紀錄詳細資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端根據使用紀錄 ID 查詢特定 API 使用情況的詳細資訊。
        /// 
        /// 範例請求：GET /v1/api_usage/ab12cd34-ef56-7890-ab12-34567890abcd
        /// 
        /// 成功回應：200 OK 與使用紀錄詳細資訊
        /// 錯誤回應：404 Not Found
        /// </remarks>
        [HttpGet("{usage_id}")]
        public async Task<IActionResult> GetDataById(Guid usage_id)
        {
            // 取得此筆API 使用紀錄
            ApiUsageModel data = await service.GetApiUsageData(usage_id);

            if (data == null)
            {
                return NotFound();
            }

            // 回傳資料處理
            ApiUsageViewModel result = mapper.Map<ApiUsageViewModel>(data);

            return Ok(result);
        }

        /// <summary>
        /// 建立新的 API 使用紀錄
        /// </summary>
        /// <param name="model">包含新 API 使用紀錄資料的建立模型</param>
        /// <returns>包含新建立 API 使用紀錄資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許建立新的 API 使用紀錄，通常由 API Gateway 或後端自動建立。
        /// 客戶端需要提供必要的使用紀錄資訊，如 API ID、金鑰 ID、請求 IP、響應時間和狀態碼等。
        /// 
        /// 範例請求：POST /v1/api_usage 與 JSON 格式的 API 使用紀錄資料
        /// 
        /// 成功回應：201 Created 與新建立的 API 使用紀錄資訊
        /// 錯誤回應：400 Bad Request
        /// </remarks>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] ApiUsageCreateModel model)
        {
            // 進行model Mapping處理
            ApiUsageModel queryModel = mapper.Map<ApiUsageModel>(model);

            // 判斷欄位
            if (queryModel == null)
            {
                return BadRequest("Invalid data.");
            }

            // 新增API 使用紀錄
            ApiUsageModel data = await service.CreateApiUsageData(queryModel);

            if (data is null) return StatusCode(500, "新增失敗");

            return CreatedAtAction(nameof(GetDataById), new { usage_id = data.usage_id }, mapper.Map<ApiUsageViewModel>(data));
        }
    }
}
