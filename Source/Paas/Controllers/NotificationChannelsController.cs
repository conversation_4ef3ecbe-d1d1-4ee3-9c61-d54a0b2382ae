using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;

namespace Paas.Controllers
{
    /// <summary>
    /// 通知箤道管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與通知箤道（Notification Channels）相關的操作，包括查詢、新增、更新和刪除通知箤道設定。
    /// 所有端點皆使用版本管理，以 `/v1/` 作為前綴，並需要適當的身分驗證與授權。
    /// 實作 RESTful API 設計原則，提供統一的介面處理通知箤道資源。
    /// 通知箤道用於定義系統如何將通知發送給用戶，包括電子郵件、簡訊、應用內通知和其他通訊方式。
    /// </remarks>
    [Route("v1/notification_channels")]
    [ApiController]
    [Authorize]
    public class NotificationChannelsController : ControllerBase
    {
        /// <summary>
        /// 通知箤道服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理通知箤道相關的業務邏輯，如查詢、新增、更新和刪除通知箤道設定。
        /// </remarks>
        protected readonly INotificationChannelsService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化通知箤道控制器的新執行個體
        /// </summary>
        /// <param name="service">通知箤道服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化通知箤道服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public NotificationChannelsController(INotificationChannelsService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得所有通知箤道設定
        /// </summary>
        /// <param name="queryModel">包含查詢條件的模型，如用戶 ID、狀態和類型等</param>
        /// <returns>包含通知箤道清單的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢系統中的通知箤道設定，支援多種條件過濾。
        /// 包括按用戶 ID、箤道類型和狀態等條件進行過濾。
        /// 
        /// 範例請求：GET /v1/notification_channels?user_id=12345&amp;type=email&amp;status=active&amp;limit=20
        /// 
        /// 成功回應：200 OK 與通知箤道資料陣列
        /// 錯誤回應：401 Unauthorized 或 403 Forbidden
        /// 
        /// 返回的資料包含每個通知箤道的詳細設定，如 ID、類型、目標地址（電子郵件或手機號碼）、狀態和優先等級等。
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> GetNotificationChannels([FromQuery] NotificationChannelsQueryModel queryModel)
        {
            NotificationChannelsModel model = mapper.Map<NotificationChannelsModel>(queryModel);
            IList<NotificationChannelsModel> data = await service.GetNotificationChannels(model);
            return Ok(mapper.Map<IList<NotificationChannelsViewModel>>(data));
        }

        /// <summary>
        /// 新增通知箤道設定
        /// </summary>
        /// <param name="model">包含新通知箤道設定的建立模型</param>
        /// <returns>包含新建立通知箤道設定的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端建立新的通知箤道設定。客戶端需要提供必要的通知箤道資訊，
        /// 如箤道類型（電子郵件、簡訊等）、目標地址、用戶 ID 和狀態等。
        /// 
        /// 範例請求：POST /v1/notification_channels 與 JSON 格式的通知箤道設定
        /// 
        /// 成功回應：201 Created 與新建立的通知箤道設定
        /// 錯誤回應：400 Bad Request 或 500 Internal Server Error
        /// 
        /// 新建立的通知箤道可由系統立即使用於發送通知。在建立箤道時，可能需要由用戶進行驗證或確認步驟，
        /// 特別是電子郵件和手機簡訊等通訊方式。
        /// </remarks>
        [HttpPost]
        public async Task<IActionResult> InsertNotificationChannel(NotificationChannelsCreateModel model)
        {
            NotificationChannelsModel? createdData =
                await service.InsertNotificationChannel(mapper.Map<NotificationChannelsModel>(model));

            if (createdData is null) return StatusCode(500, "新增失敗");

            return StatusCode(201, mapper.Map<NotificationChannelsViewModel>(createdData));
        }

        /// <summary>
        /// 更新特定通知箤道設定
        /// </summary>
        /// <param name="channel_id">要更新的通知箤道的唯一識別碼</param>
        /// <param name="model">包含更新資料的模型</param>
        /// <returns>包含更新後通知箤道設定的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端更新指定的通知箤道設定。支援部分欄位更新，
        /// 僅需提供要更新的欄位，未提供的欄位將保持原值不變。
        /// 
        /// 範例請求：PUT /v1/notification_channels/aaaabbbb-cccc-dddd-eeee-ffffffffffff 與 JSON 格式的更新資料
        /// 
        /// 成功回應：200 OK 與更新後的通知箤道設定
        /// 錯誤回應：404 Not Found 或 500 Internal Server Error
        /// 
        /// 更新箤道時，某些變更可能需要驗證步驟，特別是更改通訊的目標地址（如電子郵件或手機號碼）。
        /// 更新狀態屬性可以用於啓用或停用特定的通知箤道，而不需要完全刪除。
        /// </remarks>
        [HttpPut("{channel_id}")]
        public async Task<IActionResult> UpdateNotificationChannel(Guid channel_id, [FromBody] NotificationChannelsUpdateModel model)
        {
            model.channel_id = channel_id;
            NotificationChannelsModel? updatedData = await service.UpdateNotificationChannel(mapper.Map<NotificationChannelsModel>(model));

            if (updatedData is null) return StatusCode(500, "查無資料，更新失敗");

            return Ok(mapper.Map<NotificationChannelsViewModel>(updatedData));
        }

        /// <summary>
        /// 刪除特定通知箤道設定
        /// </summary>
        /// <param name="channel_id">要刪除的通知箤道的唯一識別碼</param>
        /// <returns>表示操作結果的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端刪除指定的通知箤道設定。
        /// 刪除通知箤道將導致用戶無法透過該箤道接收通知。
        /// 
        /// 範例請求：DELETE /v1/notification_channels/aaaabbbb-cccc-dddd-eeee-ffffffffffff
        /// 
        /// 成功回應：204 No Content
        /// 錯誤回應：404 Not Found
        /// 
        /// 在某些場景下，可能會建議使用更新狀態（停用）而非完全刪除通知箤道，
        /// 以保留歷史記錄和配置資訊。如果用戶對操作感到後悔，也可以更容易復原。
        /// </remarks>
        [HttpDelete("{channel_id}")]
        public async Task<IActionResult> DeleteNotificationChannel(Guid channel_id)
        {
            bool isDeleted = await service.DeleteNotificationChannel(channel_id);

            if (!isDeleted) return NotFound();

            return NoContent();
        }
    }
}
