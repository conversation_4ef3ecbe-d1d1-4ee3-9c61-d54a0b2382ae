using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;

namespace Paas.Controllers
{
    /// <summary>
    /// API 金鑰管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與 API 金鑰（API Keys）相關的操作，包括查詢、新增、更新和刪除 API 金鑰。
    /// 所有端點皆使用版本管理，以 `/v1/` 作為前綴，並需要適當的身分驗證與授權。
    /// 實作 RESTful API 設計原則，提供統一的介面處理 API 金鑰資源。
    /// API 金鑰是客戶端進行 API 調用的身分驗證憑證，管理這些金鑰對於系統安全至關重要。
    /// </remarks>
    [Route("v1/api_keys")]
    [ApiController]
    [Authorize]
    public class ApiKeysController : ControllerBase
    {
        /// <summary>
        /// API 金鑰管理服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理 API 金鑰相關的業務邏輯，如查詢、新增、更新和刪除 API 金鑰。
        /// </remarks>
        protected readonly IApiKeysService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化 API 金鑰控制器的新執行個體
        /// </summary>
        /// <param name="service">API 金鑰服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化 API 金鑰服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public ApiKeysController(IApiKeysService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 獲取所有 API 金鑰清單
        /// </summary>
        /// <param name="queryModel">包含查詢條件的模型，如租戶 ID 和狀態等過濾條件</param>
        /// <returns>包含 API 金鑰清單的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢系統中的 API 金鑰資料，支援多種過濾條件，
        /// 如租戶 ID、狀態等，適用於管理介面的 API 金鑰列表頁面。
        /// 
        /// 範例請求：GET /v1/api_keys?tenant_id=abcd1234&amp;status=active
        /// 
        /// 成功回應：200 OK 與 API 金鑰資料陣列
        /// 錯誤回應：401 Unauthorized 或 403 Forbidden
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> GetApiKeys([FromQuery] ApiKeysQueryModel queryModel)
        {
            ApiKeysModel model = mapper.Map<ApiKeysModel>(queryModel);
            IList<ApiKeysModel> data = await service.GetApiKeys(model);
            return Ok(mapper.Map<IList<ApiKeysViewModel>>(data));
        }

        /// <summary>
        /// 根據金鑰 ID 獲取特定金鑰的詳細資訊
        /// </summary>
        /// <param name="key_id">要查詢的 API 金鑰的唯一識別碼</param>
        /// <returns>包含特定 API 金鑰詳細資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端根據 key_id 查詢特定 API 金鑰的詳細資料。
        /// 
        /// 範例請求：GET /v1/api_keys/b836f4ec-3b77-4972-bccf-1234567890ab
        /// 
        /// 成功回應：200 OK 與 API 金鑰的詳細資訊
        /// 錯誤回應：404 Not Found 或 403 Forbidden
        /// </remarks>
        [HttpGet("{key_id}")]
        public async Task<IActionResult> GetApiKey(Guid key_id)
        {
            ApiKeysModel? data = await service.GetApiKey(key_id);

            if (data is null) return NotFound();

            return Ok(mapper.Map<ApiKeysViewModel>(data));
        }

        /// <summary>
        /// 創建新的 API 金鑰
        /// </summary>
        /// <param name="model">包含新 API 金鑰資料的建立模型</param>
        /// <returns>包含新建立 API 金鑰資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端建立新的 API 金鑰。客戶端需要提供必要的 API 金鑰資訊，
        /// 如租戶 ID、API 金鑰密鑰、權限範圍和到期時間等。
        /// 
        /// 範例請求：POST /v1/api_keys 與 JSON 格式的 API 金鑰資料
        /// 
        /// 成功回應：201 Created 與新建立的 API 金鑰資訊
        /// 錯誤回應：400 Bad Request 或 500 Internal Server Error
        /// </remarks>
        [HttpPost]
        public async Task<IActionResult> InsertApiKey(ApiKeysCreateModel model)
        {
            ApiKeysModel? createdData =
                await service.InsertApiKey(mapper.Map<ApiKeysModel>(model));

            if (createdData is null) return StatusCode(500, "新增失敗");

            return StatusCode(201, mapper.Map<ApiKeysViewModel>(createdData));
        }

        /// <summary>
        /// 更新特定 API 金鑰的資訊
        /// </summary>
        /// <param name="key_id">要更新的 API 金鑰的唯一識別碼</param>
        /// <param name="model">包含更新資料的模型</param>
        /// <returns>包含更新後 API 金鑰資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端更新指定 API 金鑰的資料。支援部分欄位更新，
        /// 僅需提供要更新的欄位，未提供的欄位將保持原值不變。
        /// 
        /// 範例請求：PUT /v1/api_keys/{key_id} 與 JSON 格式的更新資料
        /// 
        /// 成功回應：200 OK 與更新後的 API 金鑰資訊
        /// 錯誤回應：404 Not Found 或 500 Internal Server Error
        /// </remarks>
        [HttpPut("{key_id}")]
        public async Task<IActionResult> UpdateApiKey(Guid key_id, [FromBody] ApiKeysUpdateModel model)
        {
            model.key_id = key_id;
            ApiKeysModel? updatedData = await service.UpdateApiKey(mapper.Map<ApiKeysModel>(model));

            if (updatedData is null) return StatusCode(500, "查無資料，更新失敗");

            return Ok(mapper.Map<ApiKeysViewModel>(updatedData));
        }

        /// <summary>
        /// 刪除特定 API 金鑰
        /// </summary>
        /// <param name="key_id">要刪除的 API 金鑰的唯一識別碼</param>
        /// <returns>表示操作結果的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端刪除指定的 API 金鑰。
        /// 刪除操作將完全移除該金鑰，且無法恢復。
        /// 被刪除的 API 金鑰將無法用於後續的 API 調用。
        /// 
        /// 範例請求：DELETE /v1/api_keys/{key_id}
        /// 
        /// 成功回應：204 No Content
        /// 錯誤回應：404 Not Found 或 403 Forbidden
        /// </remarks>
        [HttpDelete("{key_id}")]
        public async Task<IActionResult> DeleteApiKey(Guid key_id)
        {
            bool isDeleted = await service.DeleteApiKey(key_id);

            if (!isDeleted) return NotFound();

            return NoContent();
        }
    }
}
