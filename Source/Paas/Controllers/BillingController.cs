using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Paas.Controllers
{
    /// <summary>
    /// 帳單與付款管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與帳單和付款相關的操作，包括查詢、新增、更新和刪除帳單資料。
    /// 所有端點皆使用版本管理，以 `/v1/` 作為前綴，並需要適當的認證與授權。
    /// 實作 RESTful API 設計原則，提供統一的介面處理帳單資源。
    /// </remarks>
    [Route("v1/[controller]")]
    [ApiController]
    [Authorize]
    public class BillingController : ControllerBase
    {
        /// <summary>
        /// 帳單服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理帳單相關的業務邏輯，如查詢、新增、更新和刪除帳單資料。
        /// </remarks>
        protected readonly IBillingService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化帳單控制器的新執行個體
        /// </summary>
        /// <param name="service">帳單服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化帳單服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public BillingController(IBillingService service, IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 獲取所有帳單資訊
        /// </summary>
        /// <param name="tenant_id">租戶 ID 過濾條件，若為 null 則不過濾</param>
        /// <param name="status">帳單狀態過濾條件，若為 null 則不過濾</param>
        /// <param name="model">基礎查詢模型，包含分頁和排序參數</param>
        /// <returns>包含帳單列表的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端查詢系統中的帳單資訊，支援透過 `tenant_id` 和 `status` 條件進行過濾，
        /// 並支援分頁和排序功能，適用於財務管理和報表生成。
        /// 
        /// 範例請求：GET /v1/billing?tenant_id=b836f4ec-3b77-4972-bccf-1234567890ab&amp;status=pending&amp;limit=20&amp;offset=0
        /// 
        /// 成功回應：200 OK 與帳單資料陣列
        /// 錯誤回應：401 Unauthorized 或 403 Forbidden
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> Get([FromQuery] Guid? tenant_id, [FromQuery] string? status, [FromQuery] BaseQueryModel model)
        {
            // 進行model Mapping處理
            BillingModel queryModel = mapper.Map<BillingModel>(model);
            // 放入資料
            queryModel.tenant_id = tenant_id;
            queryModel.status = status;
            // 取得帳單資料清單
            IList<BillingModel> data = await service.GetBillingDataList(queryModel);

            return Ok(mapper.Map<IList<BillingViewModel>>(data));
        }

        /// <summary>
        /// 根據帳單 ID 取得特定帳單資訊
        /// </summary>
        /// <param name="billing_id">要查詢的帳單 ID</param>
        /// <returns>包含特定帳單詳細資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端根據帳單 ID 查詢特定帳單的詳細資訊。
        /// 
        /// 範例請求：GET /v1/billing/aaaabbbb-cccc-dddd-eeee-ffffffffffff
        /// 
        /// 成功回應：200 OK 與帳單詳細資訊
        /// 錯誤回應：404 Not Found
        /// </remarks>
        [HttpGet("{billing_id}")]
        public async Task<IActionResult> GetById(Guid billing_id)
        {
            // 取得帳單資料
            BillingModel data = await service.GetBillingDataById(billing_id);

            if (data == null)
            {
                return NotFound();
            }

            return Ok(mapper.Map<BillingViewModel>(data));
        }

        /// <summary>
        /// 新增帳單資訊
        /// </summary>
        /// <param name="model">包含新帳單資料的建立模型</param>
        /// <returns>包含新建立帳單資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端建立新的帳單資料。客戶端需要提供必要的帳單資訊，
        /// 如租戶 ID、金額、貨幣 ID、付款方式 ID 和到期日期等。
        /// 
        /// 範例請求：POST /v1/billing 與 JSON 格式的帳單資料
        /// 
        /// 成功回應：201 Created 與新建立的帳單資訊
        /// 錯誤回應：400 Bad Request 或 500 Internal Server Error
        /// </remarks>
        [HttpPost]
        public async Task<IActionResult> CreateBillingData([FromBody] BillingCreateModel model)
        {
            // 進行model Mapping處理
            BillingModel queryModel = mapper.Map<BillingModel>(model);
            // 新增帳單
            BillingModel data = await service.CreateBillingData(queryModel);

            if (data is null) return StatusCode(500, "新增失敗");

            return CreatedAtAction(nameof(GetById), new { billing_id = data.record_id }, mapper.Map<BillingViewModel>(data));
        }

        /// <summary>
        /// 更新特定帳單資訊
        /// </summary>
        /// <param name="billing_id">要更新的帳單 ID</param>
        /// <param name="model">包含更新資料的模型</param>
        /// <returns>包含更新後帳單資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端更新特定帳單的資訊，如金額、狀態和付款日期等。
        /// 支援部分欄位更新，僅需提供要更新的欄位，未提供的欄位將保持原值不變。
        /// 
        /// 範例請求：PUT /v1/billing/{billing_id} 與 JSON 格式的更新資料
        /// 
        /// 成功回應：200 OK 與更新後的帳單資訊
        /// 錯誤回應：404 Not Found 或 500 Internal Server Error
        /// </remarks>
        [HttpPut("{billing_id}")]
        public async Task<IActionResult> UpdateBillingData(Guid billing_id, [FromBody] BillingUpdateModel model)
        {
            // 進行model Mapping處理
            BillingModel queryModel = mapper.Map<BillingModel>(model);
            // 放入資料
            queryModel.record_id = billing_id;
            // 修改帳單
            BillingModel data = await service.UpdateBillingData(queryModel);

            if (data is null) return StatusCode(500, "查無資料，更新失敗");

            return Ok(mapper.Map<BillingViewModel>(data));
        }

        /// <summary>
        /// 刪除特定帳單
        /// </summary>
        /// <param name="billing_id">要刪除的帳單 ID</param>
        /// <returns>表示操作結果的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端刪除指定的帳單資料。
        /// 依據業務需求，可能是直接刪除資料庫中的資料，或將帳單標記為已取消狀態。
        /// 
        /// 範例請求：DELETE /v1/billing/{billing_id}
        /// 
        /// 成功回應：204 No Content
        /// 錯誤回應：404 Not Found
        /// </remarks>
        [HttpDelete("{billing_id}")]
        public async Task<IActionResult> DeleteBillingData(Guid billing_id)
        {
            bool isDeleted = await service.DeleteBillingData(billing_id);

            if (!isDeleted) return NotFound();

            return NoContent();
        }
    }
}
