using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Paas.Model;
using Paas.Service;

namespace Paas.Controllers
{
    /// <summary>
    /// 貨幣管理控制器
    /// </summary>
    /// <remarks>
    /// 此控制器負責處理所有與貨幣（Currencies）相關的操作，包括查詢、新增、更新和刪除貨幣資料。
    /// 所有端點皆使用版本管理，以 `/v1/` 作為前綴，並需要適當的身分驗證與授權。
    /// 實作 RESTful API 設計原則，提供統一的介面處理貨幣資源。
    /// 貨幣管理為系統提供了多幣別支援，對於國際化應用和多市場部署至關重要。
    /// </remarks>
    [Route("v1/[controller]")]
    [ApiController]
    [Authorize]
    public class CurrenciesController: ControllerBase
    {
        /// <summary>
        /// 貨幣管理服務介面
        /// </summary>
        /// <remarks>
        /// 用於處理貨幣相關的業務邏輯，如查詢、新增、更新和刪除貨幣資料。
        /// </remarks>
        protected readonly ICurrenciesService service;
        
        /// <summary>
        /// 物件映射器
        /// </summary>
        /// <remarks>
        /// 用於在不同的模型類別之間進行物件映射，如 DTO、ViewModel 和領域模型之間的轉換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化貨幣控制器的新執行個體
        /// </summary>
        /// <param name="service">貨幣服務的實例</param>
        /// <param name="mapper">物件映射器的實例</param>
        /// <remarks>
        /// 使用依賴注入模式，接收並初始化貨幣服務和物件映射器，以便在控制器中使用。
        /// </remarks>
        public CurrenciesController(ICurrenciesService service,IMapper mapper)
        {
            this.service = service;
            this.mapper = mapper;
        }

        /// <summary>
        /// 獲取所有貨幣資訊
        /// </summary>
        /// <returns>包含貨幣清單的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點呈現系統中所有可用的貨幣資訊，包括代碼、符號、匯率和啟用狀態等。
        /// 適用於管理介面的貨幣列表頁面、付款點的貨幣選擇和匯率計算器等功能。
        /// 
        /// 範例請求：GET /v1/currencies
        /// 
        /// 成功回應：200 OK 與貨幣資料陣列
        /// 錯誤回應：401 Unauthorized 或 403 Forbidden
        /// 
        /// 返回的清單包含所有活躍和非活躍的貨幣，客戶端可根據自身需求進行過濾和處理。
        /// </remarks>
        [HttpGet]
        public async Task<IActionResult> GetCurrencies()
        {
            IList<CurrenciesModel> data = await service.GetCurrencies();
            return Ok(mapper.Map<IList<CurrenciesViewModel>>(data));
        }

        /// <summary>
        /// 根據貨幣 ID 獲取特定貨幣的詳細資訊
        /// </summary>
        /// <param name="currency_id">要查詢的貨幣的唯一識別碼</param>
        /// <returns>包含特定貨幣詳細資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端根據 currency_id 查詢特定貨幣的詳細資料。
        /// 適用於需要查看特定貨幣詳情的場景，如貨幣編輯頁面和詳細信息展示。
        /// 
        /// 範例請求：GET /v1/currencies/b836f4ec-3b77-4972-bccf-1234567890ab
        /// 
        /// 成功回應：200 OK 與貨幣的詳細資訊
        /// 錯誤回應：404 Not Found 或 403 Forbidden
        /// 
        /// 返回的資料包含貨幣的完整資訊，如貨幣 ID、代碼、符號、匯率和啟用狀態等。
        /// </remarks>
        [HttpGet("{currency_id}")]
        public async Task<IActionResult> GetCurrency(Guid currency_id)
        {
            CurrenciesModel? data = await service.GetCurrency(currency_id);

            if (data is null) return NotFound();

            return Ok(mapper.Map<CurrenciesViewModel>(data));
        }

        /// <summary>
        /// 創建新的貨幣
        /// </summary>
        /// <param name="model">包含新貨幣資料的建立模型</param>
        /// <returns>包含新建立貨幣資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端建立新的貨幣資料。客戶端需要提供必要的貨幣資訊，
        /// 如貨幣代碼、符號、匯率和啟用狀態等。
        /// 
        /// 範例請求：POST /v1/currencies 與 JSON 格式的貨幣資料
        /// 
        /// 成功回應：201 Created 與新建立的貨幣資訊
        /// 錯誤回應：400 Bad Request 或 500 Internal Server Error
        /// 
        /// 新增貨幣應確保貨幣代碼符合 ISO 4217 標準，並提供正確的匯率唯系統的預設貨幣進行換算。
        /// </remarks>
        [HttpPost]
        public async Task<IActionResult> InsertCurrency(CurrenciesCreateModel model)
        {
            CurrenciesModel? createdData = 
                await service.InsertCurrency(mapper.Map<CurrenciesModel>(model));

            if (createdData is null) return StatusCode(500, "新增失敗");

            return StatusCode(201, mapper.Map<CurrenciesViewModel>(createdData));
        }

        /// <summary>
        /// 更新特定貨幣的資訊
        /// </summary>
        /// <param name="currency_id">要更新的貨幣的唯一識別碼</param>
        /// <param name="model">包含更新資料的模型</param>
        /// <returns>包含更新後貨幣資訊的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端更新指定貨幣的資料。支援部分欄位更新，
        /// 僅需提供要更新的欄位，未提供的欄位將保持原值不變。
        /// 
        /// 範例請求：PUT /v1/currencies/{currency_id} 與 JSON 格式的更新資料
        /// 
        /// 成功回應：200 OK 與更新後的貨幣資訊
        /// 錯誤回應：404 Not Found 或 500 Internal Server Error
        /// 
        /// 更新貨幣的匯率會影響系統中所有使用該貨幣的交易和帳單，應謹慎進行並確保正確性。
        /// </remarks>
        [HttpPut("{currency_id}")]
        public async Task<IActionResult> UpdateCurrency(Guid currency_id, [FromBody] CurrenciesUpdateModel model)
        {
            model.currency_id = currency_id;
            CurrenciesModel? updatedData = await service.UpdateCurrency(mapper.Map<CurrenciesModel>(model));

            if (updatedData is null) return StatusCode(500, "查無資料，更新失敗");

            return Ok(mapper.Map<CurrenciesViewModel>(updatedData));
        }

        /// <summary>
        /// 刪除特定貨幣
        /// </summary>
        /// <param name="currency_id">要刪除的貨幣的唯一識別碼</param>
        /// <returns>表示操作結果的 HTTP 回應</returns>
        /// <remarks>
        /// 此端點允許客戶端刪除指定的貨幣資料。
        /// 刪除操作應考慮貨幣的關聯影響。如果貨幣正在被使用，可能無法直接刪除。
        /// 
        /// 範例請求：DELETE /v1/currencies/{currency_id}
        /// 
        /// 成功回應：204 No Content
        /// 錯誤回應：404 Not Found 或 409 Conflict
        /// 
        /// 注意：為了保護數據完整性，如果貨幣正在被其他資料引用，如帳單或用戶設定，
        /// 可能會無法刪除或變更為將其標記為停用狀態而非直接刪除。
        /// </remarks>
        [HttpDelete("{currency_id}")]
        public async Task<IActionResult> DeleteCurrency(Guid currency_id)
        {
            bool isDeleted = await service.DeleteCurrency(currency_id);

            if (!isDeleted) return NotFound();

            return NoContent();
        }
    }
}
