using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Npgsql;
using Paas.Common;
using System.Net;

namespace Paas.Middleware
{
    /// <summary>
    /// 例外處理中介軟體
    /// </summary>
    /// <remarks>
    /// 此中介軟體用於捕獲應用程式中產生的所有未處理例外狀況，並將它們轉換為適當的 HTTP 回應。
    /// 透過全域異常處理，可確保所有 API 回應遵循一致的錯誤格式，提升系統的穩定性及用戶體驗。
    /// </remarks>
    public class ExceptionMiddleware
    {
        /// <summary>
        /// 下一個請求委派
        /// </summary>
        private readonly RequestDelegate _next;
        
        /// <summary>
        /// 日誌記錄器
        /// </summary>
        private readonly ILogger<ExceptionMiddleware> _logger;

        /// <summary>
        /// 初始化例外處理中介軟體的新執行個體
        /// </summary>
        /// <param name="next">處理管道中的下一個委派</param>
        /// <param name="logger">日誌記錄器實例</param>
        /// <remarks>
        /// 構造函數接收下一個中介軟體委派和日誌記錄器，以便能在例外發生時進行日誌記錄並轉發正常請求
        /// </remarks>
        public ExceptionMiddleware(RequestDelegate next, ILogger<ExceptionMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        /// <summary>
        /// 執行中介軟體的主要處理邏輯
        /// </summary>
        /// <param name="context">HTTP 請求上下文</param>
        /// <returns>表示異步操作的任務</returns>
        /// <remarks>
        /// 此方法嘗試執行下一個中介軟體委派，並捕獲執行過程中可能發生的任何例外。
        /// 若發生例外，會呼叫 HandleExceptionAsync 方法來處理例外狀況，並產生適當的錯誤回應。
        /// </remarks>
        public async Task Invoke(HttpContext context)
        {
            try
            {
                await _next(context); // 繼續執行下一個 Middleware
            }
            catch (Exception ex)
            {
                Logger.Error($"全域錯誤攔截: {ex.StackTrace + ex.Message}", ex);
                await HandleExceptionAsync(context, ex);
            }
        }

        /// <summary>
        /// 處理捕獲的例外狀況
        /// </summary>
        /// <param name="context">HTTP 請求上下文</param>
        /// <param name="ex">捕獲的例外實例</param>
        /// <returns>表示異步操作的任務</returns>
        /// <remarks>
        /// 此方法根據捕獲的例外類型決定適當的 HTTP 狀態碼和錯誤訊息。
        /// 它遵循 RESTful API 最佳實踐，將不同類型的例外映射到相應的 HTTP 狀態碼：
        /// - ArgumentException: 400 (Bad Request) - 請求參數錯誤
        /// - KeyNotFoundException: 404 (Not Found) - 找不到資源
        /// - UnauthorizedAccessException: 401 (Unauthorized) - 未授權操作
        /// - PostgreSQL 外鍵約束違反: 400 (Bad Request) - 數據完整性錯誤
        /// - 其他例外: 500 (Internal Server Error) - 伺服器內部錯誤
        /// </remarks>
        // todo 修改error 顯示方式
        private static Task HandleExceptionAsync(HttpContext context, Exception ex)
        {
            // 依據例外類型決定 StatusCode
            var statusCode = ex switch
            {
                ArgumentException => StatusCodes.Status400BadRequest,  // 400 - 請求錯誤
                KeyNotFoundException => StatusCodes.Status404NotFound, // 404 - 找不到資源
                UnauthorizedAccessException => StatusCodes.Status401Unauthorized, // 401 - 未授權
                NpgsqlException npgsqlEx when npgsqlEx.SqlState == "23503" => StatusCodes.Status400BadRequest, // 23503 外鍵約束違反(關聯對不到)，映射為 400
                NpgsqlException npgsqlEx when npgsqlEx.SqlState == "23514" => StatusCodes.Status400BadRequest, // 23514 外鍵約束違反(非指定值)，映射為 400
                NpgsqlException npgsqlEx when npgsqlEx.SqlState == "23505" => StatusCodes.Status400BadRequest, // 23505 外鍵約束違反(欄位為非null)，映射為 400
                _ => StatusCodes.Status500InternalServerError // 500 - 伺服器內部錯誤
            };

            // 設定對應的錯誤訊息
            var errorMessage = statusCode switch
            {
                StatusCodes.Status400BadRequest => "請求參數錯誤，請確認後再試。",
                StatusCodes.Status404NotFound => "查無資料，請檢查請求內容。",
                StatusCodes.Status401Unauthorized => "您沒有權限執行此操作。",
                _ => "請洽系統管理員" // 預設錯誤訊息
            };

            var response = new
            {
                statusCode,
                message = errorMessage
            };
            context.Response.ContentType = "application/json";
            context.Response.StatusCode = statusCode;

            return context.Response.WriteAsJsonAsync(response);
        }
    }
}
