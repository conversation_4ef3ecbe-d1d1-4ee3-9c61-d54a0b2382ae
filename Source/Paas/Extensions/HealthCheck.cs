using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Paas.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace Paas.Extensions;

/// <summary>
/// 健康狀態檢查設定類別
/// </summary>
/// <remarks>
/// 此類別提供設定和管理應用程式健康狀態檢查功能的方法。
/// 健康狀態檢查功能可讓系統監控和其他架構元件快速判斷應用程式是否正常運行。
/// 提供了資料庫連線檢查等功能，並輸出結構化的 JSON 回應以供其他系統或監控工具使用。
/// 此類別在 Kubernetes 等健康狀態檢查和負載平衡器定期檢查的環境中特別有用。
/// </remarks>
public static class HealthCheck
{
    /// <summary>
    /// 設定應用程式的健康狀態檢查功能
    /// </summary>
    /// <remarks>
    /// 此方法為應用程式設定健康狀態檢查機制，包含資料庫連線檢查等功能。
    /// 使用 PostgreSQL 健康檢查功能，定期檢測資料庫連線是否正常。
    /// 可以在此方法中添加其他健康檢查，如 Redis、消息佇列、外部 API 檢查等。
    /// 所有健康狀態檢查都可以透過系統的 /health 端點進行存取。
    /// </remarks>
    /// <param name="services">服務集合物件，用於註冊健康狀態檢查功能</param>
    /// <param name="configuration">應用程式設定，用於取得資料庫連線字串等設定資訊</param>
    public static IServiceCollection AddHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHealthChecks()
            .AddNpgSql( // db health check
                connectionString: configuration.GetConnectionString("PostgreConnection") ?? throw new InvalidOperationException("PostgreConnection connection string is not configured."),
                name: "database",
                healthQuery: "SELECT 1",
                failureStatus: HealthStatus.Unhealthy,
                tags: ["db", "postgresql"]
            );
            
        return services;
    }


    /// <summary>
    /// 健康狀態檢查回應格式化處理類別
    /// </summary>
    /// <remarks>
    /// 此嵌套類別提供將健康狀態檢查結果格式化為 JSON 回應的功能。
    /// 它將原始的健康狀態報告轉換為精簡的 JSON 格式，包含狀態、各元件狀態和時間戳。
    /// 此回應格式特別適合用於監控工具、健康檢查服務和負載平衡器的整合。
    /// 設計為簡潔且一致的回應，易於電腦和人工解析。
    /// </remarks>
    public static class HealthCheckResponseWriter
    {
        /// <summary>
        /// 將健康狀態檢查結果寫入 HTTP 回應
        /// </summary>
        /// <remarks>
        /// 此方法接收健康狀態檢查結果，並將其格式化為 JSON 回應輸出到 HTTP 上下文中。
        /// 輸出的 JSON 結構包含三個主要部分：
        /// 1. status：系統整體健康狀態（healthy、degraded 或 unhealthy）
        /// 2. components：各元件的健康狀態，簡化為 "ok" 或 "failed"
        /// 3. timestamp：回應產生的 UTC 時間，使用 ISO 8601 格式
        /// 此方法也設定適當的 HTTP 標頭，包含內容類型和安全相關的標頭。
        /// </remarks>
        /// <param name="context">HTTP 上下文，用於寫入回應</param>
        /// <param name="report">健康狀態檢查結果報告</param>
        /// <returns>代表靜态寫入操作的對象</returns>
        public static Task WriteAsync(HttpContext context, HealthReport report)
        {
            var result = new
            {
                status = report.Status.ToString().ToLower(), // api server status
                components = report.Entries.ToDictionary(
                    e => e.Key,
                    e => e.Value.Status == HealthStatus.Healthy ? "ok" : "failed"
                ),
                timestamp = DateTime.UtcNow.ToDateStr()
            };

            context.Response.ContentType = "application/json";
            context.Response.Headers.Append("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
            return context.Response.WriteAsync(JsonSerializer.Serialize(result));
        }
    }
}

