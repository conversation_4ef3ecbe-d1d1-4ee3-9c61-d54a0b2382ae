using Microsoft.AspNetCore.Cors.Infrastructure;
using System.Collections.Generic;

namespace Paas.Extensions
{
    /// <summary>
    /// CORS 擴展類
    /// </summary>
    /// <remarks>
    /// 提供配置 CORS (跨來源資源共享) 設定的擴展方法，包括從環境變數讀取允許的源和支援動態 Cookie Domain。
    /// </remarks>
    public static class CorsExtensions
    {
        /// <summary>
        /// 配置 CORS 政策
        /// </summary>
        /// <param name="builder">應用程式建構器</param>
        /// <param name="configuration">配置來源</param>
        /// <returns>配置後的應用程式建構器</returns>
        /// <remarks>
        /// 設置 CORS 政策，從配置中讀取允許的源，支援多個前端源，並正確處理 Cookie Domain。
        /// </remarks>
        public static WebApplicationBuilder ConfigureCors(this WebApplicationBuilder builder)
        {
            // 讀取配置
            var configuration = builder.Configuration;
            
            // 從配置中讀取允許的源，包括開發和生產環境
            var allowedOrigins = new List<string>();
            configuration.GetSection("Cors:AllowedOrigins").Bind(allowedOrigins);
            
            // 若配置為空，添加預設值用於開發
            if (allowedOrigins.Count == 0)
            {
                allowedOrigins.Add("http://localhost:3000"); // Next.js 默認開發端口
                allowedOrigins.Add("https://localhost:3000");
            }
            
            // 配置 CORS 政策
            builder.Services.AddCors(options =>
            {
                options.AddPolicy("DefaultCorsPolicy", policy =>
                {
                    policy.WithOrigins(allowedOrigins.ToArray())
                          .AllowAnyHeader()
                          .AllowAnyMethod()
                          .AllowCredentials(); // 允許憑證（如 cookies）
                });
            });
            
            return builder;
        }
        
        /// <summary>
        /// 獲取 Cookie Domain
        /// </summary>
        /// <param name="configuration">配置來源</param>
        /// <param name="request">HTTP 請求</param>
        /// <returns>用於 Cookie 的域名</returns>
        /// <remarks>
        /// 根據請求和配置決定 Cookie 應該使用的域名，支援開發和生產環境。
        /// </remarks>
        public static string? GetCookieDomain(this IConfiguration configuration, HttpRequest request)
        {
            // 從配置中讀取 Cookie Domain
            var configuredDomain = configuration["CookieDomain"];
            
            // 如果已配置，則使用配置的域名
            if (!string.IsNullOrEmpty(configuredDomain))
            {
                return configuredDomain;
            }
            
            // 從請求中獲取 X-Cookie-Domain 標頭（前端可以在請求中設置此標頭）
            var headerDomain = request.Headers["X-Cookie-Domain"].FirstOrDefault();
            if (!string.IsNullOrEmpty(headerDomain))
            {
                return headerDomain;
            }
            
            // 從 Origin 或 Referer 標頭中解析域名
            var origin = request.Headers["Origin"].FirstOrDefault() 
                      ?? request.Headers["Referer"].FirstOrDefault();
            
            if (!string.IsNullOrEmpty(origin))
            {
                try
                {
                    var uri = new Uri(origin);
                    var host = uri.Host;
                    
                    // 如果是 localhost，則返回 null（讓瀏覽器使用默認行為）
                    if (host == "localhost")
                    {
                        return null;
                    }
                    
                    // 如果是 IP 地址，則直接返回
                    if (System.Net.IPAddress.TryParse(host, out _))
                    {
                        return host;
                    }
                    
                    // 對於其他域名，設定為根域名（例如 example.com 而非 app.example.com）
                    var parts = host.Split('.');
                    if (parts.Length >= 2)
                    {
                        return $".{parts[parts.Length - 2]}.{parts[parts.Length - 1]}";
                    }
                    
                    return host;
                }
                catch
                {
                    // 解析失敗，返回 null
                    return null;
                }
            }
            
            // 默認返回 null，讓瀏覽器使用默認行為
            return null;
        }
    }
}
