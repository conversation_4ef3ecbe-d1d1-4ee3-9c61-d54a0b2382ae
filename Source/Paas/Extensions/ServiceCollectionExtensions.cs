using Microsoft.Identity.Client;
using Paas.Dac;
using Paas.Dac.Interface;
using Paas.Model;
using Paas.Service;

namespace Paas.Extensions
{
    /// <summary>
    /// 服務集合擴充方法類別
    /// </summary>
    /// <remarks>
    /// 此類別提供一系列的系統服務設定與相依性注入設定的擴充方法。
    /// 為了提供結構化的服務設定方式，所有相關的應用程式服務、資料存取層元件與 AutoMapper 設定都集中在此處理。
    /// 此類別擴充了 IServiceCollection 的功能，方便在應用程式的 Startup 或 Program 類中以流暫的方式進行服務設定。
    /// 透過將所有相依性設定分層組織，提供了清晰的結構和易於維護的服務設定方式。
    /// </remarks>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 設定應用程式所需的所有相依性服務
        /// </summary>
        /// <remarks>
        /// 此方法為應用程式設定並註冊所有必要的服務與相依性。
        /// 包含設定 HTTP 內容存取器、控制器選項、資料庫連線管理、各層級的伺服與資料存取元件。
        /// 此方法結構化地執行全面的服務設定，包含資料庫連線、服務層、資料存取層和對象映射的設定。
        /// 透過呼叫各專門化的設定方法，使得應用程式在啟動時能完整運作。
        /// </remarks>
        /// <param name="services">服務集合物件，用於註冊應用程式中所需的服務</param>
        /// <param name="configuration">應用程式的設定，包含環境變量、設定檔等的訪問介面</param>
        /// <returns>已設定完成的服務集合</returns>
        public static IServiceCollection AddApplicationServices(
            this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpContextAccessor();

            services.AddControllers(options =>
            {
                options.SuppressImplicitRequiredAttributeForNonNullableReferenceTypes = true;
            });

            //註冊資料庫連線管理
            services.AddSingleton<IConnectionControlCenter, ConnectionControlCenter>();
            services.AddSingleton<IConnectionControlCenter, ConnectionControlCenterPostgreSql>();
            //註冊Service
            services.ServiceLayeredDI();
            //註冊Dac
            services.DacLayeredDI();
            //註冊Mapper
            services.MapperLayerDI();

            return services;
        }
        /// <summary>
        /// 注冊應用程式的服務層相依性
        /// </summary>
        /// <remarks>
        /// 此方法註冊應用程式中所有的服務層元件，將接口與實作結合。
        /// 所有服務都被註冊為 Scoped 生命週期，即每個 HTTP 請求會建立新的服務實例。
        /// 此方法包含租戶管理、識別與授權、API 管理、財務與計費、監控與安全日誌、通知等服務。
        /// 使用此方法可以確保應用程式中的服務層元件都被正確註冊，並由相依性注入容器進行管理。
        /// </remarks>
        /// <param name="services">服務集合物件，用於註冊服務層相依性</param>
        /// <returns>已設定完成的服務集合</returns>
        private static IServiceCollection ServiceLayeredDI(this IServiceCollection services)
        {
            services.AddScoped<ITenantsService, TenantsService>();
            services.AddScoped<IJwtService, JwtService>();
            services.AddScoped<IApisService, ApisService>();
            services.AddScoped<ICurrenciesService, CurrenciesService>();
            services.AddScoped<IApiUsageService, ApiUsageService>();
            services.AddScoped<IBillingService, BillingService>();
            services.AddScoped<IApiKeysService, ApiKeysService>();
            services.AddScoped<ISecurityLogsService, SecurityLogsService>();
            services.AddScoped<IMonitoringMetricsService, MonitoringMetricsService>();
            services.AddScoped<IRolesService, RolesService>();
            services.AddScoped<IPermissionsService, PermissionsService>();
            services.AddScoped<INotificationChannelsService, NotificationChannelsService>();
            services.AddScoped<INotificationsService, NotificationsService>();
            services.AddScoped<IUsersService, UsersService>();
            return services;
        }

        /// <summary>
        /// 注冊應用程式的資料存取層相依性
        /// </summary>
        /// <remarks>
        /// 此方法註冊應用程式中所有的資料存取層（Data Access Component）元件，將接口與實作結合。
        /// 所有 DAC 元件都被註冊為 Scoped 生命週期，確保每個 HTTP 請求中使用的資料庫連線和操作的獨立性。
        /// 此方法涉及的 DAC 元件包含租戶、API、財務、使用紀錄、金鑰管理、安全日誌、監控指標、角色和權限等。
        /// 通過集中註冊，在實作需要變更時只需在此處修改，簡化系統維護。
        /// </remarks>
        /// <param name="services">服務集合物件，用於註冊資料存取層相依性</param>
        private static IServiceCollection DacLayeredDI(this IServiceCollection services)
        {
            services.AddScoped<ITenantsDac, TenantsDac>();
            services.AddScoped<IApisDac, ApisDac>();
            services.AddScoped<ICurrenciesDac, CurrenciesDac>();
            services.AddScoped<IApiUsageDac, ApiUsageDac>();
            services.AddScoped<IBillingDac, BillingDac>();
            services.AddScoped<IApiKeysDac, ApiKeysDac>();
            services.AddScoped<ISecurityLogsDac, SecurityLogsDac>();
            services.AddScoped<IMonitoringMetricsDac, MonitoringMetricsDac>();
            services.AddScoped<IRolesDac, RolesDac>();
            services.AddScoped<IPermissionsDac, PermissionsDac>();
            services.AddScoped<INotificationChannelsDac, NotificationChannelsDac>();
            services.AddScoped<INotificationsDac, NotificationsDac>();
            services.AddScoped<IUsersDac, UsersDac>();
            return services;
        }

        /// <summary>
        /// 設定 AutoMapper 物件映射相依性
        /// </summary>
        /// <remarks>
        /// 此方法設定 AutoMapper 和其設定檔，用於資料模型之間的自動映射。
        /// 此設定用於使用 PaasProfile 類別中定義的所有映射規則，將資料庫實體和傳輸模型間進行自動轉換。
        /// AutoMapper 提供了液體的物件映射機制，簡化了在不同資料模型之間轉換的實作。
        /// 用法為先在 PaasProfile 類別中定義映射規則，然後使用此設定將其註冊到相依性注入容器。
        /// </remarks>
        /// <param name="services">服務集合物件，用於註冊 AutoMapper 相依性</param>
        /// <returns>已設定完成的服務集合</returns>
        private static IServiceCollection MapperLayerDI(this IServiceCollection services)
        {
            services.AddAutoMapper(typeof(PaasProfile));

            return services;
        }
    }
}
