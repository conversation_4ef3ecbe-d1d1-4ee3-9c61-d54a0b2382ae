using Paas.Dac.Interface;
using Paas.Dac;
using Paas.Extensions;
using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Paas.Middleware;
using Microsoft.OpenApi.Models;
using Serilog;
using Paas.Common;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using static Paas.Extensions.HealthCheck;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.EntityFrameworkCore;
using Paas.Data;

var builder = WebApplication.CreateBuilder(args);

// 讀取 Jwt 設定
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]!);

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidAudience = jwtSettings["Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(secretKey)
        };
    });

// 路由改小寫
builder.Services.Configure<RouteOptions>(options =>
{
    options.LowercaseUrls = true;
});

//Configuring Health Check
builder.Services.AddHealthChecks(builder.Configuration);

// 加入授權
builder.Services.AddAuthorization();

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
//builder.Services.AddSwaggerGen();
// 添加 Swagger 並設定 Bearer Token
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Paas API", Version = "v1" });

    // 定義 JWT SecurityScheme
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Name = "Authorization",
        Type = SecuritySchemeType.Http,
        Scheme = "Bearer",
        BearerFormat = "JWT",
        In = ParameterLocation.Header,
        Description = "輸入 JWT Token，格式：Bearer {token}"
    });

    // 套用 JWT 驗證規則
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "Bearer" }
            },
            new string[] { }
        }
    });
});//
// 相依注入
builder.Services.AddApplicationServices(builder.Configuration);

// 配置 Serilog 從 appsettings.json 讀取設定
builder.Host.UseSerilog((context, configuration) =>
{
    configuration.ReadFrom.Configuration(context.Configuration);
});
// 新增HSTS處理
builder.Services.AddHsts(options =>
{
    options.Preload = true;
    options.IncludeSubDomains = true;
    options.MaxAge = TimeSpan.FromDays(365 * 2);
});
// Cookie 處理
builder.Services.ConfigureApplicationCookie(options =>
{
    options.Cookie.SameSite = SameSiteMode.Strict; // 或 Lax
});
// 配置 Data Protection (解決容器重啟時 keys 遺失的問題)
// 將金鑰儲存到資料庫
builder.Services.AddDbContext<DataProtectionKeyContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddDataProtection()
    .PersistKeysToDbContext<DataProtectionKeyContext>()
    .SetApplicationName("PaasAPI");

// 配置 CORS
builder.ConfigureCors();


var app = builder.Build();

// Configure the HTTP request pipeline.
// 檢查是否啟用 Swagger UI (可透過環境變數控制)
var enableSwagger = builder.Configuration.GetValue<bool>("EnableSwagger", app.Environment.IsDevelopment());

if (enableSwagger)
{
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        // 隱藏 Schemas (Models) 區塊
        options.DefaultModelsExpandDepth(-1);
        // 預設收起 API
        options.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
        // 設定 Swagger UI 標題
        options.DocumentTitle = "Paas API Documentation";
        // 設定路由前綴
        options.RoutePrefix = "swagger";
    });
}

if (!app.Environment.IsDevelopment())
{
    app.UseHsts();
}
// Health check endpoint
app.MapHealthChecks("/v1/health", new HealthCheckOptions
{
    ResponseWriter = HealthCheck.HealthCheckResponseWriter.WriteAsync
});

// 使用 ErrorHandleMiddleware
app.UseMiddleware<ExceptionMiddleware>();

// 啟用 CORS - 該放在 UseRouting 之後，UseAuthorization 之前
app.UseCors("DefaultCorsPolicy");

app.UseHttpsRedirection();
//啟用 JWT 身分驗證
app.UseAuthentication();
//啟用授權
app.UseAuthorization();   

app.MapControllers();

app.Run();

// Make Program accessible for testing purposes
public partial class Program { }