using Microsoft.AspNetCore.DataProtection.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Paas.Data;

/// <summary>
/// Represents the database context for data protection keys.
/// </summary>
public class DataProtectionKeyContext : DbContext, IDataProtectionKeyContext
{
    /// <summary>
    /// Initializes a new instance of the <see cref="DataProtectionKeyContext"/> class.
    /// </summary>
    /// <param name="options">The options for this context.</param>
    public DataProtectionKeyContext(DbContextOptions<DataProtectionKeyContext> options)
        : base(options) { }

    /// <summary>
    /// Gets or sets the data protection keys.
    /// </summary>
    public DbSet<DataProtectionKey> DataProtectionKeys { get; set; } = null!;
}
