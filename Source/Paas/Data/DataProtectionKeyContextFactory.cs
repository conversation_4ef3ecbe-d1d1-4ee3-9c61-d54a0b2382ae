using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using System.IO;

namespace Paas.Data;

/// <summary>
/// Factory for creating DataProtectionKeyContext instances at design time.
/// </summary>
public class DataProtectionKeyContextFactory : IDesignTimeDbContextFactory<DataProtectionKeyContext>
{
    /// <summary>
    /// Creates a new instance of a DataProtectionKeyContext.
    /// </summary>
    /// <param name="args">Arguments provided by the design-time service.</param>
    /// <returns>A new DataProtectionKeyContext instance.</returns>
    public DataProtectionKeyContext CreateDbContext(string[] args)
    {
        // When running EF Core tools, the current directory is the project's root.
        IConfigurationRoot configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json")
            .Build();

        var optionsBuilder = new DbContextOptionsBuilder<DataProtectionKeyContext>();
        var connectionString = configuration.GetConnectionString("DefaultConnection");

        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException("The 'DefaultConnection' connection string was not found in appsettings.json.");
        }

        optionsBuilder.UseNpgsql(connectionString);

        return new DataProtectionKeyContext(optionsBuilder.Options);
    }
}
