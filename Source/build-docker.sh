#!/bin/bash

# 設定變數
IMAGE_NAME="paas-api"
TAG="latest"
CONTAINER_NAME="paas-api-container"

echo "🚀 開始建置 Docker Image..."

# 建置 Docker Image
docker build -t ${IMAGE_NAME}:${TAG} .

if [ $? -eq 0 ]; then
    echo "✅ Docker Image 建置成功！"
    echo "📦 Image 名稱: ${IMAGE_NAME}:${TAG}"
    
    # 顯示 Image 資訊
    echo "📊 Image 資訊:"
    docker images ${IMAGE_NAME}:${TAG}
    
    echo ""
    echo "🎯 可用的指令:"
    echo "1. 執行容器: docker run -d -p 8080:8080 --name ${CONTAINER_NAME} ${IMAGE_NAME}:${TAG}"
    echo "2. 使用 docker-compose: docker-compose up -d"
    echo "3. 查看容器日誌: docker logs ${CONTAINER_NAME}"
    echo "4. 停止容器: docker stop ${CONTAINER_NAME}"
    echo "5. 移除容器: docker rm ${CONTAINER_NAME}"
    echo ""
    echo "🌐 API 將在 http://localhost:8080 上運行"
    echo "📋 健康檢查端點: http://localhost:8080/v1/health"
    echo "📚 Swagger UI: http://localhost:8080/swagger"
    echo ""
    echo "🔧 Swagger 控制選項:"
    echo "• 啟用 Swagger: docker run -e EnableSwagger=true -p 8080:8080 ${IMAGE_NAME}:${TAG}"
    echo "• 停用 Swagger: docker run -e EnableSwagger=false -p 8080:8080 ${IMAGE_NAME}:${TAG}"
    echo "• 測試 Swagger 控制: ./test-swagger-control.sh"
    
else
    echo "❌ Docker Image 建置失敗！"
    exit 1
fi
