# APIM 資料庫設計

## 一、租戶與 API 管理

### 1.1 租戶管理 (tenants)
```sql
-- 幣別管理表格 (先行範例，因 tenants 需引用 currencies)
CREATE TABLE currencies (
    -- [系統欄位] 幣別唯一識別碼
    currency_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [必填] 幣別代碼
    code CHAR(3) NOT NULL UNIQUE,
    
    -- [必填] 幣別符號
    symbol VARCHAR(10) NOT NULL,
    
    -- [必填] 匯率
    exchange_rate DECIMAL(10, 6) NOT NULL,
    
    -- [狀態控制] 幣別啟用狀態
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- [時間戳記] 最後更新時間
    last_updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

/* 索引說明：
 * 1. idx_currency_active：優化啟用幣別查詢
 */
CREATE INDEX idx_currency_active ON currencies(is_active) WHERE is_active = TRUE;

/* 使用情境說明：
 * 1. 新增幣別：
 *    INSERT INTO currencies (code, symbol, exchange_rate)
 *    VALUES ('USD', '$', 1.0);
 *
 * 2. 更新幣別匯率：
 *    UPDATE currencies 
 *    SET exchange_rate = 1.1, last_updated_at = CURRENT_TIMESTAMP
 *    WHERE currency_id = 'currency-uuid';
 */

-- 租戶管理表格
CREATE TABLE tenants (
    -- [系統欄位] 自動生成的UUID v7格式唯一識別碼
    tenant_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [必填] 租戶法定名稱，最大長度255字元
    -- 用於合約、發票等正式文件
    name VARCHAR(255) NOT NULL,
    
    -- [唯一約束] 主要聯絡信箱，用於帳單寄送與系統通知
    -- 需確保唯一性，避免重複註冊
    contact_email VARCHAR(255) NOT NULL UNIQUE,
    
    -- [狀態標記] 帳號啟用狀態，預設為true(啟用)
    -- 用於控制租戶訪問權限，false時自動停用所有API存取
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- [關聯欄位] 預設計價幣別，關聯至currencies表
    -- 用於帳單生成時的預設幣別
    billing_currency UUID REFERENCES currencies(currency_id),
    
    -- [JSON結構] 法律認證地址，用於開立發票與法律文件
    -- 格式範例：{"street":"民生東路三段","city":"台北市","country":"TW","postalCode":"104"}
    legal_address JSONB,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

/* 索引說明：
 * 1. idx_tenant_active：用於快速過濾啟用中的租戶
 * 2. idx_tenant_email：加速信箱查詢與唯一性檢查
 */
CREATE INDEX idx_tenant_active ON tenants(is_active) WHERE is_active = TRUE;
CREATE INDEX idx_tenant_email ON tenants(contact_email);

/* 使用情境說明：
 * 1. 新租戶註冊流程：
 *    INSERT INTO tenants (name, contact_email) 
 *    VALUES ('範例科技', '<EMAIL>');
 *
 * 2. 租戶停用處理：
 *    UPDATE tenants 
 *    SET is_active = false, updated_at = CURRENT_TIMESTAMP 
 *    WHERE tenant_id = 'uuid';
 *
 * 3. 更新租戶資訊：
 *    UPDATE tenants 
 *    SET legal_address = '{"street": "新地址"}', updated_at = CURRENT_TIMESTAMP 
 *    WHERE tenant_id = 'uuid';
 *
 * 4. 查詢特定幣別的租戶：
 *    SELECT t.* FROM tenants t 
 *    JOIN currencies c ON t.billing_currency = c.currency_id 
 *    WHERE c.code = 'TWD';
 */
```

### 1.2 API 管理 (apis)
```sql
-- API管理表格：記錄所有租戶註冊的API服務
CREATE TABLE apis (
    -- [系統欄位] API唯一識別碼
    api_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] 所屬租戶識別碼，用於多租戶隔離
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    
    -- [必填] API服務名稱，用於管理介面顯示
    name VARCHAR(255) NOT NULL,
    
    -- [選填] API功能描述，支援多行文字
    description TEXT,
    
    -- [版本控制] API版本號，採用語意化版本格式(SemVer)
    -- 範例：1.0.0、2.1.3，用於API版本管理
    version VARCHAR(20) NOT NULL DEFAULT '1.0.0',
    
    -- [流量管制] 每分鐘最大請求次數
    -- 用於Kong Gateway的rate-limiting插件配置
    rate_limit_per_minute INT NOT NULL DEFAULT 1000,
    
    -- [狀態控制] API啟用狀態
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

/* 索引說明：
 * 1. idx_api_tenant：加速租戶關聯查詢
 * 2. idx_api_version：支援版本號篩選
 */
CREATE INDEX idx_api_tenant ON apis(tenant_id, is_active);
CREATE INDEX idx_api_version ON apis(version);

/* 使用情境說明：
 * 1. 註冊新API服務：
 *    INSERT INTO apis (tenant_id, name, description, rate_limit_per_minute)
 *    VALUES ('tenant-uuid', 'Payment API', '金流交易API服務', 5000);
 *
 * 2. 更新API版本：
 *    UPDATE apis 
 *    SET version = '1.1.0', updated_at = CURRENT_TIMESTAMP
 *    WHERE api_id = 'api-uuid';
 *
 * 3. 調整流量限制：
 *    UPDATE apis 
 *    SET rate_limit_per_minute = 2000, updated_at = CURRENT_TIMESTAMP
 *    WHERE api_id = 'api-uuid';
 *
 * 4. 批次查詢高流量API：
 *    SELECT a.*, t.name as tenant_name 
 *    FROM apis a 
 *    JOIN tenants t ON a.tenant_id = t.tenant_id
 *    WHERE a.rate_limit_per_minute > 5000;
 */
```

---

## 二、安全與認證系統

### 2.1 API 金鑰管理 (api_keys)
```sql
-- API金鑰管理表格：用於管理租戶的API存取憑證
CREATE TABLE api_keys (
    -- [系統欄位] API金鑰唯一識別碼
    key_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] 所屬租戶識別碼
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    
    -- [必填] API金鑰值，需確保系統唯一性
    api_key VARCHAR(255) NOT NULL UNIQUE,
    
    -- [狀態控制] 金鑰狀態，僅允許 'active' 或 'revoked'
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'revoked')),
    
    -- [JSON結構] API存取範圍與權限設定
    scopes JSONB,
    
    -- [時效控制] 金鑰過期時間，null表示永不過期
    expiration_at TIMESTAMPTZ,
    
    -- [時間戳記] 金鑰生成時間
    generated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- [使用追蹤] 最後使用時間
    last_used_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

/* 索引說明：
 * 1. idx_apikey_tenant：加速租戶關聯查詢與狀態篩選
 * 2. idx_apikey_expiration：優化過期金鑰查詢效能
 */
CREATE INDEX idx_apikey_tenant ON api_keys(tenant_id, status);
CREATE INDEX idx_apikey_expiration ON api_keys(expiration_at) WHERE status = 'active';

/* 使用情境說明：
 * 1. 生成新API金鑰：
 *    INSERT INTO api_keys (tenant_id, api_key, status, scopes)
 *    VALUES ('tenant-uuid', 'generated-key', 'active', '{"read": true, "write": false}');
 *
 * 2. 撤銷API金鑰：
 *    UPDATE api_keys 
 *    SET status = 'revoked' 
 *    WHERE key_id = 'key-uuid';
 *
 * 3. 更新最後使用時間：
 *    UPDATE api_keys 
 *    SET last_used_at = CURRENT_TIMESTAMP 
 *    WHERE key_id = 'key-uuid';
 */
```

### 2.2 SSO 整合配置 (sso_configs)
```sql
-- SSO配置表格：管理租戶的單一登入整合設定
CREATE TABLE sso_configs (
    -- [系統欄位] 配置唯一識別碼
    config_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] 所屬租戶識別碼
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    
    -- [必填] SSO提供者類型，限定可用的提供者
    provider_type VARCHAR(50) NOT NULL CHECK (provider_type IN ('google', 'microsoft', 'custom')),
    
    -- [認證資訊] OAuth/OIDC客戶端識別碼
    client_id VARCHAR(255) NOT NULL,
    
    -- [加密存儲] OAuth/OIDC客戶端密鑰
    client_secret BYTEA NOT NULL,
    
    -- [JSON結構] OpenID Connect相關配置
    metadata JSONB,
    
    -- [狀態控制] 配置啟用狀態
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

/* 安全性設定：
 * 1. 啟用行級安全策略，確保租戶只能訪問自己的SSO配置
 * 2. 使用pgcrypto進行client_secret加密存儲 (需另行配置extension及pgp_sym_encrypt等)
 */
ALTER TABLE sso_configs ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolated_sso ON sso_configs 
    USING (tenant_id = current_setting('app.current_tenant')::UUID);

/* 使用情境說明：
 * 1. 新增SSO配置：
 *    INSERT INTO sso_configs (tenant_id, provider_type, client_id, client_secret, metadata)
 *    VALUES (
 *      'tenant-uuid',
 *      'google',
 *      'client-id',
 *      pgp_sym_encrypt('secret', 'key'), 
 *      '{"auth_url": "https://..."}'
 *    );
 *
 * 2. 更新SSO配置：
 *    UPDATE sso_configs 
 *    SET metadata = jsonb_set(metadata, '{auth_url}', '"new-url"'),
 *        updated_at = CURRENT_TIMESTAMP 
 *    WHERE config_id = 'config-uuid';
 */
```

---

## 三、日誌與監控

### 3.1 API 使用記錄 (api_usage)
```sql
-- API使用記錄表格：追蹤所有API呼叫的詳細資訊
CREATE TABLE api_usage (
    -- [系統欄位] 使用記錄唯一識別碼
    usage_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] API與金鑰識別碼
    api_id UUID NOT NULL REFERENCES apis(api_id),
    key_id UUID NOT NULL REFERENCES api_keys(key_id),
    
    -- [網路資訊] 請求來源IP地址
    request_ip INET NOT NULL,
    
    -- [效能指標] API響應時間（毫秒）
    response_time FLOAT NOT NULL,
    
    -- [狀態資訊] HTTP狀態碼
    status_code INT NOT NULL,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (created_at);

/* 分區策略說明：
 * 1. 按月份進行分區，優化查詢效能
 * 2. 自動化分區管理，便於歷史資料存檔
 */
CREATE TABLE api_usage_202402 PARTITION OF api_usage
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

/* 索引說明：
 * 1. idx_usage_api：優化API使用統計查詢
 * 2. idx_usage_key：加速金鑰使用分析
 */
CREATE INDEX idx_usage_api ON api_usage(api_id, created_at);
CREATE INDEX idx_usage_key ON api_usage(key_id, created_at);

/* 使用情境說明：
 * 1. 記錄API呼叫：
 *    INSERT INTO api_usage (api_id, key_id, request_ip, response_time, status_code)
 *    VALUES ('api-uuid', 'key-uuid', '***********', 150.5, 200);
 *
 * 2. 分析API效能：
 *    SELECT api_id, AVG(response_time) as avg_response_time
 *    FROM api_usage
 *    WHERE created_at >= NOW() - INTERVAL '1 hour'
 *    GROUP BY api_id;
 */
```

### 3.2 安全性日誌 (security_logs)
```sql
-- 安全事件類型表格：定義系統支援的安全事件類型
CREATE TABLE security_event_types (
    -- [系統欄位] 事件類型唯一識別碼
    type_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [必填] 事件類型名稱
    name VARCHAR(50) NOT NULL UNIQUE,
    
    -- [選填] 事件類型描述
    description TEXT,
    
    -- [分級控制] 事件嚴重程度（1-5）
    severity INT CHECK (severity BETWEEN 1 AND 5),
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 需要先有使用者表，見下方 6.1 (users)，本範例先示意

-- 安全日誌表格：記錄系統中的所有安全相關事件
CREATE TABLE security_logs (
    -- [系統欄位] 日誌唯一識別碼
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] 租戶與使用者識別碼
    tenant_id UUID REFERENCES tenants(tenant_id),
    user_id UUID REFERENCES users(user_id),
    
    -- [必填] 事件類型識別碼
    event_type_id UUID NOT NULL REFERENCES security_event_types(type_id),
    
    -- [網路資訊] 事件來源IP地址
    ip_address INET NOT NULL,
    
    -- [使用者代理] 事件來源用戶代理
    user_agent TEXT,
    
    -- [事件詳細] JSON格式的事件詳細資訊
    details JSONB NOT NULL,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (created_at);

/* 分區策略說明：
 * 1. 按月份進行分區，優化查詢效能
 * 2. 自動化分區管理，便於歷史資料存檔
 */
CREATE TABLE security_logs_202402 PARTITION OF security_logs
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

/* 索引說明：
 * 1. idx_security_tenant：優化租戶安全事件查詢
 * 2. idx_security_type：加速事件類型分析
 */
CREATE INDEX idx_security_tenant ON security_logs(tenant_id, created_at);
CREATE INDEX idx_security_type ON security_logs(event_type_id, created_at);

/* 使用情境說明：
 * 1. 記錄安全事件：
 *    INSERT INTO security_logs (tenant_id, user_id, event_type_id, ip_address, user_agent, details)
 *    VALUES ('tenant-uuid', 'user-uuid', 'event-type-uuid', '***********', 'Mozilla/5.0', '{"key": "value"}');
 *
 * 2. 分析安全事件：
 *    SELECT event_type_id, COUNT(*) as count
 *    FROM security_logs
 *    WHERE created_at >= NOW() - INTERVAL '1 day'
 *    GROUP BY event_type_id;
 */
```

### 3.3 監控指標系統
```sql
-- 監控指標類型表格：定義系統支援的監控指標類型
CREATE TABLE metric_types (
    -- [系統欄位] 指標類型唯一識別碼
    type_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [必填] 指標類型名稱
    name VARCHAR(50) NOT NULL UNIQUE,
    
    -- [選填] 指標類型描述
    description TEXT,
    
    -- [單位控制] 指標單位
    unit VARCHAR(20) NOT NULL,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 監控指標資料表格：記錄系統中的所有監控指標資料
CREATE TABLE metrics_data (
    -- [系統欄位] 資料唯一識別碼
    data_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] 指標類型識別碼
    metric_type_id UUID NOT NULL REFERENCES metric_types(type_id),
    
    -- [關聯欄位] 租戶與API識別碼
    tenant_id UUID REFERENCES tenants(tenant_id),
    api_id UUID REFERENCES apis(api_id),
    
    -- [數值欄位] 指標值
    value DOUBLE PRECISION NOT NULL,
    
    -- [標籤欄位] JSON格式的標籤資訊
    tags JSONB,
    
    -- [時間戳記] 資料時間戳記
    timestamp TIMESTAMPTZ NOT NULL,
    
    -- (修正) 單一主鍵為 data_id，不含 timestamp
    -- PRIMARY KEY (data_id),
    
    -- 分區依據 timestamp
) PARTITION BY RANGE (timestamp);

/* 分區策略說明：
 * 1. 按月份進行分區，優化查詢效能
 * 2. 自動化分區管理，便於歷史資料存檔
 */
CREATE TABLE metrics_data_202402 PARTITION OF metrics_data
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

/* 索引說明：
 * 1. idx_metrics_tenant：優化租戶監控指標查詢
 * 2. idx_metrics_api：加速API監控指標分析
 * 3. idx_metrics_type：優化指標類型查詢
 */
CREATE INDEX idx_metrics_tenant ON metrics_data(tenant_id, timestamp);
CREATE INDEX idx_metrics_api ON metrics_data(api_id, timestamp);
CREATE INDEX idx_metrics_type ON metrics_data(metric_type_id, timestamp);

/* 使用情境說明：
 * 1. 記錄監控指標：
 *    INSERT INTO metrics_data (metric_type_id, tenant_id, api_id, value, tags, timestamp)
 *    VALUES (
 *      'metric-type-uuid', 
 *      'tenant-uuid', 
 *      'api-uuid', 
 *      10.5, 
 *      '{"key": "value"}', 
 *      '2024-02-01 12:00:00'
 *    );
 *
 * 2. 分析監控指標：
 *    SELECT metric_type_id, AVG(value) as avg_value
 *    FROM metrics_data
 *    WHERE timestamp >= NOW() - INTERVAL '1 hour'
 *    GROUP BY metric_type_id;
 */

-- (修正) 使用 tags 表時，需要引用 metrics_data(data_id)
CREATE TABLE tags (
    -- [系統欄位] 標籤唯一識別碼
    tag_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] 監控指標資料識別碼 (修正: 對應 data_id)
    metric_data_id UUID NOT NULL REFERENCES metrics_data(data_id),
    
    -- [必填] 標籤名稱
    name VARCHAR(50) NOT NULL,
    
    -- [必填] 標籤值
    value VARCHAR(255) NOT NULL,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(metric_data_id, name)
);

/* 索引說明：
 * 1. idx_tag_metric：加速指標標籤查詢
 */
CREATE INDEX idx_tag_metric ON tags(metric_data_id);

/* 使用情境說明：
 * 1. 新增指標標籤：
 *    INSERT INTO tags (metric_data_id, name, value)
 *    VALUES ('data-uuid', 'environment', 'production');
 */
```

---

## 四、計費與金流系統

### 4.1 付款方式 (payment_methods) 與租戶付款設定
```sql
-- 付款方式管理表格：管理系統支援的付款方式
CREATE TABLE payment_methods (
    -- [系統欄位] 付款方式唯一識別碼
    method_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [必填] 付款方式名稱
    name VARCHAR(50) NOT NULL UNIQUE,
    
    -- [選填] 付款方式描述
    description TEXT,
    
    -- [狀態控制] 付款方式啟用狀態
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 租戶付款方式設定表格：管理租戶的付款方式設定
CREATE TABLE tenant_payment_methods (
    -- [關聯欄位] 租戶識別碼
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    
    -- [關聯欄位] 付款方式識別碼
    method_id UUID NOT NULL REFERENCES payment_methods(method_id),
    
    -- [設定欄位] 付款方式設定
    settings JSONB,
    
    -- [狀態控制] 預設付款方式
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (tenant_id, method_id)
);

/* 使用情境說明：
 * 1. 新增付款方式：
 *    INSERT INTO payment_methods (name, description)
 *    VALUES ('信用卡', '信用卡付款');
 *
 * 2. 設定租戶付款方式：
 *    INSERT INTO tenant_payment_methods (tenant_id, method_id, settings)
 *    VALUES ('tenant-uuid', 'method-uuid', '{"key": "value"}');
 */
```

### 4.2 帳單記錄 (billing)
```sql
-- 帳單記錄表格：管理系統中的所有帳單記錄
CREATE TABLE billing_records (
    -- [系統欄位] 帳單記錄唯一識別碼
    record_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] 租戶識別碼
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    
    -- [數值欄位] 帳單金額
    amount DECIMAL(10, 2) NOT NULL,
    
    -- [關聯欄位] 幣別識別碼
    currency_id UUID NOT NULL REFERENCES currencies(currency_id),
    
    -- [關聯欄位] 付款方式識別碼
    payment_method_id UUID NOT NULL REFERENCES payment_methods(method_id),
    
    -- [狀態控制] 帳單狀態
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'paid', 'failed', 'cancelled')),
    
    -- [時間戳記] 帳單到期時間
    due_date TIMESTAMPTZ NOT NULL,
    
    -- [時間戳記] 帳單支付時間
    paid_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

/* 索引說明：
 * 1. idx_billing_tenant：優化租戶帳單查詢
 * 2. idx_billing_status：加速帳單狀態查詢
 */
CREATE INDEX idx_billing_tenant ON billing_records(tenant_id, created_at);
CREATE INDEX idx_billing_status ON billing_records(status, due_date);

/* 使用情境說明：
 * 1. 生成新帳單：
 *    INSERT INTO billing_records (tenant_id, amount, currency_id, payment_method_id, due_date)
 *    VALUES ('tenant-uuid', 100.0, 'currency-uuid', 'method-uuid', '2024-02-01');
 *
 * 2. 更新帳單狀態：
 *    UPDATE billing_records 
 *    SET status = 'paid', paid_at = CURRENT_TIMESTAMP
 *    WHERE record_id = 'record-uuid';
 */
```

---

## 五、通知系統

### 5.1 通知管理 (notifications)
```sql
-- 通知類型管理表格：管理系統支援的通知類型
CREATE TABLE notification_types (
    -- [系統欄位] 通知類型唯一識別碼
    type_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [必填] 通知類型名稱
    name VARCHAR(50) NOT NULL UNIQUE,
    
    -- [必填] 通知模板
    template TEXT NOT NULL,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 通知管理表格：管理系統中的所有通知
CREATE TABLE notifications (
    -- [系統欄位] 通知唯一識別碼
    notification_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] 通知類型識別碼
    type_id UUID NOT NULL REFERENCES notification_types(type_id),
    
    -- [關聯欄位] 租戶與使用者識別碼
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    user_id UUID NOT NULL REFERENCES users(user_id),
    
    -- [必填] 通知標題
    title VARCHAR(255) NOT NULL,
    
    -- [必填] 通知內容
    message TEXT NOT NULL,
    
    -- [狀態控制] 通知狀態
    status VARCHAR(20) NOT NULL CHECK (status IN ('unread', 'read')),
    
    -- [優先控制] 通知優先級
    priority VARCHAR(20) NOT NULL CHECK (priority IN ('low', 'medium', 'high')),
    
    -- [時間戳記] 通知過期時間
    expires_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

/* 索引說明：
 * 1. idx_notification_user：優化使用者通知查詢
 * 2. idx_notification_expires：加速通知過期查詢
 */
CREATE INDEX idx_notification_user ON notifications(user_id, status, created_at);
CREATE INDEX idx_notification_expires ON notifications(expires_at) 
    WHERE status = 'unread' AND expires_at IS NOT NULL;

/* 使用情境說明：
 * 1. 生成新通知：
 *    INSERT INTO notifications (type_id, tenant_id, user_id, title, message)
 *    VALUES ('type-uuid', 'tenant-uuid', 'user-uuid', '標題', '內容');
 *
 * 2. 更新通知狀態：
 *    UPDATE notifications 
 *    SET status = 'read'
 *    WHERE notification_id = 'notification-uuid';
 */
```

---

## 六、使用者與權限管理

### 6.1 角色管理 (roles)
```sql
-- 角色管理表格：定義系統中的角色與其描述
CREATE TABLE roles (
    -- [系統欄位] 角色唯一識別碼
    role_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [必填] 角色名稱，如 admin, tenant_admin
    name VARCHAR(50) NOT NULL UNIQUE,
    
    -- [選填] 角色描述
    description TEXT,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

/* 使用情境說明：
 * 1. 建立新角色：
 *    INSERT INTO roles (name, description)
 *    VALUES ('tenant_admin', '租戶管理員，可管理租戶內所有資源');
 */
```

### 6.2 使用者管理 (users)
```sql
-- 使用者管理表格：管理系統中所有使用者的基本資訊與認證資料
CREATE TABLE users (
    -- [系統欄位] 使用者唯一識別碼
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] 所屬租戶識別碼，可為空表示系統管理員
    tenant_id UUID REFERENCES tenants(tenant_id),
    
    -- [必填] 使用者名稱，用於登入與顯示
    username VARCHAR(255) NOT NULL UNIQUE,
    
    -- [必填] 使用者電子郵件，用於通知與重設密碼
    email VARCHAR(255) NOT NULL UNIQUE,
    
    -- [必填] 密碼雜湊值，使用安全雜湊算法
    password_hash VARCHAR(255) NOT NULL,
    
    -- [關聯欄位] 使用者角色識別碼 (單一角色)
    role_id UUID NOT NULL REFERENCES roles(role_id),
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

/* 索引說明：
 * 1. idx_user_tenant：加速租戶使用者查詢
 * 2. idx_user_email：優化電子郵件查詢
 */
CREATE INDEX idx_user_tenant ON users(tenant_id);
CREATE INDEX idx_user_email ON users(email);

/* 使用情境說明：
 * 1. 建立新使用者：
 *    INSERT INTO users (tenant_id, username, email, password_hash, role_id)
 *    VALUES ('tenant-uuid', 'john_doe', '<EMAIL>', 'hashed_password', 'role-uuid');
 *
 * 2. 更新使用者角色：
 *    UPDATE users 
 *    SET role_id = 'new-role-uuid', updated_at = CURRENT_TIMESTAMP
 *    WHERE user_id = 'user-uuid';
 */
```

### 6.3 權限管理 (permissions)
```sql
-- 權限管理表格：定義各角色的細粒度權限
CREATE TABLE permissions (
    -- [系統欄位] 權限唯一識別碼
    permission_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [關聯欄位] 所屬角色識別碼
    role_id UUID NOT NULL REFERENCES roles(role_id),
    
    -- [必填] 資源名稱，如 api, billing
    resource VARCHAR(50) NOT NULL,
    
    -- [必填] 操作類型，如 read, write
    action VARCHAR(50) NOT NULL,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(role_id, resource, action)
);

/* 索引說明：
 * 1. idx_permission_role：加速角色權限查詢
 */
CREATE INDEX idx_permission_role ON permissions(role_id);

/* 使用情境說明：
 * 1. 設定角色權限：
 *    INSERT INTO permissions (role_id, resource, action)
 *    VALUES ('role-uuid', 'api', 'read');
 *
 * 2. 移除角色權限：
 *    DELETE FROM permissions 
 *    WHERE role_id = 'role-uuid' AND resource = 'api' AND action = 'write';
 */
```

### 6.4 通知管道設定 (notification_channels)
```sql
-- 通知管道設定表格：管理不同類型的通知發送管道
CREATE TABLE notification_channels (
    -- [系統欄位] 通知管道唯一識別碼
    channel_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    
    -- [必填] 管道類型，如 email, sms, in_app
    type VARCHAR(50) NOT NULL,
    
    -- [必填] 管道設定，JSON格式儲存配置資訊
    config JSONB NOT NULL,
    
    -- [狀態控制] 管道啟用狀態
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

/* 使用情境說明：
 * 1. 新增通知管道：
 *    INSERT INTO notification_channels (type, config)
 *    VALUES ('email', '{"smtp_host": "smtp.example.com", "port": 587}');
 */
```

---

## 七、系統關聯圖（Mermaid）

```mermaid
erDiagram
    %% 租戶與核心管理
    tenants ||--o{ users : "管理使用者"
    tenants ||--o{ apis : "擁有API"
    tenants ||--o{ api_keys : "發行金鑰"
    tenants ||--o{ billing_records : "產生帳單"
    tenants ||--o{ tenant_payment_methods : "設定付款方式"
    tenants ||--o{ security_logs : "稽核記錄"
    tenants ||--o{ metrics_data : "監控數據"
    tenants ||--o{ notifications : "接收通知"
    tenants ||--o{ sso_configs : "設定SSO"

    %% 使用者與角色權限
    roles ||--o{ users : "授權角色"
    roles ||--o{ permissions : "定義權限"
    users ||--o{ security_logs : "產生事件"
    users ||--o{ notifications : "接收通知"

    %% API 管理
    apis ||--o{ api_usage : "記錄請求"
    apis ||--o{ metrics_data : "監控效能"
    api_keys ||--o{ api_usage : "驗證請求"

    %% 安全性
    security_event_types ||--o{ security_logs : "分類事件"

    %% 監控
    metric_types ||--o{ metrics_data : "定義指標"
    metrics_data ||--o{ tags : "標記分類"

    %% 金流
    currencies ||--o{ billing_records : "計價幣別"
    payment_methods ||--o{ tenant_payment_methods : "提供方式"
    payment_methods ||--o{ billing_records : "處理付款"

    %% 通知
    notification_types ||--o{ notifications : "定義類型"
    notifications }|--|| notification_channels : "發送管道"
```