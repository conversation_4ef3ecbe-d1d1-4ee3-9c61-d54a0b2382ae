#!/bin/bash

# API 認證測試腳本

echo "🔐 開始測試 API 認證功能..."

API_BASE_URL="http://localhost:8080"

# 檢查 API 是否運行
echo "🔍 檢查 API 服務狀態..."
if ! curl -s "$API_BASE_URL/v1/health" > /dev/null; then
    echo "❌ API 服務未運行，請先執行: docker-compose -f docker-compose.dev.yml up -d"
    exit 1
fi

echo "✅ API 服務正在運行"

# 測試未認證的端點
echo ""
echo "📋 測試 1: 訪問未認證的端點"
echo "🔗 GET $API_BASE_URL/v1/health"
HEALTH_RESPONSE=$(curl -s "$API_BASE_URL/v1/health")
echo "✅ 健康檢查: $HEALTH_RESPONSE"

# 測試需要認證的端點 (應該返回 401)
echo ""
echo "📋 測試 2: 訪問需要認證的端點 (無 Token)"
echo "🔗 GET $API_BASE_URL/v1/apis"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE_URL/v1/apis")
if [ "$HTTP_CODE" = "401" ]; then
    echo "✅ 正確返回 401 Unauthorized (需要認證)"
else
    echo "❌ 預期 401，實際收到 $HTTP_CODE"
fi

# 登入獲取 Token
echo ""
echo "📋 測試 3: 登入獲取 JWT Token"
echo "🔗 POST $API_BASE_URL/v1/login"

LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE_URL/v1/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "string",
    "password": "string"
  }')

echo "📄 登入回應: $LOGIN_RESPONSE"

# 提取 Token
TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    # 嘗試大寫 Token
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"Token":"[^"]*"' | cut -d'"' -f4)
fi

if [ -z "$TOKEN" ]; then
    echo "❌ 無法獲取 JWT Token"
    echo "登入回應: $LOGIN_RESPONSE"
    exit 1
fi

echo "✅ 成功獲取 JWT Token: ${TOKEN:0:50}..."

# 測試受保護的登入端點
echo ""
echo "📋 測試 4: 使用 Token 訪問受保護的登入端點"
echo "🔗 GET $API_BASE_URL/v1/login/protected"

PROTECTED_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_BASE_URL/v1/login/protected")
echo "📄 受保護端點回應: $PROTECTED_RESPONSE"

if echo "$PROTECTED_RESPONSE" | grep -q "成功"; then
    echo "✅ 成功訪問受保護的端點"
else
    echo "❌ 訪問受保護端點失敗"
fi

# 測試其他需要認證的端點
echo ""
echo "📋 測試 5: 使用 Token 訪問 APIs 端點 (帶參數)"
echo "🔗 GET $API_BASE_URL/v1/apis?limit=5&offset=0"

APIS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_BASE_URL/v1/apis?limit=5&offset=0")
APIS_HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $TOKEN" "$API_BASE_URL/v1/apis?limit=5&offset=0")
echo "📊 APIs 端點 HTTP 狀態碼: $APIS_HTTP_CODE"

if [ "$APIS_HTTP_CODE" = "200" ]; then
    echo "✅ 成功訪問 APIs 端點"
    echo "📄 回應: $APIS_RESPONSE"
elif [ "$APIS_HTTP_CODE" = "500" ]; then
    echo "⚠️  伺服器內部錯誤 (可能是資料庫查詢問題)"
else
    echo "❌ 訪問 APIs 端點失敗，HTTP 狀態碼: $APIS_HTTP_CODE"
fi

# 測試角色端點
echo ""
echo "📋 測試 6: 使用 Token 訪問 Roles 端點"
echo "🔗 GET $API_BASE_URL/api/roles"

ROLES_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_BASE_URL/api/roles")
ROLES_HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $TOKEN" "$API_BASE_URL/api/roles")
echo "📊 Roles 端點 HTTP 狀態碼: $ROLES_HTTP_CODE"

if [ "$ROLES_HTTP_CODE" = "200" ]; then
    echo "✅ 成功訪問 Roles 端點"
    echo "📄 回應: $ROLES_RESPONSE"
elif [ "$ROLES_HTTP_CODE" = "500" ]; then
    echo "⚠️  伺服器內部錯誤 (可能是資料庫查詢問題)"
else
    echo "❌ 訪問 Roles 端點失敗，HTTP 狀態碼: $ROLES_HTTP_CODE"
fi

# 測試租戶端點
echo ""
echo "📋 測試 7: 使用 Token 訪問 Tenants 端點"
echo "🔗 GET $API_BASE_URL/v1/tenants?limit=5&offset=0"

TENANTS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_BASE_URL/v1/tenants?limit=5&offset=0")
TENANTS_HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $TOKEN" "$API_BASE_URL/v1/tenants?limit=5&offset=0")
echo "📊 Tenants 端點 HTTP 狀態碼: $TENANTS_HTTP_CODE"

if [ "$TENANTS_HTTP_CODE" = "200" ]; then
    echo "✅ 成功訪問 Tenants 端點"
    echo "📄 回應: $TENANTS_RESPONSE"
elif [ "$TENANTS_HTTP_CODE" = "500" ]; then
    echo "⚠️  伺服器內部錯誤 (可能是資料庫查詢問題)"
else
    echo "❌ 訪問 Tenants 端點失敗，HTTP 狀態碼: $TENANTS_HTTP_CODE"
fi

# 測試無效 Token
echo ""
echo "📋 測試 8: 使用無效 Token"
echo "🔗 GET $API_BASE_URL/v1/apis (使用無效 Token)"

INVALID_HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer invalid-token" "$API_BASE_URL/v1/apis?limit=5&offset=0")
echo "📊 無效 Token HTTP 狀態碼: $INVALID_HTTP_CODE"

if [ "$INVALID_HTTP_CODE" = "401" ]; then
    echo "✅ 正確拒絕無效 Token"
else
    echo "❌ 預期 401，實際收到 $INVALID_HTTP_CODE"
fi

echo ""
echo "🎉 API 認證測試完成！"
echo ""
echo "📚 使用說明:"
echo "1. 登入端點: POST $API_BASE_URL/v1/login"
echo "   請求體: {\"email\": \"string\", \"password\": \"string\"}"
echo ""
echo "2. 使用 Token 訪問受保護端點:"
echo "   Header: Authorization: Bearer <your-jwt-token>"
echo ""
echo "3. 在 Swagger UI 中使用:"
echo "   - 訪問: $API_BASE_URL/swagger"
echo "   - 點擊右上角 'Authorize' 按鈕"
echo "   - 輸入: Bearer <your-jwt-token>"
