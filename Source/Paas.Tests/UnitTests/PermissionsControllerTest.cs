﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Paas.Controllers;
using Paas.Model;
using Paas.Service;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Paas.Tests.UnitTests
{
    public class PermissionsControllerTest
    {
        private readonly Mock<IPermissionsService> _serviceMock;
        private readonly IMapper _mapper;
        private readonly PermissionsController _controller;

        public PermissionsControllerTest()
        {
            _serviceMock = new Mock<IPermissionsService>();
            _mapper = new MapperConfiguration(cfg => cfg.AddProfile<PaasProfile>()).CreateMapper();
            _controller = new PermissionsController(_serviceMock.Object, _mapper);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WhenPermissionsExist()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var permissionsList = new List<PermissionsModel>
            {
                new PermissionsModel { permission_id = Guid.NewGuid(), role_id = roleId, resource = "Resource1", action = "Action1" },
                new PermissionsModel { permission_id = Guid.NewGuid(), role_id = roleId, resource = "Resource2", action = "Action2" }
            };

            _serviceMock.Setup(s => s.GetPermissionsList(roleId)).ReturnsAsync(permissionsList);

            // Act
            var result = await _controller.Get(roleId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<List<PermissionsViewModel>>(okResult.Value);
            Assert.Equal(permissionsList.Count, returnValue.Count);
        }

        [Fact]
        public async Task Get_ReturnsNotFound_WhenNoPermissionsExist()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            _serviceMock.Setup(s => s.GetPermissionsList(roleId)).ReturnsAsync(new List<PermissionsModel>());

            // Act
            var result = await _controller.Get(roleId);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }
    }
}
