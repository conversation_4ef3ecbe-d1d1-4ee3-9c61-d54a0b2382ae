﻿using AutoMapper;
using Moq;
using Paas.Controllers;
using Paas.Model;
using Paas.Service;
using Xunit;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Paas.Tests.UnitTests;

public class NotificationsControllerTest
{
    private readonly Mock<INotificationsService> _mockService;
    private readonly IMapper _mapper;
    private readonly NotificationsController _controller;

    public NotificationsControllerTest()
    {
        _mockService = new Mock<INotificationsService>();
        _mapper = new MapperConfiguration(cfg => cfg.AddProfile<PaasProfile>()).CreateMapper();
        _controller = new NotificationsController(_mockService.Object, _mapper);
    }

    [Fact]
    public async Task GetNotifications_ReturnsMappedList()
    {
        var model = new NotificationsModel
        {
            notification_id = Guid.NewGuid(),
            type_id = Guid.NewGuid(),
            tenant_id = Guid.NewGuid(),
            user_id = Guid.NewGuid(),
            title = "Test Title",
            message = "Test Message",
            status = "unread",
            priority = "medium",
            expires_at_dt = DateTimeOffset.UtcNow.AddDays(1)
        };

        var query = new NotificationsQueryModel
        {
            tenant_id = model.tenant_id,
            user_id = model.user_id,
            status = model.status,
            priority = model.priority
        };

        _mockService.Setup(s => s.GetNotifications(It.IsAny<NotificationsModel>()))
            .ReturnsAsync(new List<NotificationsModel> { model });

        var result = await _controller.GetNotifications(query);

        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnValue = Assert.IsAssignableFrom<IList<NotificationsViewModel>>(okResult.Value);
        Assert.Single(returnValue);
        Assert.Equal(model.title, returnValue[0].title);
    }

    [Fact]
    public async Task GetNotification_Existing_ReturnsOk()
    {
        var model = new NotificationsModel
        {
            notification_id = Guid.NewGuid(),
            title = "Hello",
            message = "World",
            expires_at_dt = DateTimeOffset.UtcNow.AddDays(1)
        };

        _mockService.Setup(s => s.GetNotification(model.notification_id)).ReturnsAsync(model);

        var result = await _controller.GetNotification(model.notification_id);

        var okResult = Assert.IsType<OkObjectResult>(result);
        var viewModel = Assert.IsType<NotificationsViewModel>(okResult.Value);
        Assert.Equal(model.notification_id, viewModel.notification_id);
    }

    [Fact]
    public async Task GetNotification_NotFound_Returns404()
    {
        _mockService.Setup(s => s.GetNotification(It.IsAny<Guid>()))
            .ReturnsAsync((NotificationsModel?)null);

        var result = await _controller.GetNotification(Guid.NewGuid());

        Assert.IsType<NotFoundResult>(result);
    }

    [Fact]
    public async Task InsertNotification_Success_Returns201()
    {
        var createModel = new NotificationsCreateModel
        {
            type_id = Guid.NewGuid(),
            tenant_id = Guid.NewGuid(),
            user_id = Guid.NewGuid(),
            title = "New Notification",
            message = "You got a message",
            priority = "high",
            expires_at = DateTimeOffset.UtcNow.AddDays(2)
        };

        var inserted = _mapper.Map<NotificationsModel>(createModel);
        inserted.notification_id = Guid.NewGuid();
        inserted.expires_at_dt = createModel.expires_at;

        _mockService.Setup(s => s.InsertNotification(It.IsAny<NotificationsModel>()))
            .ReturnsAsync(inserted);

        var result = await _controller.InsertNotification(createModel);

        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(201, statusResult.StatusCode);
        var viewModel = Assert.IsType<NotificationsViewModel>(statusResult.Value);
        Assert.Equal(createModel.title, viewModel.title);
    }

    [Fact]
    public async Task InsertNotification_Failure_Returns500()
    {
        _mockService.Setup(s => s.InsertNotification(It.IsAny<NotificationsModel>()))
            .ReturnsAsync((NotificationsModel?)null);

        var createModel = new NotificationsCreateModel
        {
            type_id = Guid.NewGuid(),
            tenant_id = Guid.NewGuid(),
            user_id = Guid.NewGuid(),
            title = "Fail",
            message = "Something went wrong",
            priority = "low",
            expires_at = DateTimeOffset.UtcNow.AddDays(1)
        };

        var result = await _controller.InsertNotification(createModel);

        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(500, statusResult.StatusCode);
    }
}
