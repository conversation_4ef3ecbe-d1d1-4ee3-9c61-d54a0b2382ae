﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Paas.Controllers;
using Paas.Dac;
using Paas.Model;
using Paas.Service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Tests.UnitTests
{
    public class SecurityLogsControllerTest
    {
        private readonly Mock<ISecurityLogsService> _mockService;
        private readonly IMapper _mapper;
        private readonly SecurityLogsController _controller;

        public SecurityLogsControllerTest()
        {
            _mockService = new Mock<ISecurityLogsService>();
            _mapper = new MapperConfiguration(cfg => cfg.AddProfile<PaasProfile>())
                .CreateMapper() ;
            _controller = new SecurityLogsController(_mockService.Object, _mapper);
        }

        [Fact]
        public async Task GetSecurityLogs_ReturnsOk_WhenDataExists()
        {
            // Arrange
            var securityLogs = new SecurityLogsModel()
            {
                log_id = new Guid("0195efc6-3933-7fbf-8c36-36dcac5ef232")
            };

            var expection = new List<SecurityLogsModel>();

            _mockService
                .Setup(s => s.GetSecurityLogs(securityLogs))
                .ReturnsAsync(expection);

            // Act
            var result = (await _controller.GetSecurityLogs(new SecurityLogsQueryModel())) as OkObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result?.StatusCode);
        }

        [Fact]
        public async Task GetSecurityLog_ReturnsOkResult_WhenDataExists()
        {
            // Arrange
            var logId = Guid.NewGuid();
            var mockModel = new SecurityLogsModel
            {
                log_id = logId
            };
            var mockViewModel = new SecurityLogsViewModel
            {
                log_id = logId
            };

            // 設定 service 以模擬返回資料
            _mockService.Setup(s => s.GetSecurityLog(logId)).ReturnsAsync(mockModel);

            // Act
            var result = await _controller.GetSecurityLog(logId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<SecurityLogsViewModel>(okResult.Value);
            Assert.Equal(mockViewModel.log_id, returnValue.log_id);

            _mockService.Verify(s => s.GetSecurityLog(logId), Times.Once);
        }

        [Fact]
        public async Task GetSecurityLog_ReturnsNotFound_WhenDataDoesNotExist()
        {
            // Arrange
            var logId = Guid.NewGuid();

            // 設定 service 返回 null，表示資料不存在
            _mockService.Setup(s => s.GetSecurityLog(logId)).ReturnsAsync((SecurityLogsModel?)null);

            // Act
            var result = await _controller.GetSecurityLog(logId);

            // Assert
            Assert.IsType<NotFoundResult>(result);

            _mockService.Verify(s => s.GetSecurityLog(logId), Times.Once);
        }
    }

}
