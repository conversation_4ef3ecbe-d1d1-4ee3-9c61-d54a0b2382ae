using Moq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Paas.Controllers;
using Paas.Service;
using Xunit;
using Microsoft.AspNetCore.Identity.Data;
using Newtonsoft.Json.Linq;

namespace Paas.Tests.UnitTests
{
    public class LoginControllerTest
    {
        private readonly Mock<ILogger<LoginController>> _mockLogger;
        private readonly Mock<IJwtService> _mockJwtService;
        private readonly LoginController _controller;

        public LoginControllerTest()
        {
            _mockLogger = new Mock<ILogger<LoginController>>();
            _mockJwtService = new Mock<IJwtService>();
            _controller = new LoginController(_mockLogger.Object, _mockJwtService.Object);
        }

        [Fact]
        public void Login_ValidCredentials_ReturnsOkWithToken()
        {
            // Arrange
            var request = new LoginRequest { Email = "string", Password = "string" };
            var expectedToken = "mocked_jwt_token";
            _mockJwtService.Setup(s => s.GenerateToken(request.Email)).Returns(expectedToken);

            // Act
            var result = _controller.Login(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = JObject.FromObject(okResult.Value);
            Assert.Equal(expectedToken, returnValue["token"]?.ToString());
        }

        [Fact]
        public void Login_InvalidCredentials_ReturnsUnauthorized()
        {
            // Arrange
            var request = new LoginRequest { Email = "<EMAIL>", Password = "wrongpassword" };

            // Act
            var result = _controller.Login(request);

            // Assert
            var unauthorizedResult = Assert.IsType<UnauthorizedObjectResult>(result);
            Assert.Equal(401, unauthorizedResult.StatusCode);
            var returnValue = JObject.FromObject(unauthorizedResult.Value);
            Assert.Equal("帳號或密碼錯誤", returnValue["message"]?.ToString());
        }

        [Fact]
        public void ProtectedEndpoint_ReturnsOk()
        {
            // Act
            var result = _controller.ProtectedEndpoint();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = JObject.FromObject(okResult.Value);
            Assert.Equal("你已成功訪問受保護的 API！", returnValue["message"]?.ToString());
        }
    }
}