﻿using AutoMapper;
using Moq;
using Paas.Controllers;
using Paas.Model;
using Paas.Service;
using Xunit;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Paas.Tests.UnitTests;

public class NotificationChannelsControllerTest
{
    private readonly Mock<INotificationChannelsService> _mockService;
    private readonly IMapper _mapper;
    private readonly NotificationChannelsController _controller;

    public NotificationChannelsControllerTest()
    {
        _mockService = new Mock<INotificationChannelsService>();
        _mapper = new MapperConfiguration(cfg => cfg.AddProfile<PaasProfile>()).CreateMapper();
        _controller = new NotificationChannelsController(_mockService.Object, _mapper);
    }

    [Fact]
    public async Task GetNotificationChannels_ReturnsMappedList()
    {
        var model = new NotificationChannelsModel
        {
            channel_id = Guid.NewGuid(),
            type = "email",
            config = new NotificationChannelsConfig { smtp_host = "smtp.example.com", port = 587 },
            is_active = true
        };

        var query = new NotificationChannelsQueryModel
        {
            is_active = model.is_active
        };

        _mockService.Setup(s => s.GetNotificationChannels(It.IsAny<NotificationChannelsModel>()))
            .ReturnsAsync(new List<NotificationChannelsModel> { model });

        var result = await _controller.GetNotificationChannels(query);

        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnValue = Assert.IsAssignableFrom<IList<NotificationChannelsViewModel>>(okResult.Value);
        Assert.Single(returnValue);
        Assert.Equal(model.type, returnValue[0].type);
    }

    [Fact]
    public async Task InsertNotificationChannel_Success_Returns201()
    {
        var createModel = new NotificationChannelsCreateModel
        {
            type = "sms",
            config = new NotificationChannelsConfig { smtp_host = "smtp.example.com", port = 587 },
            is_active = true
        };

        var inserted = new NotificationChannelsModel
        {
            channel_id = Guid.NewGuid(),
            type = createModel.type,
            config = createModel.config,
            is_active = createModel.is_active
        };

        _mockService.Setup(s => s.InsertNotificationChannel(It.IsAny<NotificationChannelsModel>()))
            .ReturnsAsync(inserted);

        var result = await _controller.InsertNotificationChannel(createModel);

        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(201, statusResult.StatusCode);
        var viewModel = Assert.IsType<NotificationChannelsViewModel>(statusResult.Value);
        Assert.Equal(createModel.type, viewModel.type);
    }

    [Fact]
    public async Task InsertNotificationChannel_Failure_Returns500()
    {
        _mockService.Setup(s => s.InsertNotificationChannel(It.IsAny<NotificationChannelsModel>()))
            .ReturnsAsync((NotificationChannelsModel?)null);

        var createModel = new NotificationChannelsCreateModel
        {
            type = "in_app",
            config = new NotificationChannelsConfig { smtp_host = "smtp.example.com", port = 587 },
            is_active = false
        };

        var result = await _controller.InsertNotificationChannel(createModel);

        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(500, statusResult.StatusCode);
    }

    [Fact]
    public async Task UpdateNotificationChannel_Success_ReturnsOk()
    {
        var updateModel = new NotificationChannelsUpdateModel
        {
            channel_id = Guid.NewGuid(),
            config = new NotificationChannelsConfig { smtp_host = "smtp.example.com", port = 587 },
            is_active = true
        };

        var updated = new NotificationChannelsModel
        {
            channel_id = updateModel.channel_id,
            type = "email",
            config = updateModel.config,
            is_active = updateModel.is_active
        };

        _mockService.Setup(s => s.UpdateNotificationChannel(It.IsAny<NotificationChannelsModel>()))
            .ReturnsAsync(updated);

        var result = await _controller.UpdateNotificationChannel(updateModel.channel_id.Value, updateModel);

        var okResult = Assert.IsType<OkObjectResult>(result);
        var viewModel = Assert.IsType<NotificationChannelsViewModel>(okResult.Value);
        Assert.Equal(updated.channel_id, viewModel.channel_id);
    }

    [Fact]
    public async Task UpdateNotificationChannel_Failure_Returns500()
    {
        _mockService.Setup(s => s.UpdateNotificationChannel(It.IsAny<NotificationChannelsModel>()))
            .ReturnsAsync((NotificationChannelsModel?)null);

        var updateModel = new NotificationChannelsUpdateModel
        {
            channel_id = Guid.NewGuid(),
            config = new NotificationChannelsConfig { smtp_host = "smtp.example.com", port = 587 },
            is_active = false
        };

        var result = await _controller.UpdateNotificationChannel(updateModel.channel_id.Value, updateModel);

        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(500, statusResult.StatusCode);
    }

    [Fact]
    public async Task DeleteNotificationChannel_Success_ReturnsNoContent()
    {
        var channelId = Guid.NewGuid();

        _mockService.Setup(s => s.DeleteNotificationChannel(channelId))
            .ReturnsAsync(true);

        var result = await _controller.DeleteNotificationChannel(channelId);

        Assert.IsType<NoContentResult>(result);
    }

    [Fact]
    public async Task DeleteNotificationChannel_NotFound_Returns404()
    {
        var channelId = Guid.NewGuid();

        _mockService.Setup(s => s.DeleteNotificationChannel(channelId))
            .ReturnsAsync(false);

        var result = await _controller.DeleteNotificationChannel(channelId);

        Assert.IsType<NotFoundResult>(result);
    }
}
