using AutoMapper;
using Moq;
using Paas.Controllers;
using Paas.Model;
using Paas.Service;
using Xunit;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Paas.Tests.UnitTests
{
    public class TenantsControllerTests
    {
        private readonly Mock<ITenantsService> _mockService;
        private readonly IMapper _mapper;
        private readonly TenantsController _controller;

        public TenantsControllerTests()
        {
            // Mock ITenantsService
            _mockService = new Mock<ITenantsService>();

            // Initialize AutoMapper
            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new PaasProfile());  // Add your profile here
            });
            _mapper = mappingConfig.CreateMapper();

            // Initialize the controller
            _controller = new TenantsController(_mockService.Object, _mapper);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WithListOfTenantsViewModel()
        {
            // Arrange
            var mockTenants = new List<TenantsModel>
            {
                new TenantsModel { 
                    tenant_id = Guid.NewGuid(), 
                    name = "Tenant 1", 
                    contact_email = "<EMAIL>",
                    legal_address = new AddressModel { 
                        street = "Street 1", 
                        city = "City 1", 
                        country = "Country 1", 
                        postalCode = "12345" 
                    }
                },
                new TenantsModel { 
                    tenant_id = Guid.NewGuid(), 
                    name = "Tenant 2", 
                    contact_email = "<EMAIL>",
                    legal_address = new AddressModel { 
                        street = "Street 2", 
                        city = "City 2", 
                        country = "Country 2", 
                        postalCode = "54321" 
                    }
                }
            };

            _mockService.Setup(service => service.GetTenantsDataList(It.IsAny<TenantsModel>()))
                        .ReturnsAsync(mockTenants);

            // Act
            var result = await _controller.Get(null, new BaseQueryModel());

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<List<TenantsViewModel>>(okResult.Value);
            Assert.Equal(mockTenants.Count, returnValue.Count);
        }

        [Fact]
        public async Task GetDataById_ReturnsNotFound_WhenTenantDoesNotExist()
        {
            // Arrange
            var tenantId = Guid.NewGuid();
            _mockService.Setup(service => service.GetTenantsData(tenantId)).ReturnsAsync((TenantsModel)null);

            // Act
            var result = await _controller.GetDataById(tenantId);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task Create_ReturnsCreatedResult_WithTenantViewModel()
        {
            // Arrange
            var newTenant = new TenantsCreateModel
            {
                contact_email = "<EMAIL>",
                name = "New Tenant",
                billing_currency = Guid.NewGuid(),
                legal_address = new AddressModel { street = "Street", city = "City", country = "Country", postalCode = "12345" }
            };

            var exampleTenant = new TenantsModel
            {
                tenant_id = Guid.NewGuid(),
                name = "Example",
                contact_email = "<EMAIL>",
                legal_address = new AddressModel { 
                    street = "Example Street", 
                    city = "Example City", 
                    country = "Example Country", 
                    postalCode = "11111" 
                }
            };

            _mockService.Setup(service => service.CreateTenantsData(It.IsAny<TenantsModel>())).ReturnsAsync(exampleTenant!);

            // Act
            var result = await _controller.Create(newTenant);

            // Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(201, createdAtActionResult.StatusCode);
        }

        [Fact]
        public async Task Update_ReturnsOkResult_WhenTenantUpdated()
        {
            // Arrange
            var tenantId = Guid.NewGuid();
            var updateModel = new TenantsUpdateModel
            {
                contact_email = "<EMAIL>",
                name = "Updated Tenant",
                billing_currency = Guid.NewGuid(),
                legal_address = new AddressModel { street = "Updated Street", city = "Updated City", country = "Updated Country", postalCode = "54321" },
                is_active = true
            };

            var updatedTenant = new TenantsModel
            {
                tenant_id = tenantId,
                name = "Updated Test Tenant",
                contact_email = "<EMAIL>",
                legal_address = new AddressModel { 
                    street = "Updated Street", 
                    city = "Updated City", 
                    country = "Updated Country", 
                    postalCode = "67890" 
                }
            };

            _mockService.Setup(service => service.UpdateTenantsData(It.IsAny<TenantsModel>())).ReturnsAsync(updatedTenant);

            // Act
            var result = await _controller.Update(tenantId, updateModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<TenantsViewModel>(okResult.Value);
            Assert.Equal(updateModel.contact_email, returnValue.contact_email);
        }

        [Fact]
        public async Task Delete_ReturnsNoContent_WhenTenantDeletedSuccessfully()
        {
            // Arrange
            var tenantId = Guid.NewGuid();
            _mockService.Setup(service => service.UpdateTenantsStatus(tenantId)).ReturnsAsync(true);  // 假設刪除成功，返回 true

            // Act
            var result = await _controller.Delete(tenantId);

            // Assert
            Assert.IsType<NoContentResult>(result);  // 預期是 204 No Content
        }

        [Fact]
        public async Task Delete_ReturnsNotFound_WhenTenantDoesNotExist()
        {
            // Arrange
            var tenantId = Guid.NewGuid();
            _mockService.Setup(service => service.UpdateTenantsStatus(tenantId)).ReturnsAsync(false);  // 假設刪除失敗，返回 false

            // Act
            var result = await _controller.Delete(tenantId);

            // Assert
            Assert.IsType<NotFoundResult>(result);  // 預期是 404 Not Found
        }

    }
}
