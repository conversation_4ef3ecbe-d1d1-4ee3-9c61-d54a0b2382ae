﻿using Moq;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Paas.Controllers;
using Paas.Model;
using Paas.Service;

namespace Paas.Tests.UnitTests;

public class CurrenciesControllerTest
{
    private readonly Mock<ICurrenciesService> _mockService;
    private readonly IMapper _mapper;
    private readonly CurrenciesController _controller;

    public CurrenciesControllerTest()
    {
        _mockService = new Mock<ICurrenciesService>();
        _mapper = new MapperConfiguration(cfg => cfg.AddProfile<PaasProfile>())
            .CreateMapper();
        _controller = new CurrenciesController(_mockService.Object, _mapper);
    }

    [Fact]
    public async Task GetCurrencies_ReturnsOkResult_WithList()
    {
        // Arrange
        var data = new List<CurrenciesModel>
        {
            new CurrenciesModel
            {
                currency_id = Guid.NewGuid(),
                code = "USD",
                symbol = "$",
                exchange_rate = 1.0m,
                is_active = true
            }
        };

        _mockService.Setup(s => s.GetCurrencies()).ReturnsAsync(data);

        // Act
        var result = await _controller.GetCurrencies();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnData = Assert.IsAssignableFrom<IList<CurrenciesViewModel>>(okResult.Value);
        Assert.Single(returnData);

        var vm = returnData[0];
        Assert.Equal("USD", vm.code);
        Assert.Equal("$", vm.symbol);
        Assert.Equal(1.0m, vm.exchange_rate);
        Assert.True(vm.is_active);
    }

    [Fact]
    public async Task GetCurrency_ExistingId_ReturnsOk()
    {
        var id = Guid.NewGuid();
        var model = new CurrenciesModel
        {
            currency_id = id,
            code = "JPY",
            symbol = "¥",
            exchange_rate = 110.25m,
            is_active = true
        };

        _mockService.Setup(s => s.GetCurrency(id)).ReturnsAsync(model);

        var result = await _controller.GetCurrency(id);

        var okResult = Assert.IsType<OkObjectResult>(result);
        var vm = Assert.IsType<CurrenciesViewModel>(okResult.Value);
        Assert.Equal("JPY", vm.code);
        Assert.Equal("¥", vm.symbol);
        Assert.Equal(110.25m, vm.exchange_rate);
        Assert.True(vm.is_active);
    }

    [Fact]
    public async Task GetCurrency_NotFound_ReturnsNotFound()
    {
        var id = Guid.NewGuid();

        _mockService.Setup(s => s.GetCurrency(id)).ReturnsAsync((CurrenciesModel?)null);

        var result = await _controller.GetCurrency(id);

        Assert.IsType<NotFoundResult>(result);
    }

    [Fact]
    public async Task InsertCurrency_ValidModel_ReturnsCreated()
    {
        var createModel = new CurrenciesCreateModel
        {
            code = "EUR",
            symbol = "€",
            exchange_rate = 0.85m,
            is_active = true
        };

        var currencyModel = new CurrenciesModel
        {
            currency_id = Guid.NewGuid(),
            code = "EUR",
            symbol = "€",
            exchange_rate = 0.85m,
            is_active = true
        };

        _mockService.Setup(s => s.InsertCurrency(It.IsAny<CurrenciesModel>())).ReturnsAsync(currencyModel);

        var result = await _controller.InsertCurrency(createModel);

        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(201, statusResult.StatusCode);
        var vm = Assert.IsType<CurrenciesViewModel>(statusResult.Value);
        Assert.Equal("EUR", vm.code);
        Assert.Equal("€", vm.symbol);
        Assert.Equal(0.85m, vm.exchange_rate);
        Assert.True(vm.is_active);
    }

    [Fact]
    public async Task InsertCurrency_Failed_Returns500()
    {
        var createModel = new CurrenciesCreateModel
        {
            code = "TWD",
            symbol = "NT$",
            exchange_rate = 30.0m,
            is_active = true
        };

        _mockService.Setup(s => s.InsertCurrency(It.IsAny<CurrenciesModel>())).ReturnsAsync((CurrenciesModel?)null);

        var result = await _controller.InsertCurrency(createModel);

        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(500, statusResult.StatusCode);
    }

    [Fact]
    public async Task UpdateCurrency_ValidId_ReturnsOk()
    {
        var id = Guid.NewGuid();
        var updateModel = new CurrenciesUpdateModel
        {
            code = "GBP",
            symbol = "£",
            exchange_rate = 0.75m,
            is_active = false
        };

        var updatedModel = new CurrenciesModel
        {
            currency_id = id,
            code = "GBP",
            symbol = "£",
            exchange_rate = 0.75m,
            is_active = false
        };

        _mockService.Setup(s => s.UpdateCurrency(It.IsAny<CurrenciesModel>())).ReturnsAsync(updatedModel);

        var result = await _controller.UpdateCurrency(id, updateModel);

        var okResult = Assert.IsType<OkObjectResult>(result);
        var vm = Assert.IsType<CurrenciesViewModel>(okResult.Value);
        Assert.Equal("GBP", vm.code);
        Assert.Equal("£", vm.symbol);
        Assert.Equal(0.75m, vm.exchange_rate);
        Assert.False(vm.is_active);
    }

    [Fact]
    public async Task UpdateCurrency_NotFound_Returns500()
    {
        var id = Guid.NewGuid();
        var updateModel = new CurrenciesUpdateModel
        {
            code = "AUD",
            symbol = "A$",
            exchange_rate = 1.4m,
            is_active = true
        };

        _mockService.Setup(s => s.UpdateCurrency(It.IsAny<CurrenciesModel>())).ReturnsAsync((CurrenciesModel?)null);

        var result = await _controller.UpdateCurrency(id, updateModel);

        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(500, statusResult.StatusCode);
    }

    [Fact]
    public async Task DeleteCurrency_Deleted_ReturnsNoContent()
    {
        var id = Guid.NewGuid();

        _mockService.Setup(s => s.DeleteCurrency(id)).ReturnsAsync(true);

        var result = await _controller.DeleteCurrency(id);

        Assert.IsType<NoContentResult>(result);
    }

    [Fact]
    public async Task DeleteCurrency_NotFound_ReturnsNotFound()
    {
        var id = Guid.NewGuid();

        _mockService.Setup(s => s.DeleteCurrency(id)).ReturnsAsync(false);

        var result = await _controller.DeleteCurrency(id);

        Assert.IsType<NotFoundResult>(result);
    }
}
