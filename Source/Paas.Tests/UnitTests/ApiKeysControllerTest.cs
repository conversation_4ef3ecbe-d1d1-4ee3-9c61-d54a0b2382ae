﻿using AutoMapper;
using Moq;
using Paas.Controllers;
using Paas.Model;
using Paas.Service;
using Xunit;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Paas.Tests.UnitTests;

public class ApiKeysControllerTest
{
    private readonly Mock<IApiKeysService> _mockService;
    private readonly IMapper _mapper;
    private readonly ApiKeysController _controller;

    public ApiKeysControllerTest()
    {
        _mockService = new Mock<IApiKeysService>();
        _mapper = new MapperConfiguration(cfg => cfg.AddProfile<PaasProfile>()).CreateMapper();
        _controller = new ApiKeysController(_mockService.Object, _mapper);
    }

    [Fact]
    public async Task GetApiKeys_ReturnsMappedList()
    {
        var model = new ApiKeysModel { key_id = Guid.NewGuid(), api_key = "key123", status = "active", tenant_id = Guid.NewGuid(), generated_at_dt = DateTimeOffset.UtcNow, scopes = new Scopes() };
        var query = new ApiKeysQueryModel { tenant_id = model.tenant_id, status = model.status };

        _mockService.Setup(s => s.GetApiKeys(It.IsAny<ApiKeysModel>())).ReturnsAsync(new List<ApiKeysModel> { model });

        var result = await _controller.GetApiKeys(query);

        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnValue = Assert.IsAssignableFrom<IList<ApiKeysViewModel>>(okResult.Value);
        Assert.Single(returnValue);
        Assert.Equal(model.api_key, returnValue[0].api_key);
    }

    [Fact]
    public async Task GetApiKey_Existing_ReturnsOk()
    {
        var model = new ApiKeysModel { key_id = Guid.NewGuid(), api_key = "testKey", status = "active", tenant_id = Guid.NewGuid(), generated_at_dt = DateTimeOffset.UtcNow, scopes = new Scopes() };
        _mockService.Setup(s => s.GetApiKey(model.key_id)).ReturnsAsync(model);

        var result = await _controller.GetApiKey(model.key_id);
        var okResult = Assert.IsType<OkObjectResult>(result);
        var viewModel = Assert.IsType<ApiKeysViewModel>(okResult.Value);
        Assert.Equal(model.key_id, viewModel.key_id);
    }

    [Fact]
    public async Task GetApiKey_NotFound_Returns404()
    {
        _mockService.Setup(s => s.GetApiKey(It.IsAny<Guid>())).ReturnsAsync((ApiKeysModel?)null);

        var result = await _controller.GetApiKey(Guid.NewGuid());
        Assert.IsType<NotFoundResult>(result);
    }

    [Fact]
    public async Task InsertApiKey_Success_Returns201()
    {
        var createModel = new ApiKeysCreateModel
        {
            api_key = "newKey",
            tenant_id = Guid.NewGuid(),
            expiration_at = null,
            scopes = new Scopes()
        };

        var inserted = _mapper.Map<ApiKeysModel>(createModel);
        inserted.key_id = Guid.NewGuid();
        inserted.generated_at_dt = DateTimeOffset.UtcNow;

        _mockService.Setup(s => s.InsertApiKey(It.IsAny<ApiKeysModel>())).ReturnsAsync(inserted);

        var result = await _controller.InsertApiKey(createModel);
        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(201, statusResult.StatusCode);
    }

    [Fact]
    public async Task InsertApiKey_Failure_Returns500()
    {
        _mockService.Setup(s => s.InsertApiKey(It.IsAny<ApiKeysModel>())).ReturnsAsync((ApiKeysModel?)null);

        var model = new ApiKeysCreateModel
        {
            api_key = "failKey",
            tenant_id = Guid.NewGuid(),
            scopes = new Scopes(),
            expiration_at = null
        };

        var result = await _controller.InsertApiKey(model);
        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(500, statusResult.StatusCode);
    }

    [Fact]
    public async Task UpdateApiKey_Success_ReturnsOk()
    {
        var updateModel = new ApiKeysUpdateModel
        {
            key_id = Guid.NewGuid(),
            status = "active",
            scopes = new Scopes(),
            expiration_at = null
        };

        var updated = _mapper.Map<ApiKeysModel>(updateModel);
        updated.api_key = "updatedKey";
        updated.generated_at_dt = DateTimeOffset.UtcNow;

        _mockService.Setup(s => s.UpdateApiKey(It.IsAny<ApiKeysModel>())).ReturnsAsync(updated);

        var result = await _controller.UpdateApiKey(updateModel.key_id, updateModel);
        var okResult = Assert.IsType<OkObjectResult>(result);
        var viewModel = Assert.IsType<ApiKeysViewModel>(okResult.Value);
        Assert.Equal(updated.api_key, viewModel.api_key);
    }

    [Fact]
    public async Task UpdateApiKey_NotFound_Returns500()
    {
        _mockService.Setup(s => s.UpdateApiKey(It.IsAny<ApiKeysModel>())).ReturnsAsync((ApiKeysModel?)null);

        var model = new ApiKeysUpdateModel
        {
            key_id = Guid.NewGuid(),
            status = "revoked",
            expiration_at = null,
            scopes = new Scopes()
        };

        var result = await _controller.UpdateApiKey(model.key_id, model);
        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(500, statusResult.StatusCode);
    }

    [Fact]
    public async Task DeleteApiKey_Success_ReturnsNoContent()
    {
        _mockService.Setup(s => s.DeleteApiKey(It.IsAny<Guid>())).ReturnsAsync(true);

        var result = await _controller.DeleteApiKey(Guid.NewGuid());
        Assert.IsType<NoContentResult>(result);
    }

    [Fact]
    public async Task DeleteApiKey_NotFound_Returns404()
    {
        _mockService.Setup(s => s.DeleteApiKey(It.IsAny<Guid>())).ReturnsAsync(false);

        var result = await _controller.DeleteApiKey(Guid.NewGuid());
        Assert.IsType<NotFoundResult>(result);
    }
}

