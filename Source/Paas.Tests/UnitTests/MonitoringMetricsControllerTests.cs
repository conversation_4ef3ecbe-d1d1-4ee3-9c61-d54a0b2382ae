﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Paas.Controllers;
using Paas.Model;
using Paas.Service;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Paas.Tests.UnitTests
{
    public class MonitoringMetricsControllerTests
    {
        private readonly Mock<IMonitoringMetricsService> _mockService;
        private readonly IMapper _mapper;
        private readonly MonitoringMetricsController _controller;

        public MonitoringMetricsControllerTests()
        {
            _mockService = new Mock<IMonitoringMetricsService>();

            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<PaasProfile>();
            });
            _mapper = config.CreateMapper();

            _controller = new MonitoringMetricsController(_mockService.Object, _mapper);
        }

        [Fact]
        public async Task GetDataById_ReturnsOk_WhenDataExists()
        {
            var metricId = Guid.NewGuid();
            var mockModel = new MonitoringMetricsModel
            {
                data_id = metricId,
                metric_type_id = Guid.NewGuid(),
                tenant_id = Guid.NewGuid(),
                api_id = Guid.NewGuid(),
                value = 123.45m,
                tags = "[{\"key\":\"env\",\"value\":\"prod\"}]",
                start_time = DateTimeOffset.UtcNow.AddMinutes(-5),
                end_time = DateTimeOffset.UtcNow,
                timestamp = DateTimeOffset.UtcNow
            };

            _mockService.Setup(s => s.GetMetricsDataById(metricId))
                .ReturnsAsync(mockModel);

            var result = await _controller.GetDataById(metricId);

            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<MonitoringMetricsViewModel>(okResult.Value);

            Assert.Equal(mockModel.data_id, returnValue.data_id);
            Assert.Equal(mockModel.value, returnValue.value);
            Assert.NotNull(returnValue.tags);
            Assert.Single(returnValue.tags);
            Assert.Equal("prod", returnValue.tags[0]["value"]);

            _mockService.Verify(s => s.GetMetricsDataById(metricId), Times.Once);
        }

        [Fact]
        public async Task GetDataById_ReturnsNotFound_WhenDataIsNull()
        {
            var metricId = Guid.NewGuid();

            _mockService.Setup(s => s.GetMetricsDataById(metricId))
                .ReturnsAsync((MonitoringMetricsModel?)null);

            var result = await _controller.GetDataById(metricId);

            Assert.IsType<NotFoundResult>(result);
            _mockService.Verify(s => s.GetMetricsDataById(metricId), Times.Once);
        }

        [Fact]
        public async Task Get_ReturnsFilteredData_WhenQueryParametersAreProvided()
        {
            // Arrange
            var tenantId = Guid.NewGuid();
            var metricTypeId = Guid.NewGuid();
            var apiId = Guid.NewGuid();
            var startTime = DateTime.UtcNow.AddDays(-1);
            var endTime = DateTime.UtcNow;

            var baseQueryModel = new BaseQueryModel
            {
                Offset = 0,
                Limit = 5
            };

            var dataList = new List<MonitoringMetricsModel>
            {
                new MonitoringMetricsModel
                {
                    data_id = Guid.NewGuid(),
                    tenant_id = tenantId,
                    api_id = apiId,
                    metric_type_id = metricTypeId,
                    start_time = startTime,
                    end_time = endTime,
                    value = 100,
                    tags = "[{\"key\":\"region\",\"value\":\"us-east\"}]",
                    timestamp = DateTime.UtcNow
                }
            };

            _mockService.Setup(s => s.GetMetricsDataList(It.IsAny<MonitoringMetricsModel>()))
                .ReturnsAsync(dataList);

            // Act
            var result = await _controller.Get(tenantId, metricTypeId, apiId, startTime, endTime, baseQueryModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<List<MonitoringMetricsViewModel>>(okResult.Value);

            Assert.Single(returnValue);
            Assert.Equal(tenantId, returnValue[0].tenant_id);
            Assert.NotNull(returnValue[0].tags);

            _mockService.Verify(s => s.GetMetricsDataList(It.IsAny<MonitoringMetricsModel>()), Times.Once);
        }
    }
}
