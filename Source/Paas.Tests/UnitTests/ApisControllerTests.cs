﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Paas.Controllers;
using Paas.Model;
using Paas.Service;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Paas.Tests.UnitTests
{
    public class ApisControllerTests
    {
        private readonly Mock<IApisService> _mockService;
        private readonly IMapper _mapper;
        private readonly ApisController _controller;

        public ApisControllerTests()
        {
            _mockService = new Mock<IApisService>();

            var configuration = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<PaasProfile>(); // 加入你的自定義 AutoMapper 配置
            });
            _mapper = configuration.CreateMapper();

            _controller = new ApisController(_mockService.Object, _mapper);
        }

        [Fact]
        public async Task Get_ShouldReturnOkResult_WhenApisDataIsFound()
        {
            // Arrange
            var tenantId = Guid.NewGuid();
            var model = new BaseQueryModel();
            var apisModelList = new List<ApisModel>
        {
            new ApisModel
            {
                api_id = Guid.NewGuid(),
                tenant_id = tenantId,
                name = "Test API",
                description = "Test API Description",
                version = "1.0.0",
                rate_limit_per_minute = 100,
                is_active = true,
                created_at_dt = DateTimeOffset.UtcNow,
                updated_at_dt = DateTimeOffset.UtcNow
            }
        };

            _mockService.Setup(service => service.GetApisDataList(It.IsAny<ApisModel>()))
                .ReturnsAsync(apisModelList);

            // Act
            var result = await _controller.Get(tenantId, model);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<List<ApisViewModel>>(okResult.Value);
            Assert.Single(returnValue); // 確保回傳了正確的資料數量
        }

        [Fact]
        public async Task GetById_ShouldReturnNotFound_WhenApiDoesNotExist()
        {
            // Arrange
            var apiId = Guid.NewGuid();
            _mockService.Setup(service => service.GetApisData(It.IsAny<Guid>()))
                .ReturnsAsync((ApisModel?)null);

            // Act
            var result = await _controller.GetById(apiId);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task Create_ShouldReturnCreatedAtActionResult_WhenApiIsCreatedSuccessfully()
        {
            // Arrange
            var model = new ApisCreateModel
            {
                tenant_id = Guid.NewGuid(),
                name = "New API",
                description = "New API Description",
                version = "1.0.0",
                rate_limit_per_minute = 100
            };

            var createdApi = new ApisModel
            {
                api_id = Guid.NewGuid(),
                tenant_id = model.tenant_id,
                name = model.name,
                description = model.description,
                version = model.version,
                rate_limit_per_minute = model.rate_limit_per_minute,
                is_active = true,
                created_at_dt = DateTimeOffset.UtcNow,
                updated_at_dt = DateTimeOffset.UtcNow
            };

            _mockService.Setup(service => service.CreateApisData(It.IsAny<ApisModel>()))
                .ReturnsAsync(createdApi);

            // Act
            var result = await _controller.Create(model);

            // Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            var returnValue = Assert.IsType<ApisViewModel>(createdAtActionResult.Value);
            Assert.Equal(model.name, returnValue.name); // 檢查返回的資料是否正確
        }

        [Fact]
        public async Task Update_ShouldReturnOkResult_WhenApiIsUpdatedSuccessfully()
        {
            // Arrange
            var apiId = Guid.NewGuid();
            var model = new ApisUpdateModel
            {
                name = "Updated API",
                version = "1.1.0",
                rate_limit_per_minute = 200,
                is_active = true
            };

            var updatedApi = new ApisModel
            {
                api_id = apiId,
                tenant_id = Guid.NewGuid(),
                name = model.name,
                description = "Updated API Description",
                version = model.version,
                rate_limit_per_minute = model.rate_limit_per_minute,
                is_active = model.is_active,
                created_at_dt = DateTimeOffset.UtcNow,
                updated_at_dt = DateTimeOffset.UtcNow
            };

            _mockService.Setup(service => service.UpdateApisData(It.IsAny<ApisModel>()))
                .ReturnsAsync(updatedApi);

            // Act
            var result = await _controller.Update(apiId, model);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<ApisViewModel>(okResult.Value);
            Assert.Equal(model.name, returnValue.name); // 檢查返回的資料是否正確
        }

        [Fact]
        public async Task Delete_ShouldReturnOkResult_WhenApiIsDeletedSuccessfully()
        {
            // Arrange
            var apiId = Guid.NewGuid();
            _mockService.Setup(service => service.UpdateApisStatus(It.IsAny<Guid>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.Delete(apiId);

            // Assert
            Assert.IsType<OkResult>(result);
        }

        [Fact]
        public async Task Delete_ShouldReturnNotFound_WhenApiDoesNotExist()
        {
            // Arrange
            var apiId = Guid.NewGuid();
            _mockService.Setup(service => service.UpdateApisStatus(It.IsAny<Guid>()))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.Delete(apiId);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }
    }
}

