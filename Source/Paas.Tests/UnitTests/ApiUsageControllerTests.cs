using AutoMapper;
using Moq;
using Paas.Controllers;
using Paas.Model;
using Paas.Service;
using Xunit;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Paas.Tests.UnitTests
{
    public class ApiUsageControllerTests
    {
        private readonly Mock<IApiUsageService> _mockService;
        private readonly IMapper _mapper;
        private readonly ApiUsageController _controller;

        public ApiUsageControllerTests()
        {
            // Mock IApiUsageService
            _mockService = new Mock<IApiUsageService>();

            // Initialize AutoMapper
            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new PaasProfile());  // Add your profile here
            });
            _mapper = mappingConfig.CreateMapper();

            // Initialize the controller
            _controller = new ApiUsageController(_mockService.Object, _mapper);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WithListOfApiUsageViewModel()
        {
            // Arrange
            var mockApiUsages = new List<ApiUsageModel>
            {
                new ApiUsageModel
                {
                    usage_id = Guid.NewGuid(),
                    api_id = Guid.NewGuid(),
                    key_id = Guid.NewGuid(),
                    request_ip = "***********",
                    response_time = 120.5m,
                    status_code = 200
                },
                new ApiUsageModel
                {
                    usage_id = Guid.NewGuid(),
                    api_id = Guid.NewGuid(),
                    key_id = Guid.NewGuid(),
                    request_ip = "***********",
                    response_time = 130.7m,
                    status_code = 500
                }
            };

            _mockService.Setup(service => service.GetApiUsageDataList(It.IsAny<ApiUsageModel>()))
                        .ReturnsAsync(mockApiUsages);

            // Act
            var result = await _controller.Get(null, null, null, new BaseQueryModel());

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<List<ApiUsageViewModel>>(okResult.Value);
            Assert.Equal(mockApiUsages.Count, returnValue.Count);
        }

        [Fact]
        public async Task GetDataById_ReturnsOkResult_WithApiUsageViewModel()
        {
            // Arrange
            var usageId = Guid.NewGuid();
            var mockApiUsage = new ApiUsageModel
            {
                usage_id = usageId,
                api_id = Guid.NewGuid(),
                key_id = Guid.NewGuid(),
                request_ip = "***********",
                response_time = 120.5m,
                status_code = 200
            };

            _mockService.Setup(service => service.GetApiUsageData(usageId))
                        .ReturnsAsync(mockApiUsage);

            // Act
            var result = await _controller.GetDataById(usageId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<ApiUsageViewModel>(okResult.Value);
            Assert.Equal(mockApiUsage.usage_id, returnValue.usage_id);
        }

        [Fact]
        public async Task Create_ReturnsCreatedAtActionResult_WithApiUsageViewModel()
        {
            // Arrange
            var newApiUsage = new ApiUsageCreateModel
            {
                api_id = Guid.NewGuid(),
                key_id = Guid.NewGuid(),
                request_ip = "***********",
                response_time = 100.0m,
                status_code = 200
            };

            var createdApiUsage = new ApiUsageModel
            {
                usage_id = Guid.NewGuid(),
                api_id = newApiUsage.api_id,
                key_id = newApiUsage.key_id,
                request_ip = newApiUsage.request_ip,
                response_time = newApiUsage.response_time,
                status_code = newApiUsage.status_code
            };

            _mockService.Setup(service => service.CreateApiUsageData(It.IsAny<ApiUsageModel>()))
                        .ReturnsAsync(createdApiUsage);

            // Act
            var result = await _controller.Create(newApiUsage);

            // Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            var returnValue = Assert.IsType<ApiUsageViewModel>(createdAtActionResult.Value);
            Assert.Equal(createdApiUsage.usage_id, returnValue.usage_id);
        }
    }
}
