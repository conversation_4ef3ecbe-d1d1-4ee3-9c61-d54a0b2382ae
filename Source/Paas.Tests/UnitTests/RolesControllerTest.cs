﻿using Moq;
using AutoMapper;
using Paas.Controllers;
using Paas.Service;
using Paas.Model;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Paas.Tests.UnitTests
{
    public class UsersControllerTest
    {
        private readonly UsersController _controller;
        private readonly Mock<IUsersService> _mockService;
        private readonly IMapper _mapper;

        public UsersControllerTest()
        {
            _mapper = new MapperConfiguration(cfg => cfg.AddProfile<PaasProfile>()).CreateMapper();
            _mockService = new Mock<IUsersService>();
            _controller = new UsersController(_mockService.Object, _mapper);
        }

        [Fact]
        public async Task Get_Users_ReturnsOkResult_WhenUsersExist()
        {
            // Arrange
            var users = new List<UsersModel>
            {
                new UsersModel { user_id = Guid.NewGuid(), username = "testuser1", email = "<EMAIL>" },
                new UsersModel { user_id = Guid.NewGuid(), username = "testuser2", email = "<EMAIL>" }
            };
            _mockService.Setup(service => service.GetUsersList(It.IsAny<UsersModel>())).ReturnsAsync(users);

            // Act
            var result = await _controller.Get(null, null, new BaseQueryModel());

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<List<UsersViewModel>>(okResult.Value);
            Assert.Equal(2, returnValue.Count);
        }

        [Fact]
        public async Task Get_Users_ReturnsNotFound_WhenNoUsersExist()
        {
            // Arrange
            _mockService.Setup(service => service.GetUsersList(It.IsAny<UsersModel>())).ReturnsAsync(new List<UsersModel>());

            // Act
            var result = await _controller.Get(null, null, new BaseQueryModel());

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task Get_User_ReturnsOkResult_WhenUserExists()
        {
            // Arrange
            var user = new UsersModel { user_id = Guid.NewGuid(), username = "testuser1", email = "<EMAIL>" };
            _mockService.Setup(service => service.GetUsersData(It.IsAny<Guid>())).ReturnsAsync(user);

            // Act
            var result = await _controller.GetDataById(user.user_id);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<UsersViewModel>(okResult.Value);
            Assert.Equal(user.username, returnValue.username);
        }

        [Fact]
        public async Task Get_User_ReturnsNotFound_WhenUserDoesNotExist()
        {
            // Arrange
            _mockService.Setup(service => service.GetUsersData(It.IsAny<Guid>())).ReturnsAsync((UsersModel)null);

            // Act
            var result = await _controller.GetDataById(Guid.NewGuid());

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task Create_User_ReturnsCreatedAtActionResult_WhenCreatedSuccessfully()
        {
            // Arrange
            var userCreateModel = new UsersCreateModel
            {
                tenant_id = Guid.NewGuid(),
                username = "testuser",
                email = "<EMAIL>",
                password_hash = "hashedpassword",
                role_id = Guid.NewGuid()
            };
            var user = new UsersModel
            {
                user_id = Guid.NewGuid(),
                tenant_id = userCreateModel.tenant_id,
                username = userCreateModel.username,
                email = userCreateModel.email,
                password_hash = userCreateModel.password_hash,
                role_id = userCreateModel.role_id
            };
            _mockService.Setup(service => service.CreateUsers(It.IsAny<UsersModel>())).ReturnsAsync(user);

            // Act
            var result = await _controller.Create(userCreateModel);

            // Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            var returnValue = Assert.IsType<UsersViewModel>(createdAtActionResult.Value);
            Assert.Equal(user.username, returnValue.username);
        }

        [Fact]
        public async Task Update_User_ReturnsOkResult_WhenUpdatedSuccessfully()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userUpdateModel = new UsersUpdateModel
            {
                email = "<EMAIL>",
                role_id = Guid.NewGuid()
            };
            var updatedUser = new UsersModel
            {
                user_id = userId,
                email = userUpdateModel.email,
                role_id = userUpdateModel.role_id
            };
            _mockService.Setup(service => service.UpdateUsers(It.IsAny<UsersModel>())).ReturnsAsync(updatedUser);

            // Act
            var result = await _controller.Update(userId, userUpdateModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<UsersViewModel>(okResult.Value);
            Assert.Equal(updatedUser.email, returnValue.email);
        }

        [Fact]
        public async Task Delete_User_ReturnsNoContent_WhenDeletedSuccessfully()
        {
            // Arrange
            var userId = Guid.NewGuid();
            _mockService.Setup(service => service.DeleteUsers(It.IsAny<Guid>())).ReturnsAsync(true);

            // Act
            var result = await _controller.Delete(userId);

            // Assert
            Assert.IsType<NoContentResult>(result);
        }

        [Fact]
        public async Task Delete_User_ReturnsNotFound_WhenUserDoesNotExist()
        {
            // Arrange
            var userId = Guid.NewGuid();
            _mockService.Setup(service => service.DeleteUsers(It.IsAny<Guid>())).ReturnsAsync(false);

            // Act
            var result = await _controller.Delete(userId);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }
    }
}
