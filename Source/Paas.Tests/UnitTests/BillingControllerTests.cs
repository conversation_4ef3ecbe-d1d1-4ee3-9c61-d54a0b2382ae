﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Paas.Controllers;
using Paas.Model;
using Paas.Service;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Paas.Tests.UnitTests
{
    public class BillingControllerTests
    {
        private readonly Mock<IBillingService> _mockService;
        private readonly IMapper _mapper;
        private readonly BillingController _controller;

        public BillingControllerTests()
        {
            _mockService = new Mock<IBillingService>();

            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new PaasProfile());
            });
            _mapper = mappingConfig.CreateMapper();

            _controller = new BillingController(_mockService.Object, _mapper);
        }

        [Fact]
        public async Task Get_ReturnsOkResult_WithListOfBillingViewModel()
        {
            // Arrange
            var mockData = new List<BillingModel>
            {
                new BillingModel { record_id = Guid.NewGuid(), tenant_id = Guid.NewGuid(), amount = 100, currency_id = Guid.NewGuid(), payment_method_id = Guid.NewGuid(), status = "Pending", due_date = DateTimeOffset.Now, paid_at = DateTimeOffset.Now },
                new BillingModel { record_id = Guid.NewGuid(), tenant_id = Guid.NewGuid(), amount = 200, currency_id = Guid.NewGuid(), payment_method_id = Guid.NewGuid(), status = "Paid", due_date = DateTimeOffset.Now, paid_at = DateTimeOffset.Now }
            };

            _mockService.Setup(s => s.GetBillingDataList(It.IsAny<BillingModel>()))
                        .ReturnsAsync(mockData);

            // Act
            var result = await _controller.Get(null, null, new BaseQueryModel());

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<List<BillingViewModel>>(okResult.Value);
            Assert.Equal(mockData.Count, returnValue.Count);
        }

        [Fact]
        public async Task GetById_ReturnsOkResult_WithBillingViewModel()
        {
            // Arrange
            var billingId = Guid.NewGuid();
            var billing = new BillingModel
            {
                record_id = billingId,
                tenant_id = Guid.NewGuid(),
                amount = 500,
                currency_id = Guid.NewGuid(),
                payment_method_id = Guid.NewGuid(),
                status = "Paid",
                due_date = DateTimeOffset.Now,
                paid_at = DateTimeOffset.Now
            };

            _mockService.Setup(s => s.GetBillingDataById(billingId)).ReturnsAsync(billing);

            // Act
            var result = await _controller.GetById(billingId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<BillingViewModel>(okResult.Value);
            Assert.Equal(billing.record_id, returnValue.record_id);
        }

        [Fact]
        public async Task CreateBillingData_ReturnsCreatedAtActionResult_WithBillingViewModel()
        {
            // Arrange
            var createModel = new BillingCreateModel
            {
                tenant_id = Guid.NewGuid(),
                amount = 1000,
                currency_id = Guid.NewGuid(),
                payment_method_id = Guid.NewGuid(),
                status = "Pending",
                due_date = DateTimeOffset.Now
            };

            var billingModel = new BillingModel
            {
                record_id = Guid.NewGuid(),
                tenant_id = createModel.tenant_id,
                amount = createModel.amount,
                currency_id = createModel.currency_id,
                payment_method_id = createModel.payment_method_id,
                status = createModel.status,
                due_date = createModel.due_date,
                paid_at = DateTimeOffset.Now
            };

            _mockService.Setup(s => s.CreateBillingData(It.IsAny<BillingModel>()))
                        .ReturnsAsync(billingModel!);

            // Act
            var result = await _controller.CreateBillingData(createModel);

            // Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            var returnValue = Assert.IsType<BillingViewModel>(createdAtActionResult.Value);
            Assert.Equal(billingModel.record_id, returnValue.record_id);
        }

        [Fact]
        public async Task UpdateBillingData_ReturnsOkResult_WhenFound()
        {
            // Arrange
            var billingId = Guid.NewGuid();
            var updateModel = new BillingUpdateModel
            {
                status = "Paid",
                paid_at = DateTimeOffset.Now
            };

            var updatedModel = new BillingModel
            {
                record_id = billingId,
                status = updateModel.status,
                paid_at = updateModel.paid_at,
                amount = 123,
                tenant_id = Guid.NewGuid(),
                currency_id = Guid.NewGuid(),
                payment_method_id = Guid.NewGuid(),
                due_date = DateTimeOffset.Now
            };

            _mockService.Setup(s => s.UpdateBillingData(It.IsAny<BillingModel>()))
                        .ReturnsAsync(updatedModel);

            // Act
            var result = await _controller.UpdateBillingData(billingId, updateModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<BillingViewModel>(okResult.Value);
            Assert.Equal(updatedModel.status, returnValue.status);
        }

        [Fact]
        public async Task UpdateBillingData_Returns500_WhenNotFound()
        {
            // Arrange
            var billingId = Guid.NewGuid();
            var updateModel = new BillingUpdateModel
            {
                status = "Paid",
                paid_at = DateTimeOffset.Now
            };

            _mockService.Setup(s => s.UpdateBillingData(It.IsAny<BillingModel>()))
                        .ReturnsAsync((BillingModel?)null);

            // Act
            var result = await _controller.UpdateBillingData(billingId, updateModel);

            // Assert
            var objectResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, objectResult.StatusCode);
        }

        [Fact]
        public async Task DeleteBillingData_ReturnsNoContent_WhenDeleted()
        {
            // Arrange
            var billingId = Guid.NewGuid();

            _mockService.Setup(s => s.DeleteBillingData(billingId))
                        .ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteBillingData(billingId);

            // Assert
            Assert.IsType<NoContentResult>(result);
        }

        [Fact]
        public async Task DeleteBillingData_ReturnsNotFound_WhenNotDeleted()
        {
            // Arrange
            var billingId = Guid.NewGuid();

            _mockService.Setup(s => s.DeleteBillingData(billingId))
                        .ReturnsAsync(false);

            // Act
            var result = await _controller.DeleteBillingData(billingId);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }
    }
}
