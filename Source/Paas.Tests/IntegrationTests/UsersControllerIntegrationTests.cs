﻿using Paas.Model;
using System.Net.Http.Json;
using System.Net;

namespace Paas.Tests.IntegrationTests
{
    public class UsersControllerIntegrationTests : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public UsersControllerIntegrationTests(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task Get_UsersList_ReturnsSuccess()
        {
            var tenantId = new Guid("0195830d-deda-7e45-8485-879e7fd6b7a7");
            var roleId = new Guid("0195f05c-6164-701c-8725-705103a307a6");
            var queryModel = new BaseQueryModel { Limit = 10, Offset = 0 };

            var response = await _client.GetAsync($"/api/users?tenant_id={tenantId}&role_id={roleId}&limit={queryModel.Limit}&offset={queryModel.Offset}");

            Assert.True(response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task Get_UserById_ReturnsSuccess_WhenExists()
        {
            var existingId = new Guid("0195f57b-8b44-7ae7-8995-cf61ed08bc31"); // 替換為有效的使用者ID
            var response = await _client.GetAsync($"/api/users/{existingId}");

            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        [Fact]
        public async Task Get_UserById_ReturnsNotFound_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var response = await _client.GetAsync($"/api/users/{nonExistingId}");

            Assert.Equal(HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task Post_User_ReturnsOk_WhenValid()
        {
            var model = new UsersCreateModel
            {
                tenant_id = new Guid("0195830d-deda-7e45-8485-879e7fd6b7a7"),
                username = "testuser",
                email = "<EMAIL>",
                password_hash = "123456hashed",
                role_id = new Guid("0195f05c-6164-701c-8725-705103a307a6")
            };

            var response = await _client.PostAsJsonAsync("/api/users", model);

            Assert.Equal(HttpStatusCode.Created, response.StatusCode);
        }

        [Fact]
        public async Task Post_User_ReturnsBadRequest_WhenInvalid()
        {
            var model = new UsersCreateModel
            {
                tenant_id = Guid.Empty,
                username = "invaliduser",
                email = "<EMAIL>",
                password_hash = "123456hashed",
                role_id = Guid.NewGuid()
            };

            var response = await _client.PostAsJsonAsync("/api/users", model);

            Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        }

        [Fact]
        public async Task Put_User_ReturnsSuccess_WhenValid()
        {
            var userId = new Guid("0196184f-f8c2-7384-8e81-b3b1c83efe1b"); // 替換為有效的使用者ID
            var updateModel = new UsersUpdateModel
            {
                email = "<EMAIL>",
                role_id = new Guid("0195f05d-3cf6-753c-8358-ead52bf6198d")
            };

            var response = await _client.PutAsJsonAsync($"/api/users/{userId}", updateModel);

            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        [Fact]
        public async Task Delete_User_ReturnsNoContent_WhenExists()
        {
            var userId = new Guid("0196184f-f8c2-7384-8e81-b3b1c83efe1b"); // 替換為有效的使用者ID
            var response = await _client.DeleteAsync($"/api/users/{userId}");

            Assert.Equal(HttpStatusCode.NoContent, response.StatusCode);
        }

        [Fact]
        public async Task Delete_User_ReturnsNotFound_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var response = await _client.DeleteAsync($"/api/users/{nonExistingId}");

            Assert.Equal(HttpStatusCode.NotFound, response.StatusCode);
        }
    }
}
