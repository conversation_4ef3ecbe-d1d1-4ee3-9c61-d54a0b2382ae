using Paas.Model;
using System.Net.Http.Json;

namespace Paas.Tests.IntegrationTests
{
    public class NotificationsIntegrationTest : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public NotificationsIntegrationTest(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        // 測試獲取所有通知
        [Fact]
        public async Task Get_Notifications_ReturnsSuccessStatusCode()
        {
            var response = await _client.GetAsync("/v1/notifications");
            response.EnsureSuccessStatusCode();
        }

        // 測試根據通知 ID 獲取特定通知 (存在資料)
        [Fact]
        public async Task Get_Notification_ReturnsSuccessStatusCode_WhenDataExist()
        {
            var existingId = "01960e1b-7ef1-7797-86f0-c9e98d4b536f"; // 假設此 ID 已存在
            var response = await _client.GetAsync($"/v1/notifications/{existingId}");
            response.EnsureSuccessStatusCode();
        }

        // 測試根據通知 ID 獲取特定通知 (不存在資料)
        [Fact]
        public async Task Get_Notification_ReturnsNotFound_WhenDataNotFound()
        {
            var nonExistingId = Guid.NewGuid();
            var response = await _client.GetAsync($"/v1/notifications/{nonExistingId}");
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode);
        }

        // 測試創建新的通知
        [Fact]
        public async Task Post_Notification_ReturnsCreatedStatusCode_WhenDataIsValid()
        {
            var requestModel = new NotificationsCreateModel
            {
                type_id = new Guid("0195a81f-2b76-7a8d-81dd-42194bc2f269"),
                tenant_id = new Guid("0195a81f-2b76-7a8d-81dd-42194bc2f269"),
                user_id = new Guid("0195a81f-2b76-7a8d-81dd-42194bc2f269"),
                title = "New Notification",
                message = "This is a test notification.",
                priority = "high",
                expires_at = DateTimeOffset.UtcNow.AddDays(7)
            };

            var response = await _client.PostAsJsonAsync("/v1/notifications", requestModel);

            Assert.Equal(System.Net.HttpStatusCode.Created, response.StatusCode);
        }

        // 測試使用查詢參數過濾通知
        [Fact]
        public async Task Get_Notifications_ReturnsFilteredData_WhenQueryParamsAreProvided()
        {
            var query = new Dictionary<string, string>
            {
                { "tenant_id", "0195a81f-2b76-7a8d-81dd-42194bc2f269" },
                { "status", "unread" },
                { "priority", "high" }
            };

            var queryString = await new FormUrlEncodedContent(query).ReadAsStringAsync();
            var response = await _client.GetAsync($"/v1/notifications?{queryString}");

            response.EnsureSuccessStatusCode();
        }
    }
}
