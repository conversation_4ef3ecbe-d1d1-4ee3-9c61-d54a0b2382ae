﻿using Paas.Model;
using System.Net.Http.Json;

namespace Paas.Tests.IntegrationTests
{
    public class TenantsIntegrationTests : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public TenantsIntegrationTests(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task Get_Tenants_ReturnsSuccess()
        {
            var response = await _client.GetAsync("/v1/tenants");
            response.EnsureSuccessStatusCode();
        }

        [Fact]
        public async Task Get_TenantById_ReturnsSuccess_WhenExists()
        {
            var existingId = new Guid("0195830d-deda-7e45-8485-879e7fd6b7a7"); // 替換為實際存在的ID
            var response = await _client.GetAsync($"/v1/tenants/{existingId}");
            response.EnsureSuccessStatusCode();
        }

        [Fact]
        public async Task Get_TenantById_ReturnsNotFound_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var response = await _client.GetAsync($"/v1/tenants/{nonExistingId}");
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task Post_Tenant_ReturnsCreated_WhenValid()
        {
            var model = new TenantsCreateModel
            {
                contact_email = "<EMAIL>",
                name = "Test Tenant",
                billing_currency = new Guid("0195830c-f670-759e-8cd7-7bb9f569eb62"),// 替換為實際存在的ID
                legal_address = new AddressModel
                {
                    country = "US",
                    city = "New York",
                    postalCode = "10001",
                    street = "123 Test St"
                }
            };

            var response = await _client.PostAsJsonAsync("/v1/tenants", model);
            Assert.Equal(System.Net.HttpStatusCode.Created, response.StatusCode);
        }

        [Fact]
        public async Task Put_Tenant_ReturnsOk_WhenValid()
        {
            var existingId = new Guid("01958315-279b-734f-8b9b-2955cd05939d"); // 替換為實際存在的ID
            var model = new TenantsUpdateModel
            {
                contact_email = "<EMAIL>",
                name = "Updated Tenant",
                billing_currency = new Guid("0195830c-f670-759e-8cd7-7bb9f569eb62"),// 替換為實際存在的ID
                legal_address = new AddressModel
                {
                    country = "US",
                    city = "Updated City",
                    postalCode = "10002",
                    street = "456 Updated St"
                },
                is_active = true
            };

            var response = await _client.PutAsJsonAsync($"/v1/tenants/{existingId}", model);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);
        }

        [Fact]
        public async Task Put_Tenant_ReturnsInternalServerError_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var model = new TenantsUpdateModel
            {
                contact_email = "<EMAIL>",
                name = "NotFound Tenant",
                billing_currency = Guid.NewGuid(),
                legal_address = new AddressModel
                {
                    country = "US",
                    city = "Nowhere",
                    postalCode = "99999",
                    street = "000 None"
                },
                is_active = true
            };

            var response = await _client.PutAsJsonAsync($"/v1/tenants/{nonExistingId}", model);
            Assert.Equal(System.Net.HttpStatusCode.InternalServerError, response.StatusCode);
        }

        [Fact]
        public async Task Delete_Tenant_ReturnsNoContent_WhenDeleted()
        {
            var existingId = new Guid("01958315-279b-734f-8b9b-2955cd05939d"); // 替換為實際存在的ID
            var response = await _client.DeleteAsync($"/v1/tenants/{existingId}");
            Assert.Equal(System.Net.HttpStatusCode.NoContent, response.StatusCode);
        }

        [Fact]
        public async Task Delete_Tenant_ReturnsNotFound_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var response = await _client.DeleteAsync($"/v1/tenants/{nonExistingId}");
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode);
        }
    }
}
