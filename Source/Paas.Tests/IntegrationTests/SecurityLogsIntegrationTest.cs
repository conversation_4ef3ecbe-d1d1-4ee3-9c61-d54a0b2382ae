using Paas.Model;

namespace Paas.Tests.IntegrationTests
{
    public class SecurityLogsIntegrationTest : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public SecurityLogsIntegrationTest(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task Get_SecurityLogs_ReturnsSuccessStatusCode()
        {
            var response = await _client.GetAsync("/v1/security_logs");

            response.EnsureSuccessStatusCode(); // �T�O��^���\���A�X (200 OK)
        }

        [Fact]
        public async Task Get_SecurityLog_ReturnsSuccessStatusCode_WhenDataExist()
        {
            var existingLogId = "0195efc6-3933-7fbf-8c36-36dcac5ef232"; // ���]�� ID �w�g�s�b���Ʈw��
            var response = await _client.GetAsync($"/v1/security_logs/{existingLogId}");

            response.EnsureSuccessStatusCode(); // �T�{�ন�\����ӱ���x���
        }

        [Fact]
        public async Task Get_SecurityLog_ReturnsNotFound_WhenDataNotFound()
        {
            var nonExistingLogId = Guid.NewGuid(); // �ϥ��H���ͦ��� UUID�A���]�� ID ���s�b
            var response = await _client.GetAsync($"/v1/security_logs/{nonExistingLogId}");

            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode); // �T�{��^ 404 NotFound
        }

        [Fact]
        public async Task Get_SecurityLogs_ReturnsFilteredData_WhenQueryParamsAreProvided()
        {
            var query = new Dictionary<string, string>
            {
                { "tenant_id", "0195a81f-2b76-7a8d-81dd-42194bc2f269" }, // ���]���� ID
                { "event_type_id", "0195ef89-0b80-733a-81a8-807d883cd407" }, // ���]�ƥ�����
                { "start_time", "2025-01-01T00:00:00Z" },
                { "end_time", "2028-04-01T00:00:00Z" }
            };

            var queryString = await new FormUrlEncodedContent(query).ReadAsStringAsync();
            var response = await _client.GetAsync($"/v1/security_logs?{queryString}");

            response.EnsureSuccessStatusCode(); // �T�O��^���\���A�X (200 OK)
            var responseData = await response.Content.ReadAsStringAsync();
            Assert.Contains("0195ef89-0b80-733a-81a8-807d883cd407", responseData); // �T�{��^��Ƥ��]�t�S�w�ƥ�����
        }

        [Fact]
        public async Task Get_SecurityLogs_ReturnsSuccess_WhenQueryParamsAreValid()
        {
            var queryModel = new SecurityLogsQueryModel
            {
                tenant_id = Guid.NewGuid(),
                event_type_id = "0195ef89-0b80-733a-81a8-807d883cd407",
                start_time = DateTimeOffset.UtcNow.AddMonths(-1),
                end_time = DateTimeOffset.UtcNow
            };

            var queryString = await new FormUrlEncodedContent(new Dictionary<string, string>
            {
                { "tenant_id", queryModel.tenant_id.ToString() },
                { "event_type_id", queryModel.event_type_id ?? string.Empty },
                { "start_time", queryModel.start_time.ToString("o") },
                { "end_time", queryModel.end_time.ToString("o") }
            }).ReadAsStringAsync();

            var response = await _client.GetAsync($"/v1/security_logs?{queryString}");

            response.EnsureSuccessStatusCode(); // �T�O��^���\���A�X (200 OK)
        }
    }
}
