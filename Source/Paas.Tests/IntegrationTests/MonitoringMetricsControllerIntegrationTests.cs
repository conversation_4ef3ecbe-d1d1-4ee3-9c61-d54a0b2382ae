﻿using System.Net;

namespace Paas.Tests.IntegrationTests
{
    public class MonitoringMetricsControllerIntegrationTests : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public MonitoringMetricsControllerIntegrationTests(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task Get_MonitoringMetricsList_ReturnsOk_WhenDataExists()
        {
            // Arrange
            var tenantId = new Guid("0195830d-deda-7e45-8485-879e7fd6b7a7"); // 可替換成實際存在的租戶 ID
            var metricTypeId = new Guid("0195eb9b-95f6-70f5-8f92-45f58bde8e20"); // 可替換成實際存在的指標類型 ID
            var apiId = new Guid("0195847f-1a98-7349-8e08-3a7e391dfe26"); // 可替換成實際存在的 API ID
            var queryString = $"?tenant_id={tenantId}&metric_type_id={metricTypeId}&api_id={apiId}&limit=10&offset=0";

            // Act
            var response = await _client.GetAsync($"/api/monitoring_metrics{queryString}");

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        [Fact]
        public async Task Get_MonitoringMetricsList_ReturnsNotFound_WhenDataNotExists()
        {
            // Arrange
            var nonExistingTenantId = Guid.NewGuid();
            var queryString = $"?tenant_id={nonExistingTenantId}";

            // Act
            var response = await _client.GetAsync($"/api/monitoring_metrics{queryString}");

            // Assert
            Assert.Equal(HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task Get_MonitoringMetricsById_ReturnsNotFound_WhenDataNotExists()
        {
            // Arrange
            var existingMetricId = Guid.NewGuid(); // TODO: 替換成實際存在的 metric_id

            // Act
            var response = await _client.GetAsync($"/api/monitoring_metrics/{existingMetricId}");

            // Assert
            Assert.Equal(HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task Get_MonitoringMetricsList_ReturnsOk_WhenQueryIsEmpty()
        {
            // Act
            var response = await _client.GetAsync("/api/monitoring_metrics");

            // Assert
            Assert.True(response.StatusCode == HttpStatusCode.OK);
        }
    }
}
