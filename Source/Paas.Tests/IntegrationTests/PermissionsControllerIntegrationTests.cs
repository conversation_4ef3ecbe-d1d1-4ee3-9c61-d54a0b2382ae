﻿using Paas.Model;
using System.Net;
using Xunit;

namespace Paas.Tests.IntegrationTests
{
    public class PermissionsControllerIntegrationTests : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public PermissionsControllerIntegrationTests(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task Get_Permissions_ReturnsSuccess_WhenRoleIdExists()
        {
            // Arrange - 這裡請替換成實際存在的 role_id（如果資料是預先寫死的）
            var existingRoleId = new Guid("0195f05c-6164-701c-8725-705103a307a6"); // TODO: 替換成測試資料庫中存在的 role_id

            // Act
            var response = await _client.GetAsync($"/api/permissions?role_id={existingRoleId}");

            // Assert
            Assert.True(response.StatusCode == HttpStatusCode.OK);
        }

        [Fact]
        public async Task Get_Permissions_ReturnsNotFound_WhenRoleIdNotExists()
        {
            // Arrange - 這裡請替換成實際存在的 role_id（如果資料是預先寫死的）
            var notExistingRoleId = Guid.NewGuid();

            // Act
            var response = await _client.GetAsync($"/api/permissions?role_id={notExistingRoleId}");

            // Assert
            Assert.True(response.StatusCode == HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task Get_Permissions_ReturnsBadRequest_WhenRoleIdMissing()
        {
            // Act
            var response = await _client.GetAsync("/api/permissions");

            // Assert
            Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        }
    }
}
