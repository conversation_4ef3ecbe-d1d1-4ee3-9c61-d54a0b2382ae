﻿using Paas.Model;
using System.Net.Http.Json;

namespace Paas.Tests.IntegrationTests
{
    public class ApisControllerIntegrationTests : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public ApisControllerIntegrationTests(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task Get_Apis_ReturnsSuccess()
        {
            var response = await _client.GetAsync("/v1/Apis");
            response.EnsureSuccessStatusCode(); // 200 OK
        }

        [Fact]
        public async Task Get_ApiById_ReturnsSuccess_WhenExists()
        {
            var existingId = new Guid("0195da7d-1a66-77ef-893d-d60e175bd81d"); // 替換為實際存在的API ID
            var response = await _client.GetAsync($"/v1/Apis/{existingId}");
            response.EnsureSuccessStatusCode(); // 200 OK
        }

        [Fact]
        public async Task Get_ApiById_ReturnsNotFound_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var response = await _client.GetAsync($"/v1/Apis/{nonExistingId}");
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode); // 404 Not Found
        }

        [Fact]
        public async Task Post_Api_ReturnsCreated_WhenValid()
        {
            var model = new ApisCreateModel
            {
                tenant_id = new Guid("01961463-a607-7af4-8a90-c6a0e420a0f8"), // 替換為實際存在的API ID
                name = "Test API",
                description = "Test description",
                version = "1.0.0",
                rate_limit_per_minute = 100
            };

            var response = await _client.PostAsJsonAsync("/v1/Apis", model);
            Assert.Equal(System.Net.HttpStatusCode.Created, response.StatusCode); // 201 Created
        }

        [Fact]
        public async Task Put_Api_ReturnsOk_WhenValid()
        {
            var existingId = new Guid("01961472-494d-7758-81b7-90c009841a34"); // 替換為實際存在的API ID
            var model = new ApisUpdateModel
            {
                name = "Updated API",
                version = "2.0.0",
                rate_limit_per_minute = 200,
                is_active = true
            };

            var response = await _client.PutAsJsonAsync($"/v1/Apis/{existingId}", model);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode); // 200 OK
        }

        [Fact]
        public async Task Put_Api_ReturnsInternalServerError_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var model = new ApisUpdateModel
            {
                name = "NotFound API",
                version = "1.0.0",
                rate_limit_per_minute = 100,
                is_active = true
            };

            var response = await _client.PutAsJsonAsync($"/v1/Apis/{nonExistingId}", model);
            Assert.Equal(System.Net.HttpStatusCode.InternalServerError, response.StatusCode); // 500 Internal Server Error
        }

        [Fact]
        public async Task Delete_Api_ReturnsNoContent_WhenDeleted()
        {
            var existingId = new Guid("01961472-494d-7758-81b7-90c009841a34"); // 替換為實際存在的API ID
            var response = await _client.DeleteAsync($"/v1/Apis/{existingId}");
            Assert.Equal(System.Net.HttpStatusCode.NoContent, response.StatusCode); // 204 No Content
        }

        [Fact]
        public async Task Delete_Api_ReturnsNotFound_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var response = await _client.DeleteAsync($"/v1/Apis/{nonExistingId}");
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode); // 404 Not Found
        }
    }
}
