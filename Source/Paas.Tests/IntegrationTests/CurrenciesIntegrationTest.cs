﻿using Paas.Model;
using System.Net.Http.Json;

namespace Paas.Tests.IntegrationTests
{
    public class CurrenciesIntegrationTest : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public CurrenciesIntegrationTest(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        // 測試獲取所有幣別
        [Fact]
        public async Task Get_Currencies_ReturnsSuccessStatusCode()
        {
            var response = await _client.GetAsync("/v1/currencies");
            response.EnsureSuccessStatusCode(); // 確保返回成功的狀態碼 (200 OK)
        }

        // 測試根據幣別 ID 獲取特定幣別 (存在資料)
        [Fact]
        public async Task Get_Currency_ReturnsSuccessStatusCode_WhenDataExist()
        {
            var existingCurrencyId = "0195c712-a940-753b-8cf9-d5ef3626abc3"; // 假設此 ID 已經存在於資料庫中
            var response = await _client.GetAsync($"/v1/currencies/{existingCurrencyId}");
            response.EnsureSuccessStatusCode(); // 確認能成功獲取該幣別資料
        }

        // 測試根據幣別 ID 獲取特定幣別 (不存在資料)
        [Fact]
        public async Task Get_Currency_ReturnsNotFound_WhenDataNotFound()
        {
            var nonExistingCurrencyId = Guid.NewGuid(); // 使用隨機生成的 UUID，假設該 ID 不存在
            var response = await _client.GetAsync($"/v1/currencies/{nonExistingCurrencyId}");

            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode); // 確認返回 404 NotFound
        }

        // 測試創建新的幣別
        [Fact]
        public async Task Post_Currency_ReturnsCreatedStatusCode_WhenDataIsValid()
        {
            var requestModel = new CurrenciesCreateModel
            {
                code = "GBP",
                symbol = "£",
                exchange_rate = 0.75m,
                is_active = true
            };

            var response = await _client.PostAsJsonAsync("/v1/currencies", requestModel);

            Assert.Equal(System.Net.HttpStatusCode.Created, response.StatusCode); // 確認返回 201 Created
        }

        // 測試更新特定幣別
        [Fact]
        public async Task Put_Currency_ReturnsSuccessStatusCode_WhenDataIsValid()
        {
            var existingCurrencyId = "0195c753-c7f5-7dae-87b3-28ca8d47ca36"; // 假設此 ID 已經存在於資料庫中

            var requestModel = new CurrenciesUpdateModel
            {
                currency_id = new Guid(existingCurrencyId),
                code = "JPY",
                symbol = "¥",
                exchange_rate = 110.0m,
                is_active = true
            };

            var response = await _client.PutAsJsonAsync($"/v1/currencies/{existingCurrencyId}", requestModel);

            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode); // 確認返回 200 OK
        }

        // 測試刪除特定幣別
        [Fact]
        public async Task Delete_Currency_ReturnsNoContent_WhenDataIsDeleted()
        {
            var existingCurrencyId = "01961449-4627-777a-898c-9bf3ce2cc088"; // 假設此 ID 已經存在於資料庫中

            var response = await _client.DeleteAsync($"/v1/currencies/{existingCurrencyId}");

            Assert.Equal(System.Net.HttpStatusCode.NoContent, response.StatusCode); // 確認返回 204 No Content
        }

        // 測試刪除不存在的幣別
        [Fact]
        public async Task Delete_Currency_ReturnsNotFound_WhenDataNotFound()
        {
            var nonExistingCurrencyId = Guid.NewGuid(); // 使用隨機生成的 UUID，假設該 ID 不存在

            var response = await _client.DeleteAsync($"/v1/currencies/{nonExistingCurrencyId}");

            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode); // 確認返回 404 NotFound
        }

        // 測試使用查詢參數過濾幣別資料
        [Fact]
        public async Task Get_Currencies_ReturnsFilteredData_WhenQueryParamsAreProvided()
        {
            var query = new Dictionary<string, string>
            {
                { "code", "USD" }, // 假設幣別代碼
                { "is_active", "true" } // 假設啟用狀態
            };

            var queryString = await new FormUrlEncodedContent(query).ReadAsStringAsync();
            var response = await _client.GetAsync($"/v1/currencies?{queryString}");

            response.EnsureSuccessStatusCode(); // 確認能成功獲取過濾後的幣別資料
        }
    }
}
