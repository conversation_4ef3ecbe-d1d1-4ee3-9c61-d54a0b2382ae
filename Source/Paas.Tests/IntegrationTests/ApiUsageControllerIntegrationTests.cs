﻿using Paas.Model;
using System.Net.Http.Json;

namespace Paas.Tests.IntegrationTests
{
    public class ApiUsageControllerIntegrationTests : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public ApiUsageControllerIntegrationTests(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task Get_ApiUsage_ReturnsSuccess()
        {
            var response = await _client.GetAsync("/v1/api_usage");
            response.EnsureSuccessStatusCode(); // 200 OK
        }

        [Fact]
        public async Task Get_ApiUsageById_ReturnsSuccess_WhenExists()
        {
            var existingId = new Guid("0195d6ef-3b66-77cf-88a0-7a26b44ee75c"); // 替換為實際存在的 usage_id
            var response = await _client.GetAsync($"/v1/api_usage/{existingId}");
            response.EnsureSuccessStatusCode(); // 200 OK
        }

        [Fact]
        public async Task Get_ApiUsageById_ReturnsNotFound_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var response = await _client.GetAsync($"/v1/api_usage/{nonExistingId}");
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode); // 404 Not Found
        }

        [Fact]
        public async Task Post_ApiUsage_ReturnsCreated_WhenValid()
        {
            var model = new ApiUsageCreateModel
            {
                api_id = new Guid("0195847f-1a98-7349-8e08-3a7e391dfe26"), // 替換為實際存在的 api_id
                key_id = new Guid("0195d6e1-8a6f-7d17-8767-0e5383993982"), // 替換為實際存在的 key_id
                request_ip = "***********",
                response_time = 200.5m,
                status_code = 200
            };

            var response = await _client.PostAsJsonAsync("/v1/api_usage", model);
            Assert.Equal(System.Net.HttpStatusCode.Created, response.StatusCode); // 201 OK
        }

        [Fact]
        public async Task Post_ApiUsage_ReturnsBadRequest_WhenInvalid()
        {
            var model = new ApiUsageCreateModel
            {
                api_id = Guid.Empty, // Invalid GUID to simulate bad request
                key_id = Guid.NewGuid(),
                request_ip = "***********",
                response_time = 200.5m,
                status_code = 200
            };

            var response = await _client.PostAsJsonAsync("/v1/api_usage", model);
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode); // 400 Bad Request
        }

        [Fact]
        public async Task Get_ApiUsageWithQuery_ReturnsFilteredResults()
        {
            var api_id = new Guid("0195847f-1a98-7349-8e08-3a7e391dfe26"); // 替換為實際存在的 api_id
            var start_time = DateTimeOffset.UtcNow.AddMonths(-1);
            var end_time = DateTimeOffset.UtcNow;

            var queryString = await new FormUrlEncodedContent(new Dictionary<string, string>
            {
                { "api_id", api_id.ToString() },
                { "start_time", start_time.ToString("o") },
                { "end_time", end_time.ToString("o") },
                { "limit", 5.ToString() },
                { "offset",0.ToString() },
            }).ReadAsStringAsync();

            var response = await _client.GetAsync($"/v1/api_usage?{queryString}");
            response.EnsureSuccessStatusCode(); // 200 OK
        }
    }
}
