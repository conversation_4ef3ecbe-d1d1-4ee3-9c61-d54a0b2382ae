using System.Net;
using System.Net.Http.Json;
using Microsoft.AspNetCore.Mvc.Testing;
using Paas;
using Paas.Model;
using Xunit;

namespace Paas.Tests.IntegrationTests
{
    public class NotificationChannelsControllerTest : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public NotificationChannelsControllerTest(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task GetNotificationChannels_ReturnsOk()
        {
            var response = await _client.GetAsync("/v1/notification_channels");
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);

            var data = await response.Content.ReadFromJsonAsync<List<NotificationChannelsViewModel>>();
            Assert.NotNull(data);
        }

        [Fact]
        public async Task InsertNotificationChannel_ReturnsCreated()
        {
            var model = new NotificationChannelsCreateModel
            {
                type = "email",
                config = new NotificationChannelsConfig { smtp_host = "smtp.test.com", port = 25 },
                is_active = true
            };

            var response = await _client.PostAsJsonAsync("/v1/notification_channels", model);
            Assert.Equal(HttpStatusCode.Created, response.StatusCode);

            var created = await response.Content.ReadFromJsonAsync<NotificationChannelsViewModel>();
            Assert.NotNull(created);
            Assert.Equal(model.type, created?.type);
        }

        [Fact]
        public async Task UpdateNotificationChannel_ReturnsOk()
        {
            var createModel = new NotificationChannelsCreateModel
            {
                type = "email",
                config = new NotificationChannelsConfig { smtp_host = "smtp.initial.com", port = 587 },
                is_active = true
            };

            var createResponse = await _client.PostAsJsonAsync("/v1/notification_channels", createModel);
            var created = await createResponse.Content.ReadFromJsonAsync<NotificationChannelsViewModel>();

            var updateModel = new NotificationChannelsUpdateModel
            {
                config = new NotificationChannelsConfig { smtp_host = "smtp.updated.com", port = 2525 },
                is_active = false
            };

            var updateResponse = await _client.PutAsJsonAsync($"/v1/notification_channels/{created.channel_id}", updateModel);
            Assert.Equal(HttpStatusCode.OK, updateResponse.StatusCode);

            var updated = await updateResponse.Content.ReadFromJsonAsync<NotificationChannelsViewModel>();
            Assert.Equal(updateModel.config?.smtp_host, updated?.config?.smtp_host);
            Assert.False(updated?.is_active);
        }

        [Fact]
        public async Task DeleteNotificationChannel_ReturnsNoContent()
        {
            // Step 1: Create
            var createModel = new NotificationChannelsCreateModel
            {
                type = "email",
                config = new NotificationChannelsConfig { smtp_host = "smtp.delete.com", port = 1025 },
                is_active = true
            };

            var createResponse = await _client.PostAsJsonAsync("/v1/notification_channels", createModel);
            var created = await createResponse.Content.ReadFromJsonAsync<NotificationChannelsViewModel>();

            // Step 2: Delete
            var deleteResponse = await _client.DeleteAsync($"/v1/notification_channels/{created.channel_id}");
            Assert.Equal(HttpStatusCode.NoContent, deleteResponse.StatusCode);
        }
    }
}
