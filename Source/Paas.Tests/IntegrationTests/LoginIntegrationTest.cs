using System.Net;
using System.Net.Http.Json;
using Microsoft.AspNetCore.Identity.Data;
using Xunit;

namespace Paas.Tests.IntegrationTests
{
    public class LoginIntegrationTest : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public LoginIntegrationTest(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task Login_ValidCredentials_ReturnsOkAndToken()
        {
            // Arrange
            var request = new LoginRequest { Email = "string", Password = "string" };

            // Act
            var response = await _client.PostAsJsonAsync("/v1/Login", request);

            // Assert
            response.EnsureSuccessStatusCode(); // Status Code 200-299
            var content = await response.Content.ReadFromJsonAsync<Dictionary<string, string>>();
            Assert.NotNull(content);
            Assert.True(content.ContainsKey("token"));
            Assert.False(string.IsNullOrEmpty(content["token"]));
        }

        [Fact]
        public async Task Login_InvalidCredentials_ReturnsUnauthorized()
        {
            // Arrange
            var request = new LoginRequest { Email = "<EMAIL>", Password = "wrong" };

            // Act
            var response = await _client.PostAsJsonAsync("/v1/Login", request);

            // Assert
            Assert.Equal(HttpStatusCode.Unauthorized, response.StatusCode);
        }

        [Fact]
        public async Task ProtectedEndpoint_NoToken_ReturnsUnauthorized()
        {
            // Act
            var response = await _client.GetAsync("/v1/Login/protected");

            // Assert
            Assert.Equal(HttpStatusCode.Unauthorized, response.StatusCode);
        }

        [Fact]
        public async Task ProtectedEndpoint_WithValidToken_ReturnsOk()
        {
            // Arrange: First, get a valid token
            var loginRequest = new LoginRequest { Email = "string", Password = "string" };
            var loginResponse = await _client.PostAsJsonAsync("/v1/Login", loginRequest);
            loginResponse.EnsureSuccessStatusCode();
            var loginContent = await loginResponse.Content.ReadFromJsonAsync<Dictionary<string, string>>();
            var token = loginContent["token"];

            // Add the token to the Authorization header
            _client.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

            // Act
            var protectedResponse = await _client.GetAsync("/v1/Login/protected");

            // Assert
            protectedResponse.EnsureSuccessStatusCode(); // Status Code 200-299
            var protectedContent = await protectedResponse.Content.ReadFromJsonAsync<Dictionary<string, string>>();
            Assert.NotNull(protectedContent);
            Assert.True(protectedContent.ContainsKey("message"));
            Assert.Equal("你已成功訪問受保護的 API！", protectedContent["message"]);
        }
    }
}