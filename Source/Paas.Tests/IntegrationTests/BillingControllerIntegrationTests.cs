﻿using Paas.Model;
using System.Net.Http.Json;

namespace Paas.Tests.IntegrationTests
{
    public class BillingControllerIntegrationTests : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public BillingControllerIntegrationTests(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task Get_BillingList_ReturnsSuccess()
        {
            var tenantId = new Guid("0195830d-deda-7e45-8485-879e7fd6b7a7"); // 使用一個有效的租戶ID
            var queryModel = new BaseQueryModel
            {
                Limit = 10,
                Offset = 0
            };

            var response = await _client.GetAsync($"/v1/billing?tenant_id={tenantId}&status=Active&limit={queryModel.Limit}&offset={queryModel.Offset}");

            response.EnsureSuccessStatusCode(); // 200 OK
        }

        [Fact]
        public async Task Get_BillingById_ReturnsSuccess_WhenExists()
        {
            var existingId = new Guid("0195db92-0c6d-74cb-8c18-aa7afb2ccc15"); // 替換為有效的帳單ID
            var response = await _client.GetAsync($"/v1/billing/{existingId}");

            response.EnsureSuccessStatusCode(); // 200 OK
        }

        [Fact]
        public async Task Get_BillingById_ReturnsNotFound_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var response = await _client.GetAsync($"/v1/billing/{nonExistingId}");

            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode); // 404 Not Found
        }

        [Fact]
        public async Task Post_Billing_ReturnsCreated_WhenValid()
        {
            var model = new BillingCreateModel
            {
                tenant_id = new Guid("0195830d-deda-7e45-8485-879e7fd6b7a7"),
                amount = 1000.00m,
                currency_id = new Guid("0195830c-f670-759e-8cd7-7bb9f569eb62"),
                payment_method_id = new Guid("0195db7b-a91c-7aff-82b8-93bf80b74fc4"),
                status = "pending",
                due_date = DateTimeOffset.Now.AddDays(30)
            };

            var response = await _client.PostAsJsonAsync("/v1/billing", model);

            Assert.Equal(System.Net.HttpStatusCode.Created, response.StatusCode); // 201
        }

        [Fact]
        public async Task Post_Billing_ReturnsBadRequest_WhenInvalid()
        {
            var model = new BillingCreateModel
            {
                tenant_id = Guid.Empty, // 使用無效的 GUID 模擬壞請求
                amount = 1000.00m,
                currency_id = Guid.NewGuid(),
                payment_method_id = Guid.NewGuid(),
                status = "Pending",
                due_date = DateTimeOffset.Now.AddDays(30)
            };

            var response = await _client.PostAsJsonAsync("/v1/billing", model);

            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode); // 400 Bad Request
        }

        [Fact]
        public async Task Put_Billing_ReturnsSuccess_WhenValid()
        {
            var billingId = new Guid("0195db92-28ee-71e5-885d-162ee233467a"); // 替換為有效的帳單ID
            var updateModel = new BillingUpdateModel
            {
                status = "paid",
                paid_at = DateTimeOffset.Now
            };

            var response = await _client.PutAsJsonAsync($"/v1/billing/{billingId}", updateModel);

            response.EnsureSuccessStatusCode(); // 200 OK
        }

        [Fact]
        public async Task Delete_Billing_ReturnsNoContent_WhenExists()
        {
            var billingId = new Guid("019614de-cbcf-707f-8b0d-1fafc90349e8"); // 替換為有效的帳單ID
            var response = await _client.DeleteAsync($"/v1/billing/{billingId}");

            Assert.Equal(System.Net.HttpStatusCode.NoContent, response.StatusCode); // 204 No Content
        }

        [Fact]
        public async Task Delete_Billing_ReturnsNotFound_WhenNotExists()
        {
            var nonExistingId = Guid.NewGuid();
            var response = await _client.DeleteAsync($"/v1/billing/{nonExistingId}");

            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode); // 404 Not Found
        }
    }
}
