﻿using Paas.Model;
using System.Net.Http.Json;
using System.Net;

namespace Paas.Tests.IntegrationTests
{
    public class RolesControllerIntegrationTests : IClassFixture<CustomWebApplicationFactory>
    {
        private readonly HttpClient _client;

        public RolesControllerIntegrationTests(CustomWebApplicationFactory factory)
        {
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task Get_RolesList_ReturnsSuccess()
        {
            var response = await _client.GetAsync("/api/roles");

            Assert.True(response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task Post_Role_ReturnsCreated_WhenValid()
        {
            var model = new RolesCreateModel
            {
                name = "Test Role",
                description = "This is a test role"
            };

            var response = await _client.PostAsJsonAsync("/api/roles", model);

            Assert.Equal(HttpStatusCode.Created, response.StatusCode);
        }

        [Fact]
        public async Task Post_Role_ReturnsBadRequest_WhenInvalid()
        {
            var model = new RolesCreateModel
            {
                name = "", // Invalid: empty name
                description = "Missing name field"
            };

            var response = await _client.PostAsJsonAsync("/api/roles", model);

            Assert.True(response.StatusCode == HttpStatusCode.BadRequest || response.StatusCode == HttpStatusCode.InternalServerError);
        }
    }
}
