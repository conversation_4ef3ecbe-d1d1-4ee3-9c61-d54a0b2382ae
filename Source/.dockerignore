# .NET Core 建置輸出
**/bin/
**/obj/
**/out/

# Visual Studio 檔案
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates

# 測試結果
TestResults/
[Tt]est[Rr]esult*/
*.trx
*.coverage
*.coveragexml

# NuGet 套件
*.nupkg
*.snupkg
packages/
!packages/build/

# 暫存檔案
*.tmp
*.temp
*.log
*.cache

# OS 產生的檔案
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 檔案
.vscode/
.idea/
*.swp
*.swo
*~

# Docker 檔案
Dockerfile*
.dockerignore

# Git 檔案
.git/
.gitignore
.gitattributes

# 文件檔案
*.md
README*

# 設定檔案 (可能包含敏感資訊)
appsettings.Development.json
appsettings.Local.json
*.pfx
*.p12

# 其他不需要的檔案
*.http
.env
.env.local
.env.development
.env.test
.env.production
