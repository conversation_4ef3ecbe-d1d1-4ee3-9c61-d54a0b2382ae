using Dapper;
using Microsoft.Data.SqlClient;
using Paas.Dac.Interface;
using System.Web;

namespace Paas.Dac
{
    /// <summary>
    /// 資料存取層基礎類別
    /// </summary>
    /// <remarks>
    /// 此類別為所有資料存取層類別提供基礎功能，封裝共通的資料庫操作方法。
    /// 包含資料庫查詢、命令執行和 XSS 防護等功能。
    /// 管理資料庫連線控制中心的選擇與使用，以適應不同的資料庫提供者。
    /// 為直接繼承的資料存取類別提供統一的資料存取介面，簡化資料存取操作並增強安全性。
    /// </remarks>
    public class _Dac
    {
        /// <summary>
        /// 資料庫連線控制中心集合
        /// </summary>
        /// <remarks>
        /// 存儲可用的資料庫連線控制中心，用於建立資料庫連線。
        /// 此集合可能包含不同類型的資料庫連線控制中心，如 SQL Server 和 PostgreSQL 等。
        /// </remarks>
        private readonly IEnumerable<IConnectionControlCenter> _connectionControlCenter;

        /// <summary>
        /// 初始化資料存取層基礎類別
        /// </summary>
        /// <remarks>
        /// 建構子接收一個資料庫連線控制中心集合，用於管理各種資料庫的連線。
        /// 此集合包含不同類型的連線控制中心，如 SQL Server 和 PostgreSQL 提供者。
        /// 網所有資料庫操作提供統一的連線管理功能。
        /// </remarks>
        /// <param name="connectionControlCenter">資料庫連線控制中心集合</param>
        public _Dac(IEnumerable<IConnectionControlCenter> connectionControlCenter)
        {
            _connectionControlCenter = connectionControlCenter;
        }

        /// <summary>
        /// 當前時間的 SQL 函數表示
        /// </summary>
        /// <remarks>
        /// 代表 SQL 查詢中用於取得當前時間的函數表示式。
        /// 預設為 PostgreSQL 的 CURRENT_TIMESTAMP 函數，可用於插入或更新操作的時間欄位。
        /// 此屬性由派生類別使用，確保所有時間欄位使用一致的格式。
        /// </remarks>
        protected string Now = "CURRENT_TIMESTAMP";

        /// <summary>
        /// 根據提供者名稱選擇適當的資料庫連線控制中心
        /// </summary>
        /// <remarks>
        /// 此方法在提供的連線控制中心集合中尋找符合指定提供者名稱的連線控制中心。
        /// 使用大小寫不敏感的字串比較進行提供者名稱的匹配。
        /// 如果找不到符合指定提供者名稱的連線控制中心，會拋出一個異常。
        /// 此方法為內部使用，為其他資料存取方法提供正確的連線控制中心。
        /// </remarks>
        /// <param name="provider">要查找的資料庫提供者名稱，例如 "PostgreSql" 或 "SqlServer"</param>
        /// <returns>符合指定提供者名稱的連線控制中心</returns>
        /// <exception cref="Exception">當找不到符合指定提供者名稱的連線控制中心時拋出</exception>
        private IConnectionControlCenter GetConnectionControlCenter(string provider)
        {
            var connectionControl = _connectionControlCenter.FirstOrDefault(cc =>
                cc.ProviderName.Equals(provider, StringComparison.OrdinalIgnoreCase));

            if (connectionControl == null)
            {
                throw new Exception($"找不到 '{provider}' 的連線控制中心");
            }

            return connectionControl;
        }

        /// <summary>
        /// 執行資料庫查詢操作並返回結果集合
        /// </summary>
        /// <remarks>
        /// 此方法使用 Dapper 執行 SQL 查詢並返回指定類型的物件集合。
        /// 根據指定的提供者選擇適當的資料庫連線控制中心，建立資料庫連線。
        /// 已設定 200 秒的查詢逾時時間，適用於較複雜的查詢。
        /// 查詢結果會經過 XSS 清洗處理，除非 isRaw 參數為 true。
        /// 若發生 SQL 异常，會將异常向上拋出供上層處理。
        /// </remarks>
        /// <typeparam name="T">查詢結果的目標類型</typeparam>
        /// <param name="sql">SQL 查詢語句</param>
        /// <param name="param">查詢參數物件，可為 null</param>
        /// <param name="isRaw">是否跳過 XSS 清洗，預設為 false</param>
        /// <param name="provider">資料庫提供者名稱，預設為 "PostgreSql"</param>
        /// <returns>查詢結果的物件集合</returns>
        /// <exception cref="SqlException">發生 SQL 查詢異常時拋出</exception>
        protected IList<T> ExecuteQuery<T>(string sql, object? param = null, bool isRaw = false, string provider = "PostgreSql")
        {
            // 依據 provider 取得對應的 ConnectionControlCenter
            var connectionControl = GetConnectionControlCenter(provider);
            var result = new List<T>();
            using (System.Data.IDbConnection conn = connectionControl.CreateConnection())
            {
                try
                {
                    result = ParseXSSResult(conn.Query<T>(sql, param, commandTimeout: 200), isRaw).ToList();
                }
                catch (SqlException _)
                {
                    // 先設定直接丟出
                    throw;
                }

                return result;
            }
        }

        /// <summary>
        /// Query
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="sql"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        protected async Task<IList<T>> ExecuteQueryAsync<T>(string sql, object? param = null, bool isRaw = false, string provider = "PostgreSql")
        {
            // 依據 provider 取得對應的 ConnectionControlCenter
            var connectionControl = GetConnectionControlCenter(provider);
            IList<T> result;
            using (System.Data.IDbConnection conn = connectionControl.CreateConnection())
            {
                try
                {
                    result = ParseXSSResult(await conn.QueryAsync<T>(sql, param), isRaw);
                }
                catch (SqlException _)
                {
                    // 先設定直接丟出
                    throw;
                }

                return result;
            }
        }

        /// <summary>
        /// Command
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<bool> ExecuteCommandAsync(string sql, object? param = null, string provider = "PostgreSql")
        {
            // 依據 provider 取得對應的 ConnectionControlCenter
            var connectionControl = GetConnectionControlCenter(provider);
            bool result = false;
            using (System.Data.IDbConnection conn = connectionControl.CreateConnection())
            {
                try
                {
                    result = await conn.ExecuteAsync(sql, param, commandTimeout: 200) > 0;
                }
                catch (SqlException _)
                {
                    // TODO 先設定直接丟出
                    throw;
                }
            }
            return result;
        }
        private async Task<bool> ExecuteAsync(string sql, object? param, string provider = "PostgreSql")
        {
            // 依據 provider 取得對應的 ConnectionControlCenter
            var connectionControl = GetConnectionControlCenter(provider);
            bool result = false;
            using (System.Data.IDbConnection conn = connectionControl.CreateConnection())
            {
                try
                {
                    result = await conn.ExecuteAsync(sql, param, commandTimeout: 200) > 0;
                }
                catch (SqlException _)
                {
                    // TODO 先設定直接丟出
                    throw;
                }
            }
            return result;
        }


        /// <summary>
        /// Command
        /// </summary>
        /// <param name="sql">SQL語法</param>
        /// <param name="param">物件</param>
        /// <returns></returns>
        protected bool ExecuteCommand(string sql, object? param = null, string provider = "PostgreSql")
        {
            // 依據 provider 取得對應的 ConnectionControlCenter
            var connectionControl = GetConnectionControlCenter(provider);
            bool result = false;
            using (System.Data.IDbConnection conn = connectionControl.CreateConnection())
            {
                try
                {
                    result = conn.Execute(sql, param, commandTimeout: 200) > 0;
                }
                catch (SqlException _)
                {
                    // TODO 先設定直接丟出
                    throw;
                }
            }
            return result;
        }

        /// <summary>
        /// 防止 Stored XSS 攻擊處理
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="result"></param>
        /// <returns></returns>
        private IList<T> ParseXSSResult<T>(IEnumerable<T> result,bool isRaw)
        {
            // 如果為json字串欄位則不需encode
            if (isRaw)
            {
                return result.ToList();
            }

            if (typeof(T) == typeof(string))
            {
                return result.AsParallel().AsOrdered().Select(p => (T)Convert.ChangeType(HttpUtility.HtmlEncode(p?.ToString()?.Trim() ?? string.Empty), typeof(T))).ToList();
            }
            else if (typeof(T) == typeof(IDictionary<string, object>))
            {
                return result.Select(p =>
                {
                    foreach (KeyValuePair<string, object> keyValue in p as IDictionary<string, object> ?? new Dictionary<string, object>())
                    {

                        if (keyValue.Value != null && keyValue.Value is string)
                        {
                            (p as IDictionary<string, object>)![keyValue.Key] = HttpUtility.HtmlEncode(keyValue.Value.ToString()?.Trim() ?? string.Empty);
                        }
                    }
                    return (T)p!;
                }).ToList();
            }
            else
            {
                return result.AsParallel().AsOrdered().Select(p =>
                {
                    if (p != null)
                    {
                        Parallel.ForEach(p.GetType().GetProperties(), prop =>
                        {
                            if (prop.PropertyType == typeof(string) &&
                                (prop.CanRead && prop.GetValue(p) != null) &&
                                prop.CanWrite)
                            {
                                prop.SetValue(p, HttpUtility.HtmlEncode(prop.GetValue(p)?.ToString()?.Trim() ?? string.Empty));
                            }
                        });
                    }
                    return p!;
                }).ToList();
            }
        }
    }
}
