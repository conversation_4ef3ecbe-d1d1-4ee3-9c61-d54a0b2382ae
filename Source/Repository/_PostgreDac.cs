using Dapper;
using Microsoft.Data.SqlClient;
using Paas.Dac.Interface;
using System.Web;

namespace Paas.Dac
{
    /// <summary>
    /// PostgreSQL 資料存取層基礎類別
    /// </summary>
    /// <remarks>
    /// 此類別為 PostgreSQL 資料庫的資料存取層提供基礎功能，封裝共通的 PostgreSQL 資料庫操作方法。
    /// 包含資料庫查詢、命令執行和 XSS 防護等功能。
    /// 只管理 PostgreSQL 資料庫的連線使用，為直接繼承的資料存取類別提供統一的 PostgreSQL 資料存取介面。
    /// 簡化資料存取操作並增強安全性，完全專注於 PostgreSQL 的操作處理。
    /// </remarks>
    public class _PostgreDac
    {
        /// <summary>
        /// 資料庫連線控制中心集合
        /// </summary>
        /// <remarks>
        /// 存儲可用的資料庫連線控制中心，操作時將特別選擇 PostgreSQL 連線定定控制中心。
        /// 集合中可能包含多種提供者的連線控制中心，但只會使用 PostgreSQL 連線控制中心。
        /// </remarks>
        private readonly IEnumerable<IConnectionControlCenter> _connectionControlCenter;

        /// <summary>
        /// 初始化 PostgreSQL 資料存取層基礎類別
        /// </summary>
        /// <remarks>
        /// 建構子接收一個資料庫連線控制中心集合，用於管理 PostgreSQL 資料庫的連線。
        /// 帶入的連線控制中心集合必須包含一個 ProviderName 為 "PostgreSql" 的連線控制中心。
        /// </remarks>
        /// <param name="connectionControlCenter">資料庫連線控制中心集合</param>
        public _PostgreDac(IEnumerable<IConnectionControlCenter> connectionControlCenter)
        {
            _connectionControlCenter = connectionControlCenter;
        }

        /// <summary>
        /// 根據提供者名稱選擇適當的資料庫連線控制中心
        /// </summary>
        /// <remarks>
        /// 此方法在提供的連線控制中心集合中尋找符合指定提供者名稱的連線控制中心。
        /// 使用大小寫不敏感的字串比較進行提供者名稱的匹配。
        /// 如果找不到符合指定提供者名稱的連線控制中心，會拋出一個異常。
        /// 此方法為內部使用，主要用於獲取 PostgreSQL 的連線控制中心。
        /// </remarks>
        /// <param name="provider">要查找的資料庫提供者名稱，通常為 "PostgreSql"</param>
        /// <returns>符合指定提供者名稱的連線控制中心</returns>
        /// <exception cref="Exception">當找不到符合指定提供者名稱的連線控制中心時拋出</exception>
        private IConnectionControlCenter GetConnectionControlCenter(string provider)
        {
            var connectionControl = _connectionControlCenter.FirstOrDefault(cc =>
                cc.ProviderName.Equals(provider, StringComparison.OrdinalIgnoreCase));

            if (connectionControl == null)
            {
                throw new Exception($"找不到 '{provider}' 的連線控制中心");
            }

            return connectionControl;
        }

        /// <summary>
        /// 執行 PostgreSQL 資料庫查詢操作並返回結果集合
        /// </summary>
        /// <remarks>
        /// 此方法使用 Dapper 執行 PostgreSQL 查詢並返回指定類型的物件集合。
        /// 統一使用 PostgreSQL 連線控制中心，建立 PostgreSQL 資料庫連線。
        /// 已設定 200 秒的查詢逾時時間，適用於較複雜的查詢。
        /// 查詢結果會經過 XSS 清洗處理，除非 isRaw 參數為 true。
        /// 若發生 SQL 异常，會將异常向上拋出供上層處理。
        /// </remarks>
        /// <typeparam name="T">查詢結果的目標類型</typeparam>
        /// <param name="sql">PostgreSQL 查詢語句</param>
        /// <param name="param">查詢參數物件，可為 null</param>
        /// <param name="isRaw">是否跳過 XSS 清洗，預設為 false</param>
        /// <returns>查詢結果的物件集合</returns>
        /// <exception cref="SqlException">發生 SQL 查詢異常時拋出</exception>
        protected IList<T> ExecuteQuery<T>(string sql, object? param = null, bool isRaw = false)
        {
            // 依據 provider 取得對應的 ConnectionControlCenter
            var connectionControl = GetConnectionControlCenter("PostgreSql");
            var result = new List<T>();
            using (System.Data.IDbConnection conn = connectionControl.CreateConnection())
            {
                try
                {
                    result = ParseXSSResult(conn.Query<T>(sql, param, commandTimeout: 200), isRaw).ToList();
                }
                catch (SqlException _)
                {
                    // 先設定直接丟出
                    throw;
                }

                return result;
            }
        }

        /// <summary>
        /// 非同步執行 PostgreSQL 資料庫命令操作
        /// </summary>
        /// <remarks>
        /// 此方法非同步執行 PostgreSQL 資料庫命令，如 INSERT、UPDATE 或 DELETE 操作。
        /// 使用 Dapper 的 ExecuteAsync 方法執行 SQL 命令，並返回操作是否成功的布林值。
        /// 已設定 200 秒的命令逾時時間，適用於較複雜的操作。
        /// 當至少有一行資料被影響時，返回 true，否則返回 false。
        /// 若發生 SQL 异常，會將异常向上拋出供上層處理。
        /// </remarks>
        /// <param name="sql">PostgreSQL 命令語句，如 INSERT、UPDATE 或 DELETE 語句</param>
        /// <param name="param">命令參數物件，可為 null</param>
        /// <returns>一個非同步布林值，表示操作是否成功影響了至少一行資料</returns>
        /// <exception cref="SqlException">發生 SQL 命令異常時拋出</exception>
        public async Task<bool> ExecuteCommandAsync(string sql, object? param = null)
        {
            // 依據 provider 取得對應的 ConnectionControlCenter
            var connectionControl = GetConnectionControlCenter("PostgreSql");
            bool result = false;
            using (System.Data.IDbConnection conn = connectionControl.CreateConnection())
            {
                try
                {
                    result = await conn.ExecuteAsync(sql, param, commandTimeout: 200) > 0;
                }
                catch (SqlException _)
                {
                    // TODO 先設定直接丟出
                    throw;
                }
            }
            return result;
        }

        /// <summary>
        /// 防止儲存型 XSS 攻擊的結果處理
        /// </summary>
        /// <remarks>
        /// 此方法對資料庫查詢結果進行 XSS 防護處理，確保返回給前端的資料安全。
        /// 根據引數 isRaw 的值決定是否進行 XSS 清洗，當需要原始 JSON 字串時可設置為 true 以跳過清洗。
        /// 根據結果物件的類型選擇不同的處理方式：
        /// 1. 字串類型：直接進行 HTML 編碼
        /// 2. 字典類型：對字典中的字串值進行 HTML 編碼
        /// 3. 自定義類別：使用反射遍歷屬性，對所有字串屬性進行 HTML 編碼
        /// 使用並行處理以提高效能，特別是處理大量的字串或屬性時。
        /// </remarks>
        /// <typeparam name="T">要處理的物件類型</typeparam>
        /// <param name="result">要進行 XSS 清洗的查詢結果集合</param>
        /// <param name="isRaw">是否保留原始字串而不進行 XSS 清洗</param>
        /// <returns>經過 XSS 清洗處理的物件集合</returns>
        private IList<T> ParseXSSResult<T>(IEnumerable<T> result, bool isRaw)
        {
            // 如果為json字串欄位則不需encode
            if (isRaw)
            {
                return result.ToList();
            }

            if (typeof(T) == typeof(string))
            {
                return result.AsParallel().AsOrdered().Select(p => (T)Convert.ChangeType(HttpUtility.HtmlEncode(p?.ToString()?.Trim() ?? string.Empty), typeof(T))).ToList();
            }
            else if (typeof(T) == typeof(IDictionary<string, object>))
            {
                return result.Select(p =>
                {
                    foreach (KeyValuePair<string, object> keyValue in p as IDictionary<string, object> ?? new Dictionary<string, object>())
                    {

                        if (keyValue.Value != null && keyValue.Value is string)
                        {
                            (p as IDictionary<string, object>)![keyValue.Key] = HttpUtility.HtmlEncode(keyValue.Value.ToString()?.Trim() ?? string.Empty);
                        }
                    }
                    return (T)p!;
                }).ToList();
            }
            else
            {
                return result.AsParallel().AsOrdered().Select(p =>
                {
                    if (p != null)
                    {
                        Parallel.ForEach(p.GetType().GetProperties(), prop =>
                        {
                            if (prop.PropertyType == typeof(string) &&
                                (prop.CanRead && prop.GetValue(p) != null) &&
                                prop.CanWrite)
                            {
                                prop.SetValue(p, HttpUtility.HtmlEncode(prop.GetValue(p)?.ToString()?.Trim() ?? string.Empty));
                            }
                        });
                    }
                    return p!;
                }).ToList();
            }
        }
    }
}
