using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Paas.Dac.Interface;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// SQL Server 資料庫連線控制中心
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IConnectionControlCenter 介面，提供 SQL Server 資料庫的連線管理功能。
    /// 負責建立和提供資料庫連線物件，是資料存取層中的基礎元件。
    /// 使用應用程式設定中的連線字串進行資料庫連線初始化。
    /// 為所有的資料存取類別提供 SQL Server 的連線功能。
    /// </remarks>
    public class ConnectionControlCenter : IConnectionControlCenter
    {
        /// <summary>
        /// 主要資料庫連線字串
        /// </summary>
        /// <remarks>
        /// 存儲從應用程式設定中讀取的 SQL Server 連線字串。
        /// </remarks>
        private readonly string strConnMain;
        
        public string ProviderName => "SqlServer";

        public ConnectionControlCenter(IConfiguration configuration)
        {
            strConnMain = configuration.GetConnectionString("SqlServerConnection") ?? throw new ArgumentNullException("SqlServerConnection connection string is not configured.");
        }

        /// <summary>
        /// 建立資料庫連線
        /// </summary>
        /// <remarks>
        /// 此方法建立並返回一個新的 SQL Server 資料庫連線物件。
        /// 每次調用返回一個新的連線實例，資料存取層可以使用此連線執行所需的資料庫操作。
        /// 返回的連線使用在建構子中初始化的連線字串。
        /// 此連線需要由調用者適當地管理，包括打開、關閉和釋放等操作。
        /// </remarks>
        /// <returns>一個實作 IDbConnection 介面的 SqlConnection 物件</returns>
        public IDbConnection CreateConnection()
        {
            return new SqlConnection(strConnMain);
        }
    }
}
