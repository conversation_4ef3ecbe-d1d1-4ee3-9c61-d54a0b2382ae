using Paas.Model;

namespace Paas.Dac
{
    /// <summary>
    /// API 金鑰資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了 API 金鑰資料存取層的基本操作，包含查詢、新增、更新和刪除 API 金鑰的方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的 API 金鑰資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 整合多租戶環境的 API 金鑰管理功能，可根據租戶適當限制資料範圍。
    /// 為系統提供安全的 API 訪問軟體平台的身份驗證機制。
    /// </remarks>
    public interface IApiKeysDac
    {
        /// <summary>
        /// 取得 API 金鑰資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的 API 金鑰資料。
        /// 可根據模型中的租戶 ID、狀態等條件篩選結果。
        /// 查詢結果包含 API 金鑰 ID、金鑰字串、租戶 ID、狀態、生成時間等詳細資訊。
        /// 支援多租戶環境，可根據租戶 ID 適當限制資料範圍。
        /// </remarks>
        /// <param name="model">API 金鑰模型，包含查詢條件如租戶 ID、狀態等</param>
        /// <returns>非同步 API 金鑰記錄集合</returns>
        Task<IList<ApiKeys>> GetApiKeys(ApiKeys model);
        /// <summary>
        /// 根據金鑰 ID 取得特定 API 金鑰資料
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的 API 金鑰 ID 查詢特定金鑰的詳細資訊。
        /// 返回包含 API 金鑰完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含 API 金鑰 ID、金鑰字串、租戶 ID、狀態、生成時間等詳細資訊。
        /// 找不到的情況加以優雅處理，返回 nullable 物件。
        /// </remarks>
        /// <param name="keyId">要查詢的 API 金鑰的唯一識別碼</param>
        /// <returns>非同步 API 金鑰記錄物件，如果未找到則返回 null</returns>
        Task<ApiKeys?> GetApiKey(Guid keyId);
        /// <summary>
        /// 建立新的 API 金鑰
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆 API 金鑰記錄，使用提供的模型作為資料來源。
        /// 會自動產生 API 金鑰的唯一識別碼和金鑰字串（如模型中未提供）。
        /// 會紀錄金鑰的生成時間，為金鑰管理提供追蹤能力。
        /// 返回新建立的 API 金鑰記錄物件，包含完整的金鑰資料和系統生成的 ID。
        /// </remarks>
        /// <param name="model">包含新 API 金鑰資料的模型，如租戶 ID、金鑰字串（可選）、狀態等</param>
        /// <returns>非同步新增的 API 金鑰記錄物件，包含系統生成的 ID 和相關資訊</returns>
        Task<ApiKeys?> InsertApiKey(ApiKeys model);
        /// <summary>
        /// 更新特定 API 金鑰
        /// </summary>
        /// <remarks>
        /// 此方法更新已存在的 API 金鑰記錄，使用提供的模型作為更新的資料來源。
        /// 模型中必須包含要更新的 API 金鑰的唯一識別碼(key_id)。
        /// 可更新金鑰的狀態、相關限制設定等屬性，但通常不更新金鑰字串本身。
        /// 返回更新後的 API 金鑰記錄物件，包含最新的金鑰資料。
        /// </remarks>
        /// <param name="model">包含要更新的 API 金鑰資料的模型，需要包含 key_id</param>
        /// <returns>非同步更新後的 API 金鑰記錄物件，若更新失敗則返回 null</returns>
        Task<ApiKeys?> UpdateApiKey(ApiKeys model);
        /// <summary>
        /// 刪除特定 API 金鑰
        /// </summary>
        /// <remarks>
        /// 此方法根據指定的 API 金鑰 ID 刪除資料庫中的 API 金鑰記錄。
        /// 此操作會永久刪除記錄，刪除後無法恢復，應謹慎使用。
        /// 刪除金鑰後，使用該金鑰的所有 API 調用將無法進行驗證。
        /// 如果指定的 API 金鑰 ID 不存在，將返回 false。
        /// </remarks>
        /// <param name="keyId">要刪除的 API 金鑰的唯一識別碼</param>
        /// <returns>非同步布林值，表示刪除操作是否成功</returns>
        Task<bool> DeleteApiKey(Guid keyId);
    }
}
