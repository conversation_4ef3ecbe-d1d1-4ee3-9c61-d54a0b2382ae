using Paas.Model;

namespace Paas.Dac
{
    /// <summary>
    /// 通知管道資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了通知管道資料存取層的基本操作，包含查詢、建立、更新和刪除通知管道的方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的通知管道資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 整合多租戶環境的通知管道管理功能，可根據租戶適當限制資料範圍。
    /// 為系統提供通知交付機制的基礎支援，使得系統可以透過不同管道（如電子郵件、簡訊、應用內通知等）發送通知。
    /// </remarks>
    public interface INotificationChannelsDac
    {
        /// <summary>
        /// 取得通知管道設定清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的通知管道設定。
        /// 可根據模型中的條件（如租戶 ID、管道類型、狀態等）篩選結果。
        /// 支援狀態過濾，可依據管道的啟用/停用狀態進行查詢。
        /// 支援分頁功能，可指定頁碼和每頁筆數節省資源使用。
        /// 查詢結果包含管道 ID、名稱、類型、設定參數、狀態、創建時間等詳細資訊。
        /// </remarks>
        /// <param name="model">通知管道模型，包含查詢條件如狀態、分頁參數等</param>
        /// <returns>非同步通知管道設定集合</returns>
        Task<IList<NotificationChannels>> GetNotificationChannels(NotificationChannels model);
        /// <summary>
        /// 建立新的通知管道設定
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆通知管道設定記錄，使用提供的模型作為資料來源。
        /// 會自動產生通知管道的唯一識別碼和相關的時間戳記（如創建時間）。
        /// 記錄通知管道的相關資訊，如管道名稱、類型、設定參數、狀態等。
        /// 通知管道類型可能包括電子郵件、簡訊、應用內通知、推播訊息等。
        /// 設定參數可能包含管道特定的設定，如電子郵件伺服器設定、簡訊 API 密鑰等。
        /// </remarks>
        /// <param name="model">包含新通知管道設定的模型，如管道名稱、類型、設定參數、狀態等</param>
        /// <returns>非同步新增的通知管道設定物件，包含系統生成的 ID 和相關資訊</returns>
        Task<NotificationChannels?> InsertNotificationChannel(NotificationChannels model);
        /// <summary>
        /// 更新特定通知管道設定
        /// </summary>
        /// <remarks>
        /// 此方法更新已存在的通知管道設定記錄，使用提供的模型作為更新的資料來源。
        /// 模型中必須包含要更新的通知管道的唯一識別碼(channel_id)。
        /// 可更新的資料包括管道名稱、類型、設定參數、狀態等屬性。
        /// 更新時會自動紀錄修改時間，以追蹤通知管道設定的變化歷程。
        /// 特別要注意的是，更新管道狀態（啟用/停用）可能會影響通知的發送行為。
        /// </remarks>
        /// <param name="model">包含要更新的通知管道設定的模型，需要包含 channel_id</param>
        /// <returns>非同步更新後的通知管道設定物件，若更新失敗則返回 null</returns>
        Task<NotificationChannels?> UpdateNotificationChannel(NotificationChannels model);
        /// <summary>
        /// 刪除特定通知管道設定
        /// </summary>
        /// <remarks>
        /// 此方法根據指定的通知管道 ID 刪除資料庫中的通知管道設定記錄。
        /// 此操作會永久刪除記錄，刪除後無法恢復，應謹慎使用。
        /// 刪除後，系統將不再能使用此管道發送通知，可能會影響現有的通知配置。
        /// 在刪除管道前，應確保沒有相關的通知模板或活動排程依賴該管道。
        /// 如果只是暂時停用管道功能，建議使用更新狀態而非刪除操作。
        /// </remarks>
        /// <param name="channelId">要刪除的通知管道的唯一識別碼</param>
        /// <returns>非同步布林值，表示刪除操作是否成功</returns>
        Task<bool> DeleteNotificationChannel(Guid channelId);
    }
}
