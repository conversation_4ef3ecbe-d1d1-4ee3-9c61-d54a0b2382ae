using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 租戶資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了租戶資料存取層的基本操作，包含查詢、新增、更新和狀態管理等方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的租戶資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 為系統提供多租戶管理功能的基礎支援，是整個系統的核心組件之一。
    /// </remarks>
    public interface ITenantsDac
    {

        /// <summary>
        /// 取得租戶資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的租戶資料。
        /// 可根據模型中的條件（如租戶名稱、狀態、增加時間範圍等）篩選結果。
        /// 查詢結果包含租戶 ID、名稱、描述、狀態、建立時間等詳細資訊。
        /// 用於管理介面、報表生成和租戶管理功能。
        /// </remarks>
        /// <param name="model">租戶模型，包含查詢條件如租戶名稱、狀態等</param>
        /// <returns>非同步租戶資料集合</returns>
        Task<IList<Tenants>> GetTenantsDataList(TenantsModel model);

        /// <summary>
        /// 取得特定租戶資料
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的租戶 ID 查詢特定租戶的詳細資訊。
        /// 返回包含租戶完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含租戶 ID、名稱、描述、狀態、建立時間等詳細資訊。
        /// 用於特定租戶的詳細資訊顯示、編輯和管理功能。
        /// </remarks>
        /// <param name="tenant_id">要查詢的租戶的唯一識別碼</param>
        /// <returns>非同步租戶資料物件，如果未找到則返回 null</returns>
        Task<Tenants?> GetTenantsData(Guid tenant_id);

        /// <summary>
        /// 建立新的租戶資料
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆租戶記錄，使用提供的模型作為資料來源。
        /// 會自動產生租戶的唯一識別碼和相關的時間戳記（如創建時間）。
        /// 記錄租戶的相關資訊，包括名稱、描述、狀態等基本屬性。
        /// 建立新租戶時，其初始狀態通常為啟用狀態，可立即開始使用系統的各種功能。
        /// </remarks>
        /// <param name="model">包含新租戶資料的模型，如租戶名稱、描述、狀態等</param>
        /// <returns>非同步新增的租戶記錄物件，包含系統生成的 ID 和相關資訊</returns>
        Task<Tenants?> CreateTenantsData(TenantsModel model);

        /// <summary>
        /// 更新租戶資料
        /// </summary>
        /// <remarks>
        /// 此方法更新已存在的租戶記錄，使用提供的模型作為更新的資料來源。
        /// 模型中必須包含要更新的租戶的唯一識別碼(tenant_id)。
        /// 可更新的資料包括租戶的名稱、描述、狀態等屬性，但不更新唯一識別碼。
        /// 更新時會自動紀錄修改時間，以追蹤租戶資料的變化歷程。
        /// </remarks>
        /// <param name="model">包含要更新的租戶資料的模型，需要包含 tenant_id</param>
        /// <returns>非同步更新後的租戶記錄物件，若更新失敗則返回 null</returns>
        Task<Tenants?> UpdateTenantsData(TenantsModel model);

        /// <summary>
        /// 更新租戶狀態
        /// </summary>
        /// <remarks>
        /// 此方法根據指定的租戶 ID 更新租戶的狀態。
        /// 可用於啟用或停用特定租戶的訪問權限，影響租戶在系統中的活動狀態。
        /// 停用狀態的租戶通常無法進行新的操作或訪問新的資源，但原有資料仍然保留。
        /// 更新狀態時會自動紀錄修改時間，以追蹤狀態變化歷程。
        /// </remarks>
        /// <param name="tenant_id">要更新狀態的租戶的唯一識別碼</param>
        /// <returns>非同步布林值，表示狀態更新操作是否成功</returns>
        Task<bool> UpdateTenantsStatus(Guid tenant_id);
    }
}
