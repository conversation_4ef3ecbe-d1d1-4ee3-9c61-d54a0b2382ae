using Paas.Model;

namespace Paas.Dac
{
    /// <summary>
    /// 幣別資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了幣別資料存取層的基本操作，包含查詢、建立、更新和刪除幣別的方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的幣別資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 為系統提供運算和顯示不同幣別金額的基礎支援，尤其在多國際化環境下提供一致的幣別轉換和顯示。
    /// 支援多租戶系統中的幣別維護和管理，包含各幣別的代碼、名稱、符號等基本資訊。
    /// </remarks>
    public interface ICurrenciesDac
    {
        /// <summary>
        /// 取得所有幣別資訊
        /// </summary>
        /// <remarks>
        /// 此方法查詢並返回系統中所有可用的幣別資訊。
        /// 查詢結果包含幣別 ID、幣別代碼、幣別名稱、幣別符號、類型、狀態等詳細資訊。
        /// 通常用於填充下拉式選單或列表，如網站上的幣別選擇、交易設定、賬單系統等。
        /// 可能包含對幣別的適用狀態篩選，像是只回傳啟用中的幣別。
        /// 在多幣別應用中，是提供使用者選擇幣別、轉換貨幣金額的基礎功能。
        /// </remarks>
        /// <returns>非同步幣別資訊集合</returns>
        Task<IList<Currencies>> GetCurrencies();
        /// <summary>
        /// 根據幣別 ID 取得特定幣別資訊
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的幣別 ID 查詢特定幣別的詳細資訊。
        /// 返回包含幣別完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含幣別 ID、幣別代碼、幣別名稱、幣別符號、類型、狀態等詳細資訊。
        /// 常用於編輯幣別情形、相關幣別詳情頁面、或賬單系統中顯示幣別的詳細資訊。
        /// 通常用於隨後的幣別更新或刪除操作前的詳細資訊確認。
        /// </remarks>
        /// <param name="currency_id">要查詢的幣別的唯一識別碼</param>
        /// <returns>非同步幣別記錄物件，如果未找到則返回 null</returns>
        Task<Currencies?> GetCurrency(Guid currency_id);
        /// <summary>
        /// 建立新的幣別
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆幣別記錄，使用提供的模型作為資料來源。
        /// 會自動產生幣別的唯一識別碼和相關的時間戳記（如創建時間）。
        /// 記錄幣別的相關資訊，如幣別代碼（如 USD、EUR等）、幣別名稱、幣別符號（如 $、€等）、幣別類型、狀態等。
        /// 幣別代碼通常遵循 ISO 4217 標準，以確保全球一致的幣別表示。
        /// 在新增幣別時，需要確保幣別代碼的唯一性，避免系統中出現重複的幣別代碼。
        /// </remarks>
        /// <param name="model">包含新幣別資料的模型，如幣別代碼、名稱、符號等</param>
        /// <returns>非同步新增的幣別記錄物件，包含系統生成的 ID 和相關資訊</returns>
        Task<Currencies?> InsertCurrency(Currencies model);
        /// <summary>
        /// 更新特定幣別資訊
        /// </summary>
        /// <remarks>
        /// 此方法更新已存在的幣別記錄，使用提供的模型作為更新的資料來源。
        /// 模型中必須包含要更新的幣別的唯一識別碼(currency_id)。
        /// 可更新的資料包括幣別名稱、幣別符號、狀態等屬性。注意，通常幣別代碼不應該允許更改。
        /// 更新時會自動紀錄修改時間，以追蹤幣別資訊的變化歷程。
        /// 更新幣別狀態（啟用/停用）時用源可能影響到系統中已使用該幣別的交易記錄或計價設定。
        /// </remarks>
        /// <param name="model">包含要更新的幣別資訊的模型，需要包含 currency_id</param>
        /// <returns>非同步更新後的幣別記錄物件，若更新失敗則返回 null</returns>
        Task<Currencies?> UpdateCurrency(Currencies model);
        /// <summary>
        /// 刪除特定幣別
        /// </summary>
        /// <remarks>
        /// 此方法根據指定的幣別 ID 刪除資料庫中的幣別記錄。
        /// 此操作會永久刪除記錄，刪除後無法恢復，應謹慎使用。
        /// 刪除之前應確認此幣別未被已存在的交易、賬單或其他零售相關記錄所使用，以避免資料完整性問題。
        /// 如果幣別已被其他組件所使用，應優先考慮停用該幣別而非直接刪除，即透過更新狀態為停用。
        /// 刪除幣別可能會影響系統中對該幣別的引用，如金額顯示、轉換計算等功能。
        /// </remarks>
        /// <param name="currency_id">要刪除的幣別的唯一識別碼</param>
        /// <returns>非同步布林值，表示刪除操作是否成功</returns>
        Task<bool> DeleteCurrency(Guid currency_id);


    }
}
