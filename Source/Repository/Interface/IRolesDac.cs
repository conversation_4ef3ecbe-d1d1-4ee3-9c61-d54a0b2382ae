using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 角色資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了角色資料存取層的基本操作，包含查詢和新增角色的方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的角色資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 為系統提供角色型授權管理功能的基礎支援，使得可以根據角色委派不同的權限。
    /// 當角色與權限和使用者進行關聯時，就可以實現細精的基於角色的存取控制系統。
    /// </remarks>
    public interface IRolesDac
    {

        /// <summary>
        /// 取得所有角色資料清單
        /// </summary>
        /// <remarks>
        /// 此方法查詢資料庫中的所有角色記錄。
        /// 返回的結果包含所有可用角色的完整列表，包括角色 ID、名稱、描述和相關的元數據。
        /// 通常用於授權管理介面，顯示可選擇的角色列表，或會獲取角色資訊以靜態文檔目錄的形式於介面中顯示。
        /// 此方法通常不需要適用操作權限上的限制，可用於顯示角色選項或關聯標籤。
        /// 在前端應用程式中，這些角色將被用作下拉選單或選項列表，以供使用者選擇或查看。
        /// </remarks>
        /// <returns>非同步角色資料集合，包含所有系統定義的角色</returns>
        Task<IList<Roles>> GetRolesList();

        /// <summary>
        /// 建立新的角色資料
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆角色記錄，使用提供的模型作為資料來源。
        /// 會自動產生角色的唯一識別碼和相關的時間戳記（如創建時間）。
        /// 記錄角色的相關資訊，如角色名稱、描述和任何需要的特殊設定。
        /// 新增角色時，必要的屬性包括角色名稱，並可能需要雙重驗證以確保名稱的唯一性。
        /// 建立角色後，通常需要進行後續操作，如將權限與角色關聯，以實現授權管理功能。
        /// </remarks>
        /// <param name="model">包含新角色資料的模型，如角色名稱、描述等屬性</param>
        /// <returns>非同步新增的角色記錄物件，包含系統生成的 ID 和相關資訊；若建立失敗則返回 null</returns>
        Task<Roles?> CreateRolesData(RolesModel model);
    }
}
