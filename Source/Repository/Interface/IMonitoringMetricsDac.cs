using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 監控指標資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了監控指標資料存取層的基本操作，包含查詢監控指標資料的方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的監控指標資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 整合多租戶環境的監控指標管理功能，可根據租戶適當限制資料範圍。
    /// 為系統提供效能監控、資源使用情況和系統健康狀態的資料存取功能，支援各種監控工具和儀表板的資料需求。
    /// </remarks>
    public interface IMonitoringMetricsDac
    {
        /// <summary>
        /// 取得監控指標資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的監控指標資料。
        /// 可根據模型中的條件（如租戶 ID、指標類型、時間範圍、資源種類等）篩選結果。
        /// 支援複雜的查詢條件組合，可進行多條件的交叉過濾。
        /// 支援分頁和排序功能，節省資源使用並提高查詢效率。
        /// 查詢結果包含指標 ID、租戶 ID、指標名稱、指標類型、指標值、時間戳記等詳細資訊。
        /// 常用於監控儀表板、效能分析報告、系統健康狀態評估等管理功能。
        /// </remarks>
        /// <param name="model">監控指標模型，包含查詢條件如租戶 ID、指標類型、時間範圍等</param>
        /// <returns>非同步監控指標資料集合</returns>
        Task<IList<MetricsData>> GetMetricsDataList(MonitoringMetricsModel model);

        /// <summary>
        /// 根據指標 ID 取得特定監控指標詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的指標 ID 查詢特定監控指標的詳細資訊。
        /// 返回包含監控指標完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含指標 ID、租戶 ID、指標名稱、指標類型、指標值、時間戳記等詳細資訊。
        /// 指標詳細資料可能另包含不包含在列表視圖中的附加詳細資訊，如詳細的技術數據、計算方法、相關資源使用情況等。
        /// 常用於指標詳細分析、異常調查、效能分析或定制報告等情境。
        /// </remarks>
        /// <param name="metric_id">要查詢的監控指標的唯一識別碼</param>
        /// <returns>非同步監控指標記錄物件，如果未找到則返回 null</returns>
        Task<MetricsData?> GetMetricsDataById(Guid metric_id);
    }
}
