using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 使用者資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了使用者資料存取層的基本操作，包含查詢、新增、更新和刪除使用者等方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的使用者資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 整合多租戶環境的使用者管理功能，可根據租戶適當限制資料範圍。
    /// 為系統提供使用者身份驗證、授權和管理功能的基礎支援。
    /// </remarks>
    public interface IUsersDac
    {

        /// <summary>
        /// 取得使用者資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的使用者資料。
        /// 可根據模型中的條件（如使用者名稱、電子郵件、租戶 ID、角色等）篩選結果。
        /// 查詢結果包含使用者 ID、名稱、電子郵件、租戶 ID、角色等詳細資訊。
        /// 支援多租戶環境，可根據租戶 ID 適當限制資料範圍。
        /// 用於管理介面、使用者管理功能和授權檢查功能。
        /// </remarks>
        /// <param name="model">使用者模型，包含查詢條件如使用者名稱、電子郵件、租戶 ID 等</param>
        /// <returns>非同步使用者資料集合</returns>
        Task<IList<Users>> GetUsersList(UsersModel model);

        /// <summary>
        /// 根據使用者 ID 取得詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的使用者 ID 查詢特定使用者的詳細資訊。
        /// 返回包含使用者完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含使用者 ID、名稱、電子郵件、租戶 ID、角色等詳細資訊。
        /// 用於使用者詳細資訊顯示、編輯和管理功能。
        /// </remarks>
        /// <param name="user_id">要查詢的使用者的唯一識別碼</param>
        /// <returns>非同步使用者資料物件，如果未找到則返回 null</returns>
        Task<Users?> GetUsersDataById(Guid user_id);

        /// <summary>
        /// 建立新的使用者
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆使用者記錄，使用提供的模型作為資料來源。
        /// 會自動產生使用者的唯一識別碼和相關的時間戳記（如創建時間）。
        /// 記錄使用者的相關資訊，如使用者名稱、電子郵件、租戶 ID、角色等。
        /// 建立使用者時，必要的屬性包括使用者名稱和電子郵件，可能需要進行重複性檢查。
        /// 新增的使用者需要指定租戶關聯，以支援多租戶環境。
        /// </remarks>
        /// <param name="model">包含新使用者資料的模型，如使用者名稱、電子郵件、租戶 ID、角色等</param>
        /// <returns>非同步新增的使用者記錄物件，包含系統生成的 ID 和相關資訊</returns>
        Task<Users?> CreateUsers(UsersModel model);

        /// <summary>
        /// 更新使用者資料
        /// </summary>
        /// <remarks>
        /// 此方法更新已存在的使用者記錄，使用提供的模型作為更新的資料來源。
        /// 模型中必須包含要更新的使用者的唯一識別碼(user_id)。
        /// 可更新的資料包括使用者名稱、電子郵件、角色等屬性，但通常不更新唯一識別碼和租戶關聯。
        /// 更新時會自動紀錄修改時間，以追蹤使用者資料的變化歷程。
        /// 在更新時可能需要進行權限檢查，確保只有有權限的使用者可以修改特定資料。
        /// </remarks>
        /// <param name="model">包含要更新的使用者資料的模型，需要包含 user_id</param>
        /// <returns>非同步更新後的使用者記錄物件，若更新失敗則返回 null</returns>
        Task<Users?> UpdateUsers(UsersModel model);

        /// <summary>
        /// 刪除使用者資料
        /// </summary>
        /// <remarks>
        /// 此方法根據指定的使用者 ID 刪除資料庫中的使用者記錄。
        /// 此操作會永久刪除使用者資料，刪除後無法恢復，應謹慎使用。
        /// 根據系統設計，可能會執行輔助操作，如刪除相關的使用者權限、角色關聯等。
        /// 在刪除使用者前應考慮系統完整性，確保沒有關鍵的相依關聯會導致問題。
        /// 一般而言，系統可能傳入額外的參數來指定是否執行硬刪除或輔助刪除操作。
        /// </remarks>
        /// <param name="user_id">要刪除的使用者的唯一識別碼</param>
        /// <returns>非同步布林值，表示刪除操作是否成功</returns>
        Task<bool> DeleteUsers(Guid user_id);

        /// <summary>
        /// 根據電子郵件查詢使用者資料
        /// </summary>
        /// <remarks>
        /// 此方法根據電子郵件地址查詢使用者的完整資料。
        /// 主要用於登入流程中的使用者查詢和身分驗證。
        /// 返回的資料包含使用者的基本資訊、密碼雜湊、角色等完整資訊。
        /// 如果找不到對應的使用者，則返回 null。
        /// 此方法應謹慎使用，確保只在必要的身分驗證流程中調用。
        /// </remarks>
        /// <param name="email">要查詢的使用者電子郵件地址</param>
        /// <returns>找到的使用者實體，如果不存在則返回 null</returns>
        Task<Users?> GetUserByEmailAsync(string email);
    }
}
