using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac.Interface
{
    /// <summary>
    /// 資料庫連線控制中心介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了資料庫連線管理的基本操作，抽象了不同資料庫提供者的連線建立方式。
    /// 為資料存取層提供統一的資料庫連線管理介面，使不同資料庫提供者的連線可以透過相同的方式進行存取。
    /// 支援多種資料庫提供者，如 SQL Server、PostgreSQL 等，透過不同實現類別進行操作。
    /// 為封裝資料庫連線建立細節提供了基礎，簡化上層資料存取層的實現。
    /// </remarks>
    public interface IConnectionControlCenter
    {

        /// <summary>
        /// 資料庫提供者名稱
        /// </summary>
        /// <remarks>
        /// 該屬性返回資料庫提供者的名稱，例如 "SqlServer" 或 "PostgreSql"。
        /// 這個名稱用於識別不同的資料庫系統，使得資料存取層可以選擇適當的連線控制中心。
        /// 通常在實現類中固定返回一個字符串常量，用於資料存取層中的提供者匹配。
        /// 由於此值在資料存取層中被廣泛使用，應保持一致性並避免更改。
        /// </remarks>
        string ProviderName { get; }

        /// <summary>
        /// 建立資料庫連線
        /// </summary>
        /// <remarks>
        /// 此方法建立並返回一個新的資料庫連線物件，基於實現類設定的連線字串和資料庫提供者。
        /// 每次調用返回一個新的連線實例，資料存取層可以使用此連線執行所需的資料庫操作。
        /// 返回的連線是 IDbConnection 介面的實現，如 SqlConnection 或 NpgsqlConnection，從而提供統一的資料庫存取介面。
        /// 此連線需要由調用者適當地管理，包括打開、關閉和釋放等操作，建議使用 using 區塊確保適當釋放連線資源。
        /// </remarks>
        /// <returns>一個實作 IDbConnection 介面的資料庫連線物件</returns>
        IDbConnection CreateConnection();
    }
}
