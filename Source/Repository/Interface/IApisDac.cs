using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// API 服務資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了 API 服務資料存取層的基本操作，包含查詢、新增、更新和狀態管理等方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的 API 服務資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 整合多租戶環境的 API 服務管理功能，可根據租戶適當限制資料範圍。
    /// 為系統提供 API 服務的註冊、管理和監控功能的基礎支援。
    /// </remarks>
    public interface IApisDac
    {
        /// <summary>
        /// 取得 API 服務資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的 API 服務資料。
        /// 可根據模型中的條件（如 API ID、名稱、租戶 ID、狀態等）篩選結果。
        /// 查詢結果包含 API 服務 ID、名稱、說明、版本、端點路徑、狀態等詳細資訊。
        /// 支援多租戶環境，可根據租戶 ID 適當限制資料範圍。
        /// 用於顯示可用的 API 服務清單、管理介面和報告等功能。
        /// </remarks>
        /// <param name="model">API 服務模型，包含查詢條件如 API ID、名稱、租戶 ID、狀態等</param>
        /// <returns>非同步 API 服務資料集合</returns>
        Task<IList<Apis>> GetApisDataList(ApisModel model);

        /// <summary>
        /// 建立新的 API 服務
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆 API 服務記錄，使用提供的模型作為資料來源。
        /// 會自動產生 API 服務的唯一識別碼和相關的時間戳記（如創建時間）。
        /// 記錄 API 服務的相關資訊，如名稱、說明、版本、端點路徑、狀態等。
        /// 新增的 API 服務可用於系統中的 API 管理和調用功能。
        /// </remarks>
        /// <param name="model">包含新 API 服務資料的模型，如名稱、說明、版本、端點路徑、狀態等</param>
        /// <returns>非同步新增的 API 服務記錄物件，包含系統生成的 ID 和相關資訊</returns>
        Task<Apis?> CreateApisData(ApisModel model);

        /// <summary>
        /// 更新 API 服務的資料
        /// </summary>
        /// <remarks>
        /// 此方法更新已存在的 API 服務記錄，使用提供的模型作為更新的資料來源。
        /// 模型中必須包含要更新的 API 服務的唯一識別碼(api_id)。
        /// 可更新的資料包括 API 服務的名稱、說明、版本、端點路徑、狀態等屬性。
        /// 更新時會自動紀錄修改時間，以追蹤 API 服務的變化歷程。
        /// </remarks>
        /// <param name="model">包含要更新的 API 服務資料的模型，需要包含 api_id</param>
        /// <returns>非同步更新後的 API 服務記錄物件，若更新失敗則返回 null</returns>
        Task<Apis?> UpdateApisData(ApisModel model);

        /// <summary>
        /// 更新 API 服務狀態
        /// </summary>
        /// <remarks>
        /// 此方法根據指定的租戶 ID 更新相關 API 服務的狀態。
        /// 通常用於批量更新特定租戶的所有 API 服務狀態，例如啟用或停用服務。
        /// 更新狀態時會自動紀錄修改時間，以追蹤狀態變化歷程。
        /// 影響系統中 API 服務的可用性，例如禁用狀態的 API 服務將不再接受調用請求。
        /// </remarks>
        /// <param name="tenant_id">要更新 API 服務狀態的租戶的唯一識別碼</param>
        /// <returns>非同步布林值，表示狀態更新操作是否成功</returns>
        Task<bool> UpdateApisStatus(Guid tenant_id);
    }
}
