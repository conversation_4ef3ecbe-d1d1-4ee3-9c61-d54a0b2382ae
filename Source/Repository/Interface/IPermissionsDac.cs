using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 權限資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了權限資料存取層的基本操作，包含根據角色查詢權限的方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的權限資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 為系統提供基於角色的權限管理功能的基礎支援，確保使用者只能存取其角色所允許的資源。
    /// 與角色管理功能緊密結合，一同構成完整的基於角色的存取控制(RBAC)系統。
    /// </remarks>
    public interface IPermissionsDac
    {
        /// <summary>
        /// 取得特定角色的所有權限列表
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的角色 ID 查詢該角色關聯的所有權限。
        /// 返回的結果包含角色所有關聯的權限記錄，每個權限包含權限 ID、名稱、描述、資源類型和屬性等信息。
        /// 通常用於授權管理介面，顯示特定角色所具有的權限，或用於權限驗證系統中確認使用者是否有權限執行特定操作。
        /// 從安全角度考慮，此方法在驗證使用者權限時非常重要，是確保資源安全存取的關鍵組件。
        /// 當使用者嘗試存取某個資源或執行某個操作時，系統將使用此方法的結果確定使用者是否有權限。
        /// </remarks>
        /// <param name="role_id">要查詢權限的角色的唯一識別碼</param>
        /// <returns>非同步權限集合，包含角色關聯的所有權限</returns>
        Task<IList<Permissions>> GetPermissionsList(Guid role_id);
    }
}
