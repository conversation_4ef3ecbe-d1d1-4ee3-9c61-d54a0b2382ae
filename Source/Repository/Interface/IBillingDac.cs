
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac.Interface
{
    /// <summary>
    /// 帳單資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了帳單資料存取層的基本操作，包含查詢、新增、更新和刪除帳單資料的方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 整合多租戶環境的帳單管理功能，可根據租戶適當限制資料範圍。
    /// </remarks>
    public interface IBillingDac
    {
        /// <summary>
        /// 取得帳單資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的帳單資料。
        /// 可根據模型中的租戶 ID、狀態等條件篩選結果。
        /// 查詢結果包含帳單 ID、租戶 ID、金額、幣別、付款方式、狀態、到期日期和付款日期等詳細資訊。
        /// 支援多租戶環境，可根據租戶 ID 適當限制資料範圍。
        /// </remarks>
        /// <param name="model">帳單模型，包含查詢條件如租戶 ID、狀態等</param>
        /// <returns>非同步符合條件的帳單記錄集合</returns>
        Task<IList<BillingRecords>> GetBillingDataList(BillingModel model);

        /// <summary>
        /// 根據帳單 ID 取得特定帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的帳單 ID 查詢特定帳單的詳細資訊。
        /// 返回包含帳單完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含帳單 ID、租戶 ID、金額、幣別、付款方式、狀態、到期日期和付款日期等詳細資料。
        /// </remarks>
        /// <param name="billing_id">要查詢的帳單的唯一識別碼</param>
        /// <returns>非同步帳單記錄物件，如果未找到則返回 null</returns>
        Task<BillingRecords?> GetBillingDataById(Guid billing_id);

        /// <summary>
        /// 建立新的帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆帳單記錄，使用提供的模型作為資料來源。
        /// 會自動產生帳單的唯一識別碼和相關的時間戳記（如創建時間）。
        /// 返回新建立的帳單記錄物件，包含完整的帳單資料和系統生成的 ID。
        /// </remarks>
        /// <param name="model">包含新帳單資料的模型，如租戶 ID、金額、幣別、付款方式、狀態和到期日</param>
        /// <returns>非同步新增的帳單記錄物件，包含系統生成的 ID 和相關資訊</returns>
        Task<BillingRecords?> CreateBillingData(BillingModel model);

        /// <summary>
        /// 更新帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法更新已存在的帳單記錄，使用提供的模型作為更新的資料來源。
        /// 模型中必須包含要更新的帳單的唯一識別碼(record_id)。
        /// 只更新模型中提供的非空欄位，空值欄位保持原始資料不變。
        /// 返回更新後的帳單記錄物件，包含最新的帳單資料。
        /// </remarks>
        /// <param name="model">包含要更新的帳單資料的模型，需要包含 record_id</param>
        /// <returns>非同步更新後的帳單記錄物件，若更新失敗則返回 null</returns>
        Task<BillingRecords?> UpdateBillingData(BillingModel model);

        /// <summary>
        /// 刪除特定帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法根據指定的帳單 ID 刪除資料庫中的帳單記錄。
        /// 此操作會永久刪除記錄，刪除後無法恢復，應謹慎使用。
        /// 如果指定的帳單 ID 不存在，將返回 false。
        /// </remarks>
        /// <param name="billing_id">要刪除的帳單的唯一識別碼</param>
        /// <returns>非同步布林值，表示刪除操作是否成功</returns>
        Task<bool> DeleteBillingData(Guid billing_id);
    }
}
