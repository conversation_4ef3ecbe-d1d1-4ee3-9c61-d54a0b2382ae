using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac.Interface
{
    /// <summary>
    /// API 使用紀錄資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了 API 使用紀錄資料存取層的基本操作，包含查詢和建立 API 使用紀錄的方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的 API 使用紀錄資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 整合多租戶環境的 API 使用紀錄功能，用於追蹤和分析各租戶的 API 使用情況。
    /// 為系統提供監控、計費和分析 API 使用情況的基礎資料。
    /// </remarks>
    public interface IApiUsageDac
    {

        /// <summary>
        /// 取得所有 API 使用紀錄清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的 API 使用紀錄。
        /// 可根據模型中的租戶 ID、API ID、時間範圍等條件篩選結果。
        /// 查詢結果包含使用紀錄 ID、租戶 ID、API ID、調用時間、調用狀態、響應時間等詳細資訊。
        /// 支援多租戶環境，可根據租戶 ID 適當限制資料範圍。
        /// 用於監控、分析和計費目的，可提供 API 使用詳細報告。
        /// </remarks>
        /// <param name="model">API 使用紀錄模型，包含查詢條件如租戶 ID、API ID、時間範圍等</param>
        /// <returns>非同步 API 使用紀錄集合</returns>
        Task<IList<ApiUsage>> GetApiUsageDataList(ApiUsageModel model);

        /// <summary>
        /// 取得特定 API 使用紀錄
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的使用紀錄 ID 查詢特定 API 使用紀錄的詳細資訊。
        /// 返回包含 API 使用紀錄完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含使用紀錄 ID、租戶 ID、API ID、調用時間、調用狀態、響應時間等詳細資訊。
        /// 用於實現特定 API 調用的詳細分析和問題追蹤。
        /// </remarks>
        /// <param name="usage_id">要查詢的 API 使用紀錄的唯一識別碼</param>
        /// <returns>非同步 API 使用紀錄物件，如果未找到則返回 null</returns>
        Task<ApiUsage?> GetApiUsageData(Guid usage_id);

        /// <summary>
        /// 建立新的 API 使用紀錄
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆 API 使用紀錄，使用提供的模型作為資料來源。
        /// 會自動產生使用紀錄的唯一識別碼和相關的時間戳記（如調用時間）。
        /// 紀錄 API 調用的相關資訊，如租戶 ID、API ID、調用狀態、響應時間等。
        /// 用於監控、分析和計費目的，為 API 使用情況提供完整的追蹤資料。
        /// </remarks>
        /// <param name="model">包含新 API 使用紀錄資料的模型，如租戶 ID、API ID、調用狀態、響應時間等</param>
        /// <returns>非同步新增的 API 使用紀錄物件，包含系統生成的 ID 和相關資訊</returns>
        Task<ApiUsage?> CreateApiUsageData(ApiUsageModel model);
    }
}
