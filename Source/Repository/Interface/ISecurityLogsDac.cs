using Paas.Model;

namespace Paas.Dac
{
    /// <summary>
    /// 安全性日誌資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了安全性日誌資料存取層的基本操作，包含查詢安全性日誌的方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的安全性日誌資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 整合多租戶環境的安全性日誌管理功能，可根據租戶適當限制資料範圍。
    /// 為系統提供安全事件記錄和審計功能的基礎支援，相關日誌可用於安全分析、合規驗證和問題追蹤。
    /// </remarks>
    public interface ISecurityLogsDac
    {
        /// <summary>
        /// 取得安全性日誌資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的安全性日誌資料。
        /// 可根據模型中的條件（如租戶 ID、事件類型、事件時間範圍、使用者 ID 等）篩選結果。
        /// 支援複雜的查詢條件組合，可進行多條件的交叉過濾。
        /// 支援分頁功能，可指定頁碼和每頁筆數，節省資源使用並提高查詢效率。
        /// 查詢結果包含日誌 ID、租戶 ID、事件時間、事件類型、事件詳情、相關 IP 位址等詳細資訊。
        /// 日誌事件類型可能包括登入嘗試、密碼重設、權限變更、資料存取等安全相關操作。
        /// </remarks>
        /// <param name="model">安全性日誌模型，包含查詢條件如租戶 ID、事件類型、時間範圍等</param>
        /// <returns>非同步安全性日誌資料集合</returns>
        Task<IList<SecurityLogs>> GetSecurityLogs(SecurityLogs model);
        /// <summary>
        /// 根據日誌 ID 取得特定安全性日誌詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的日誌 ID 查詢特定安全性日誌的詳細資訊。
        /// 返回包含安全性日誌完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含日誌 ID、租戶 ID、事件時間、事件類型、事件詳情、相關 IP 位址等詳細資訊。
        /// 日誌詳細資料可能另包含不包含在列表視圖中的附加詳細資訊，如詳細的資源存取資訊、操作環境資訊等。
        /// 用於安全事件分析、安全稽核或安全專案調查等安全管理功能。
        /// </remarks>
        /// <param name="logId">要查詢的安全性日誌的唯一識別碼</param>
        /// <returns>非同步安全性日誌記錄物件，如果未找到則返回 null</returns>
        Task<SecurityLogs?> GetSecurityLog(Guid logId);
    }
}
