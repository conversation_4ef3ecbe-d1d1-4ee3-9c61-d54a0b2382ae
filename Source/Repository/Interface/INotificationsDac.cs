using Paas.Model;

namespace Paas.Dac
{
    /// <summary>
    /// 通知資料存取層介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了通知資料存取層的基本操作，包含查詢和建立通知的方法。
    /// 是資料存取層和服務層之間的契約，確保定義清晰的通知資料存取方法。
    /// 所有方法都返回 Task 物件，支援非同步操作，提高應用程式性能和操作性。
    /// 整合多租戶環境的通知管理功能，可根據租戶和使用者適當限制資料範圍。
    /// 為系統提供使用者通知、提醒和警報功能的基礎支援，增強系統的現時性和互動性。
    /// </remarks>
    public interface INotificationsDac
    {
        /// <summary>
        /// 取得通知資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的通知資料。
        /// 可根據模型中的條件（如租戶 ID、使用者 ID、通知狀態、時間範圍等）篩選結果。
        /// 查詢結果包含通知 ID、通知標題、內容、類型、狀態、創建時間等詳細資訊。
        /// 支援多租戶環境，可根據租戶 ID 和使用者 ID 適當限制資料範圍。
        /// 於前端使用者介面中顯示使用者的通知中心或通知列表。
        /// </remarks>
        /// <param name="model">通知模型，包含查詢條件如租戶 ID、使用者 ID、通知狀態等</param>
        /// <returns>非同步通知資料集合</returns>
        Task<IList<Notifications>> GetNotifications(Notifications model);
        /// <summary>
        /// 根據通知 ID 取得特定通知詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的通知 ID 查詢特定通知的詳細資訊。
        /// 返回包含通知完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含通知 ID、通知標題、內容、類型、狀態、創建時間等詳細資訊。
        /// 用於顯示通知的完整內容，如當使用者點擊通知以查看詳細資訊時。
        /// 這通常會觸發通知的讀取狀態更新，標記通知已被查看。
        /// </remarks>
        /// <param name="notification_id">要查詢的通知的唯一識別碼</param>
        /// <returns>非同步通知記錄物件，如果未找到則返回 null</returns>
        Task<Notifications?> GetNotification(Guid notification_id);
        /// <summary>
        /// 建立新的通知
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆通知記錄，使用提供的模型作為資料來源。
        /// 會自動產生通知的唯一識別碼和相關的時間戳記（如創建時間）。
        /// 記錄通知的相關資訊，如通知標題、內容、類型、狀態等。
        /// 新增的通知初始狀態通常為“未讀”，使用者查看後可更新為“已讀”。
        /// 可設定通知的目標受眾，如特定使用者、特定角色的使用者或特定租戶的所有使用者。
        /// </remarks>
        /// <param name="model">包含新通知資料的模型，如通知標題、內容、類型、狀態等</param>
        /// <returns>非同步新增的通知記錄物件，包含系統生成的 ID 和相關資訊</returns>
        Task<Notifications?> InsertNotifications(Notifications model);
    }
}
