using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 監控指標資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IMonitoringMetricsDac 介面，提供系統監控指標的資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的監控指標數據，包含查詢監控指標資料的功能。
    /// 支援多租戶環境，可根據租戶 ID 和 API ID 適當限制資料範圍。
    /// 為系統提供監控和效能跟蹤功能，支援各種指標的時間序列分析和儀表板呈現。
    /// </remarks>
    public class MonitoringMetricsDac : _Dac,IMonitoringMetricsDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public MonitoringMetricsDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得監控指標資料清單
        /// </summary>
        /// <param name="model">查詢條件模型，包含租戶 ID、API ID、指標類型 ID、時間範圍和分頁參數等</param>
        /// <returns>非同步監控指標資料集合</returns>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的監控指標資料。
        /// 可根據模型中的條件（如租戶 ID、API ID、指標類型 ID、時間範圍等）篩選結果。
        /// 支援時間範圍過濾，可指定開始時間和結束時間進行篩選。
        /// 查詢結果按時間戳記降序排序，顯示最新的指標數據先。
        /// 返回的資料包含數據 ID、指標類型 ID、租戶 ID、API ID、指標值、標籤和時間戳記等詳細資訊。
        /// 標籤 (tags) 欄位可存儲一組附加的元數據，供更詳細的分類和分析用途。
        /// 支援分頁功能，使用 limit 和 offset 參數控制返回的記錄數量。
        /// 常用於監控儀表板、效能分析報告、系統健康狀態評估等管理功能。
        /// </remarks>
        public async Task<IList<MetricsData>> GetMetricsDataList(MonitoringMetricsModel model)
        {
            string strSql = @"SELECT data_id,
                                     metric_type_id,
                                     tenant_id,
                                     api_id,
                                     value,
                                     tags,
                                     timestamp
                                FROM metrics_data
                               WHERE (@tenant_id IS NULL OR tenant_id = @tenant_id)
                                 AND (@api_id IS NULL OR api_id = @api_id)
                                 AND (@metric_type_id IS NULL OR metric_type_id = @metric_type_id)
                                 AND (@start_time IS NULL OR timestamp >= @start_time) 
                                 AND (@end_time IS NULL OR timestamp <= @end_time)
                            ORDER BY timestamp DESC
                               LIMIT @limit OFFSET @offset;";

            return await ExecuteQueryAsync<MetricsData>(strSql, model,true);
        }

        /// <summary>
        /// 根據指標 ID 取得特定監控指標詳細資料
        /// </summary>
        /// <param name="metric_id">要查詢的監控指標的唯一識別碼</param>
        /// <returns>非同步監控指標記錄物件，如果未找到則返回 null</returns>
        /// <remarks>
        /// 此方法根據提供的指標 ID 查詢特定監控指標的詳細資訊。
        /// 返回包含監控指標完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含數據 ID、指標類型 ID、租戶 ID、API ID、指標值、標籤和時間戳記等詳細資訊。
        /// 常用於指標詳細分析、異常調查、效能分析或定制報告等情境。
        /// 不同的指標類型可能有不同的屬性和解釋方式，可以根據 metric_type_id 進行相應的處理。
        /// </remarks>
        public async Task<MetricsData?> GetMetricsDataById(Guid metric_id)
        {
            string strSql = @"SELECT data_id,
                                     metric_type_id,
                                     tenant_id,
                                     api_id,
                                     value,
                                     tags,
                                     timestamp
                                FROM metrics_data
                               WHERE data_id = @dataId";

            return (await ExecuteQueryAsync<MetricsData>(strSql, new { dataId = metric_id }, true)).FirstOrDefault();
        }
    }
}
