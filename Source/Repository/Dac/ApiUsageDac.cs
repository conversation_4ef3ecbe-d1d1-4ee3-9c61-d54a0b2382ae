using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// API 使用記錄資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IApiUsageDac 介面，提供 API 使用記錄的資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責用於記錄和追蹤 API 請求的統計和使用數據，包括響應時間、狀態碼、IP 位址等資訊。
    /// 提供納查詢、新增 API 使用記錄的功能，支援平台的 API 效能監控和計費功能。
    /// </remarks>
    public class ApiUsageDac : _Dac, IApiUsageDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public ApiUsageDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得 API 使用紀錄清單
        /// </summary>
        /// <param name="model">查詢條件模型，包含 API ID、時間範圍、分頁參數等</param>
        /// <returns>非同步 API 使用紀錄集合</returns>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的 API 使用紀錄。
        /// 可根據模型中的條件（如 API ID、時間範圍等）篩選結果。
        /// 支援時間範圍過濾，可指定開始時間和結束時間進行篩選。
        /// 返回的資料包含使用記錄 ID、API ID、金鑰 ID、請求 IP、響應時間、狀態碼、創建時間等詳細資訊。
        /// 結果按使用記錄 ID 排序，並支援分頁功能（limit 和 offset）以優化性能。
        /// 常用於 API 使用統計、監控儀表板和使用量分析等場景。
        /// </remarks>
        public async Task<IList<ApiUsage>> GetApiUsageDataList(ApiUsageModel model)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(@"SELECT usage_id,
                                   api_id,
                                   key_id,
                                   request_ip::TEXT AS request_ip,
                                   response_time,
                                   status_code,
                                   created_at
                              FROM api_usage
                             WHERE (@api_id IS NULL OR api_id = @api_id)");

            // 設定搜尋條件
            if (model.start_time != null && model.end_time != null)
            {
                strSql.Append("AND created_at BETWEEN @start_time AND @end_time ");
            }

            strSql.Append(@" ORDER BY usage_id
                                LIMIT @limit OFFSET @offset;");

            return await ExecuteQueryAsync<ApiUsage>(strSql.ToString(), model);
        }

        /// <summary>
        /// 根據 ID 取得特定 API 使用紀錄
        /// </summary>
        /// <param name="usageId">要查詢的 API 使用紀錄的唯一識別碼</param>
        /// <returns>非同步 API 使用紀錄物件，如果未找到則返回 null</returns>
        /// <remarks>
        /// 此方法根據提供的使用紀錄 ID 查詢特定的 API 使用紀錄詳細資訊。
        /// 返回包含使用記錄 ID、API ID、金鑰 ID、請求 IP、響應時間、狀態碼、創建時間等詳細資訊。
        /// 如果指定的 ID 不存在，則返回 null。
        /// 用於查詢特定 API 請求的詳細資訊，如效能分析、問題調試或客戶支援等場景。
        /// </remarks>
        public async Task<ApiUsage?> GetApiUsageData(Guid usageId)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(@"SELECT usage_id,
                                   api_id,
                                   key_id,
                                   request_ip::TEXT AS request_ip,
                                   response_time,
                                   status_code,
                                   created_at
                              FROM api_usage
                             WHERE usage_id = @usage_id");

            return (await ExecuteQueryAsync<ApiUsage>(strSql.ToString(), new { usage_id = usageId })).FirstOrDefault();
        }

        /// <summary>
        /// 建立新的 API 使用紀錄
        /// </summary>
        /// <param name="model">包含新 API 使用紀錄資料的模型，如 API ID、金鑰 ID、請求 IP、響應時間、狀態碼等</param>
        /// <returns>非同步新增的 API 使用紀錄物件，包含系統生成的 ID 和相關資訊</returns>
        /// <remarks>
        /// 此方法在資料庫中新增一筆 API 使用紀錄，使用提供的模型作為資料來源。
        /// 會自動記錄創建時間，以追蹤 API 使用的時間。
        /// 記錄 API 使用的相關資訊，如 API ID、金鑰 ID、請求 IP、響應時間和狀態碼等。
        /// 請求 IP 會被轉換為 INET 類型存儲，並在存儲前進行去除空格處理。
        /// 新增完成後返回完整的記錄，包含系統生成的使用記錄 ID 和創建時間。
        /// 通常用於 API 請求日誌記錄、API 使用統計和計費等功能。
        /// </remarks>
        public async Task<ApiUsage?> CreateApiUsageData(ApiUsageModel model)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(@"INSERT INTO api_usage (
	                                api_id,
	                                key_id,
	                                request_ip,
	                                response_time,
                                    status_code
	                               )
                            VALUES (
		                            @api_id,
	                                @key_id,
                                    TRIM(@request_ip)::INET,
	                                @response_time,
                                    @status_code)
                         RETURNING usage_id,
                                   api_id,
                                   key_id,
                                   request_ip::TEXT AS request_ip,
                                   response_time,
                                   status_code,
                                   created_at;");


            return (await ExecuteQueryAsync<ApiUsage>(strSql.ToString(), model)).FirstOrDefault();
        }


    }
}
