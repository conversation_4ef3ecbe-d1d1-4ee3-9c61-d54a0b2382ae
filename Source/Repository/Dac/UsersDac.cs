using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 使用者資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IUsersDac 介面，提供使用者資料的完整資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的使用者，包含創建、查詢、更新和刪除使用者的功能。
    /// 支援多租戶環境，可根據租戶 ID 和角色 ID 適當限制資料範圍。
    /// 確保使用者資訊的安全存取和管理，包含密碼隱藏等安全考量。
    /// </remarks>
    public class UsersDac : _Dac, IUsersDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public UsersDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得使用者資料清單
        /// </summary>
        /// <param name="model">查詢條件模型，包含租戶 ID、角色 ID 和分頁參數等</param>
        /// <returns>非同步使用者資料集合</returns>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的使用者資料。
        /// 可根據模型中的條件（如租戶 ID、角色 ID 等）篩選結果。
        /// 查詢結果按創建時間降序排序，顯示最新創建的使用者先。
        /// 返回的資料包含使用者 ID、租戶 ID、使用者名稱、電子郵件、角色 ID、創建時間和更新時間等基本資訊。
        /// 支援分頁功能，使用 limit 和 offset 參數控制返回的記錄數量。
        /// 常用於系統管理介面中的使用者管理頁面、使用者列表或報表等功能。
        /// 注意，返回的資料不包含密碼相關資訊，以確保安全性。
        /// </remarks>
        public async Task<IList<Users>> GetUsersList(UsersModel model)
        {
            string strSql = @"SELECT user_id,
                                     tenant_id,
                                     username,
                                     email,
                                     role_id,
                                     created_at,
                                     updated_at
                                FROM users
                               WHERE (@tenant_id IS NULL OR tenant_id = @tenant_id)
                                 AND (@role_id IS NULL OR role_id = @role_id)
                            ORDER BY created_at DESC
                               LIMIT @limit OFFSET @offset";

            return await ExecuteQueryAsync<Users>(strSql, model);
        }

        /// <summary>
        /// 根據使用者 ID 取得特定使用者詳細資料
        /// </summary>
        /// <param name="user_id">要查詢的使用者的唯一識別碼</param>
        /// <returns>非同步使用者記錄物件，如果未找到則返回 null</returns>
        /// <remarks>
        /// 此方法根據提供的使用者 ID 查詢特定使用者的詳細資訊。
        /// 返回包含使用者完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含使用者 ID、租戶 ID、使用者名稱、電子郵件、角色 ID、創建時間和更新時間等基本資訊。
        /// 返回的資料不包含密碼相關資訊，以確保安全性。
        /// 常用於使用者詳情頁面、使用者編輯功能或在驗證使用者身份時取得使用者資訊。
        /// </remarks>
        public async Task<Users?> GetUsersDataById(Guid user_id)
        {
            string strSql = @"SELECT user_id,
                                     tenant_id,
                                     username,
                                     email,
                                     role_id,
                                     created_at,
                                     updated_at
                                FROM users
                               WHERE user_id = @userId";

            return (await ExecuteQueryAsync<Users>(strSql, new { userId = user_id })).FirstOrDefault();
        }

        /// <summary>
        /// 建立新的使用者
        /// </summary>
        /// <param name="model">包含新使用者資料的模型，如租戶 ID、使用者名稱、電子郵件、密碼隱藏和角色 ID 等</param>
        /// <returns>非同步新增的使用者記錄物件，包含系統生成的 ID 和相關資訊</returns>
        /// <remarks>
        /// 此方法在資料庫中新增一筆使用者記錄，使用提供的模型作為資料來源。
        /// 記錄使用者的相關資訊，包括租戶 ID、使用者名稱、電子郵件、密碼隱藏和角色 ID 等。
        /// 密碼以隱藏形式存儲，以確保安全性，不會在返回實體中進行暴露。
        /// 會自動記錄創建時間和更新時間。
        /// 新增完成後返回完整的記錄，包含系統生成的使用者 ID 和基本資訊，但不包含密碼相關資訊。
        /// 通常用於系統管理中的使用者創建功能、租戶使用者管理或註冊流程等。
        /// </remarks>
        public async Task<Users?> CreateUsers(UsersModel model)
        {
            string strSql = @"INSERT INTO users (
	                                     tenant_id,
	                                     username,
	                                     email,
	                                     password_hash,
	                                     role_id
	                                     )
                                   VALUES(
		                                 @tenant_id,
		                                 @username,
		                                 @email,
		                                 @password_hash,
		                                 @role_id
		                                 ) 
                                RETURNING 
                                        user_id,
	                                    tenant_id,
	                                    username,
	                                    email,
	                                    role_id,
	                                    created_at,
	                                    updated_at";

            return (await ExecuteQueryAsync<Users>(strSql, model)).FirstOrDefault();
        }

        /// <summary>
        /// 更新使用者資料
        /// </summary>
        /// <param name="model">包含要更新的使用者資料的模型，需要包含 user_id</param>
        /// <returns>非同步更新後的使用者記錄物件，若更新失敗則返回 null</returns>
        /// <remarks>
        /// 此方法更新已存在的使用者記錄，使用提供的模型作為更新的資料來源。
        /// 只更新模型中提供的非空屬性，使用 COALESCE 函數確保只更新指定的欄位。
        /// 可更新的資料包括電子郵件和角色 ID 等屬性，但不允許更新使用者名稱和租戶 ID。
        /// 此方法不包含密碼更新功能，應由專門的密碼更新方法處理，以確保安全性。
        /// 更新時會自動更新 updated_at 欄位，使用 CURRENT_TIMESTAMP 記錄使用者資訊的最後修改時間。
        /// 更新完成後返回更新後的完整記錄，包含使用者的基本資訊，但不包含密碼相關資訊。
        /// 更新使用者角色時應考慮權限安全影響，確保適當的權限控制和審計追蹤。
        /// </remarks>
        public async Task<Users?> UpdateUsers(UsersModel model)
        {
            string strSql = @"UPDATE users
                                 SET email = COALESCE(@email, email),
                                     role_id = COALESCE(@role_id, role_id),
                                     updated_at = CURRENT_TIMESTAMP
                               WHERE user_id = @user_id
                           RETURNING 
                                     user_id,
	                                 tenant_id,
	                                 username,
	                                 email,
	                                 role_id,
	                                 created_at,
	                                 updated_at";

            return (await ExecuteQueryAsync<Users>(strSql, model)).FirstOrDefault();
        }

        /// <summary>
        /// 刪除使用者資料
        /// </summary>
        /// <param name="user_id">要刪除的使用者的唯一識別碼</param>
        /// <returns>非同步布林值，表示刪除操作是否成功</returns>
        /// <remarks>
        /// 此方法根據指定的使用者 ID 從資料庫中刪除使用者記錄。
        /// 此操作會永久刪除記錄，刪除後無法恢復，應謹慎使用。
        /// 刪除使用者前應考慮以下影響：
        /// - 租戶中的管理員權限和責任在刪除後可能需要重新指派
        /// - 該使用者操作的歷史記錄可能需要維護
        /// - 刪除後相關的驗證令牌、權限和存取記錄均應適當處理
        /// 如果只是暂時停用使用者存取，建議使用停用功能而非直接刪除。
        /// 刪除操作應給予全面的權限控制，確保只有適當的系統管理員可以執行此操作。
        /// </remarks>
        public async Task<bool> DeleteUsers(Guid user_id)
        {
            string strSql = @"DELETE FROM users
                               WHERE user_id = @userId;";

            return await ExecuteCommandAsync(strSql, new { userId = user_id });
        }
    }
}
