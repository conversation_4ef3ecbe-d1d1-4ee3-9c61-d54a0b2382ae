using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// API 服務資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IApisDac 介面，提供 API 服務的完整資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的 API 服務，包含查詢、創建、更新和管理 API 狀態的功能。
    /// 支援多租戶環境，可根據租戶 ID 適當限制資料範圍。
    /// 提供 API 服務資料的完整生命週期管理，包含限制率、狀態和版本管理等功能。
    /// </remarks>
    public class ApisDac: _Dac, IApisDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public ApisDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得 API 服務資料清單
        /// </summary>
        /// <param name="model">查詢條件模型，包含租戶 ID、API ID 等查詢條件</param>
        /// <returns>非同步 API 服務資料集合</returns>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的 API 服務資料。
        /// 可根據模型中的條件（如租戶 ID、API ID 等）篩選結果。
        /// 使用 StringBuilder 動態建立 SQL 查詢，根據條件加入不同的篩選條件。
        /// 查詢結果按 API ID 排序。
        /// 返回的資料包含 API ID、租戶 ID、名稱、描述、版本、每分鐘限制率、活動狀態、創建時間和更新時間等基本資訊。
        /// 常用於 API 管理介面、系統監控或報表等功能，顯示可用的 API 服務清單。
        /// 在多租戶環境中，可根據租戶 ID 適當限制可見的 API 服務範圍。
        /// </remarks>
        public async Task<IList<Apis>> GetApisDataList(ApisModel model)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(@"SELECT api_id,
                                   tenant_id,
                                   name,
                                   description,
                                   version,
                                   rate_limit_per_minute,
                                   is_active,
                                   created_at,
                                   updated_at
                            FROM apis
                            WHERE 1 = 1");

            // 設定搜尋條件
            if (model.tenant_id.HasValue && model.tenant_id != Guid.Empty)
            {
                strSql.Append(" AND tenant_id = @tenant_id");
            }
            else
            {
                if (model.api_id.HasValue && model.api_id != Guid.Empty)
                {
                    strSql.Append(" AND api_id = @api_id");
                }
            }

            strSql.Append(" ORDER BY api_id ");

            return await ExecuteQueryAsync<Apis>(strSql.ToString(), model);
        }

        /// <summary>
        /// 建立新的 API 服務
        /// </summary>
        /// <param name="model">包含新 API 服務資料的模型，如租戶 ID、名稱、描述和每分鐘限制率等</param>
        /// <returns>非同步新增的 API 服務記錄物件，包含系統生成的 ID 和相關資訊</returns>
        /// <remarks>
        /// 此方法在資料庫中新增一筆 API 服務記錄，使用提供的模型作為資料來源。
        /// 記錄 API 服務的相關資訊，包括租戶 ID、名稱、描述和每分鐘限制率等。
        /// 會自動設定預設的版本和活動狀態，並記錄創建時間和更新時間。
        /// 新增完成後返回完整的記錄，包含系統生成的 API ID 和基本資訊。
        /// 通常用於系統管理中的 API 服務創建功能，或在新增租戶和服務時自動配置必要的 API 服務。
        /// 在建立 API 服務後，通常需要設定租戶對應的 API 金鑰和存取權限。
        /// </remarks>
        public async Task<Apis?> CreateApisData(ApisModel model)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(@"INSERT INTO apis (
	                                tenant_id,
                                    name,
                                    description,
                                    rate_limit_per_minute
	                               )
                            VALUES (
		                            @tenant_id,
		                            @name,
		                            @description,
		                            @rate_limit_per_minute)
                            RETURNING api_id,
                                      tenant_id,
                                      name,
                                      description,
                                      version,
                                      rate_limit_per_minute,
                                      is_active,
                                      created_at,
                                      updated_at;");

            return (await ExecuteQueryAsync<Apis>(strSql.ToString(), model))?.FirstOrDefault();
        }

        /// <summary>
        /// 更新 API 服務資料
        /// </summary>
        /// <param name="model">包含要更新的 API 服務資料的模型，需要包含 api_id</param>
        /// <returns>非同步更新後的 API 服務記錄物件，若更新失敗則返回 null</returns>
        /// <remarks>
        /// 此方法更新已存在的 API 服務記錄，使用提供的模型作為更新的資料來源。
        /// 只更新模型中提供的非空屬性，使用 COALESCE 函數確保只更新指定的欄位。
        /// 可更新的資料包括版本、每分鐘限制率和活動狀態等屬性，但不允許更新租戶 ID、名稱和描述。
        /// 更新時會自動更新 updated_at 欄位，記錄 API 服務資訊的最後修改時間。
        /// 使用 StringBuilder 動態建立 SQL 更新語句，根據是否提供活動狀態加入不同的更新條件。
        /// 更新完成後返回更新後的完整記錄，包含 API 服務的基本資訊。
        /// 更新 API 服務資料時應考慮影響，特別是限制率和狀態變更可能對已在使用該 API 的客戶端造成影響。
        /// </remarks>
        public async Task<Apis?> UpdateApisData(ApisModel model)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append($@"UPDATE apis
                               SET updated_at = {Now},
                                   version = COALESCE(@version, version),
                                   rate_limit_per_minute = COALESCE(@rate_limit_per_minute, rate_limit_per_minute) ");

            if (model.is_active != null)
            {
                strSql.Append(" , is_active = @is_active ");
            }

            strSql.Append(@" WHERE api_id  = @api_id
                         RETURNING api_id,
                                   tenant_id,
                                   name,
                                   description,
                                   version,
                                   rate_limit_per_minute,
                                   is_active,
                                   created_at,
                                   updated_at; ");

            return (await ExecuteQueryAsync<Apis>(strSql.ToString(), model)).FirstOrDefault();
        }

        /// <summary>
        /// 停用 API 服務
        /// </summary>
        /// <param name="api_id">要停用的 API 服務的唯一識別碼</param>
        /// <returns>非同步布林值，表示停用操作是否成功</returns>
        /// <remarks>
        /// 此方法會停用指定的 API 服務，將其活動狀態設為 false。
        /// 停用後的 API 服務將不再接受新的請求，但記錄仍然存在於資料庫中。
        /// 更新時會自動更新 updated_at 欄位，記錄狀態變更的時間。
        /// 此操作可用於暂時或永久停用特定的 API 服務，例如當客戶端違反使用條款、API 需要維護或停用距期功能。
        /// 停用 API 服務後，可能需要對使用該 API 的客戶端進行通知，以避免不必要的請求和錯誤。
        /// 注意，這個方法只是停用 API，而不是刪除它，如果需要再次啟用 API，可以使用 UpdateApisData 方法將 is_active 設為 true。
        /// </remarks>
        public async Task<bool> UpdateApisStatus(Guid api_id)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append($@"UPDATE apis
                            SET updated_at = {Now} , 
                                   is_active = false
                            WHERE api_id = @api_id ");

            return await ExecuteCommandAsync(strSql.ToString(), new { api_id });
        }
    }
}
