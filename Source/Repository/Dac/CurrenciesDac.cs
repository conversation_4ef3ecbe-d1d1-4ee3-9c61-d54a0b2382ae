using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Text;

namespace Paas.Dac
{
    /// <summary>
    /// 幣別資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 ICurrenciesDac 介面，提供幣別的完整資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的幣別，包含創建、查詢、更新和刪除幣別的功能。
    /// 為系統提供多幣別支援的基礎，包括幣別代碼、符號、匯率等資訊的維護。
    /// 支援幣別狀態管理，可啟用或停用特定幣別，以控制其在系統中的可見性和使用性。
    /// </remarks>
    public class CurrenciesDac : _Dac, ICurrenciesDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public CurrenciesDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得所有幣別資訊
        /// </summary>
        /// <returns>非同步幣別資訊集合</returns>
        /// <remarks>
        /// 此方法查詢並返回系統中所有可用的幣別資訊。
        /// 查詢結果按創建時間降序排序，顯示最新創建的幣別先。
        /// 返回的資料包含幣別 ID、幣別代碼、幣別符號、匯率、啟用狀態、更新時間和創建時間等詳細資訊。
        /// 常用於填充下拉式選單或列表，如網站上的幣別選擇、交易設定、賬單系統等。
        /// 查詢結果包含所有幣別，無論其啟用狀態，客戶端可根據 is_active 欄位進行適當的過濾。
        /// </remarks>
        public async Task<IList<Currencies>> GetCurrencies()
        {
            string strSql = @"SELECT 
                                currency_id, 
                                code, 
                                symbol, 
                                exchange_rate, 
                                is_active, 
                                updated_at, 
                                created_at
                              FROM currencies
                              ORDER BY created_at DESC";

            return await ExecuteQueryAsync<Currencies>(strSql);
        }

        /// <summary>
        /// 根據幣別 ID 取得特定幣別詳細資料
        /// </summary>
        /// <param name="currency_id">要查詢的幣別的唯一識別碼</param>
        /// <returns>非同步幣別記錄物件，如果未找到則返回 null</returns>
        /// <remarks>
        /// 此方法根據提供的幣別 ID 查詢特定幣別的詳細資訊。
        /// 返回包含幣別完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含幣別 ID、幣別代碼、幣別符號、匯率、啟用狀態、更新時間和創建時間等詳細資訊。
        /// 常用於編輯幣別情形、相關幣別詳情頁面或賬單系統中顯示幣別的詳細資訊。
        /// 通常用於隨後的幣別更新或刪除操作前的詳細資訊確認。
        /// </remarks>
        public async Task<Currencies?> GetCurrency(Guid currency_id)
        {
            string strsql = @"SELECT 
                                currency_id, 
                                code, 
                                symbol, 
                                exchange_rate, 
                                is_active,
                                updated_at, 
                                created_at
                              FROM currencies
                              WHERE currency_id = @currency_id";

            return (await ExecuteQueryAsync<Currencies>(strsql, new { currency_id })).FirstOrDefault();
        }

        /// <summary>
        /// 建立新的幣別
        /// </summary>
        /// <param name="model">包含新幣別資料的模型，如幣別代碼、符號、匯率、啟用狀態等</param>
        /// <returns>非同步新增的幣別記錄物件，包含系統生成的 ID 和相關資訊</returns>
        /// <remarks>
        /// 此方法在資料庫中新增一筆幣別記錄，使用提供的模型作為資料來源。
        /// 記錄幣別的相關資訊，包括幣別代碼（如 USD、TWD、EUR 等）、幣別符號（如 $、€、¥ 等）、匯率和啟用狀態。
        /// 幣別代碼通常遵循 ISO 4217 標準，以確保全球一致的幣別表示。
        /// 匯率 (exchange_rate) 定義了此幣別與系統基準幣別的換算比率。
        /// 啟用狀態 (is_active) 決定此幣別是否可在系統中使用。
        /// 新增完成後返回完整的記錄，包含系統生成的幣別 ID、創建時間和更新時間。
        /// 在新增幣別時，需要確保幣別代碼的唯一性，避免系統中出現重複的幣別代碼。
        /// </remarks>
        public async Task<Currencies?> InsertCurrency(Currencies model)
        {
            string strsql = $@" INSERT INTO currencies 
                                    (code, 
                                     symbol, 
                                     exchange_rate, 
                                     is_active)
                                    VALUES 
                                    (@code, 
                                     @symbol, 
                                     @exchange_rate, 
                                     @is_active)
                                RETURNING 
                                    currency_id, 
                                    code, 
                                    symbol, 
                                    exchange_rate, 
                                    is_active, 
                                    updated_at, 
                                    created_at;";

            return (await ExecuteQueryAsync<Currencies>(strsql, model)).FirstOrDefault();
        }

        /// <summary>
        /// 更新特定幣別資訊
        /// </summary>
        /// <param name="model">包含要更新的幣別資料的模型，需要包含 currency_id</param>
        /// <returns>非同步更新後的幣別記錄物件，若更新失敗則返回 null</returns>
        /// <remarks>
        /// 此方法更新已存在的幣別記錄，使用提供的模型作為更新的資料來源。
        /// 只更新模型中提供的非空屬性，使用 COALESCE 函數確保只更新指定的欄位。
        /// 可更新的資料包括幣別代碼、幣別符號、匯率和啟用狀態等屬性。
        /// 雖然幣別代碼可以更新，但在實際應用中應謹慎更改，因為它可能已被其他記錄所使用。
        /// 更新時會自動更新 updated_at 欄位，記錄幣別資訊的最後修改時間。
        /// 更新完成後返回更新後的完整記錄，包含所有幣別相關資訊。
        /// 更新幣別狀態（啟用/停用）時需謹慎，可能影響到系統中已使用該幣別的交易記錄或計價設定。
        /// </remarks>
        public async Task<Currencies?> UpdateCurrency(Currencies model)
        {
            string sqlStr = new($@"UPDATE currencies
                              SET 
                                   code = COALESCE(@code, code),
                                   symbol = COALESCE(@symbol, symbol),
                                   exchange_rate = COALESCE(@exchange_rate, exchange_rate),
                                   is_active = COALESCE(@is_active, is_active),
                                   updated_at = {Now}
                              WHERE currency_id = @currency_id
                              RETURNING 
                                  currency_id, 
                                  code, 
                                  symbol, 
                                  exchange_rate, 
                                  is_active, 
                                  updated_at, 
                                  created_at");

            return (await ExecuteQueryAsync<Currencies>(sqlStr, model)).FirstOrDefault();
        }

        /// <summary>
        /// 刪除特定幣別
        /// </summary>
        /// <param name="currency_id">要刪除的幣別的唯一識別碼</param>
        /// <returns>非同步布林值，表示刪除操作是否成功</returns>
        /// <remarks>
        /// 此方法根據指定的幣別 ID 從資料庫中刪除幣別記錄。
        /// 此操作會永久刪除記錄，刪除後無法恢復，應謹慎使用。
        /// 刪除之前應確認此幣別未被已存在的交易、賬單或其他相關記錄所使用，以避免資料完整性問題。
        /// 如果幣別已被其他記錄所引用，可能會導致外鍵約束錯誤或其他資料庫完整性問題。
        /// 如果只是暂時停用幣別，建議使用更新方法將 is_active 設為 false，而非直接刪除。
        /// 刪除幣別可能會影響系統中對該幣別的依賴，如金額顯示、幣別轉換等功能。
        /// </remarks>
        public async Task<bool> DeleteCurrency(Guid currency_id)
        {
            string sqlStr = @"DELETE FROM currencies
                              WHERE currency_id = @currency_id";

            return await ExecuteCommandAsync(sqlStr, new { currency_id });
        }
    }
}
