using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 租戶資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 ITenantsDac 介面，提供租戶資料的完整資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的租戶，包含創建、查詢、更新和管理租戶狀態的功能。
    /// 為多租戶系統提供基礎架構，管理每個租戶的基本資訊、狀態和計帳設定。
    /// 支援租戶各種相關操作，如啟用/停用租戶，更新租戶資訊等。
    /// </remarks>
    public class TenantsDac : _Dac, ITenantsDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public TenantsDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得租戶資料清單
        /// </summary>
        /// <param name="model">查詢條件模型，包含啟用狀態和分頁參數等</param>
        /// <returns>非同步租戶資料集合</returns>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的租戶資料。
        /// 可根據模型中的啟用狀態 (is_active) 條件篩選結果，如僅查詢啟用中的租戶。
        /// 查詢結果按租戶 ID 排序，確保結果的一致性。
        /// 返回的資料包含租戶 ID、名稱、聯絡電子郵件、啟用狀態、計費幣別、法定地址、創建時間和更新時間等詳細資訊。
        /// 支援分頁功能，使用 limit 和 offset 參數控制返回的記錄數量。
        /// 常用於系統管理介面中的租戶管理頁面、租戶列表或報表等功能。
        /// </remarks>
        public async Task<IList<Tenants>> GetTenantsDataList(TenantsModel model)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(@"SELECT tenant_id,
                                   name,
                                   contact_email,
                                   is_active,
                                   billing_currency,
                                   legal_address,
                                   created_at,
                                   updated_at
                              FROM tenants
                             WHERE (@is_active IS NULL OR is_active = @is_active)
                          ORDER BY tenant_id
                             LIMIT @limit OFFSET @offset;");

            return await ExecuteQueryAsync<Tenants>(strSql.ToString(), model,true);
        }

        /// <summary>
        /// 根據租戶 ID 取得特定租戶詳細資料
        /// </summary>
        /// <param name="tenant_id">要查詢的租戶的唯一識別碼</param>
        /// <returns>非同步租戶記錄物件，如果未找到則返回 null</returns>
        /// <remarks>
        /// 此方法根據提供的租戶 ID 查詢特定租戶的詳細資訊。
        /// 返回包含租戶完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含租戶 ID、名稱、聯絡電子郵件、啟用狀態、計費幣別、法定地址、創建時間和更新時間等詳細資訊。
        /// 法定地址 (legal_address) 欄位以 JSON 格式存儲結構化的地址資訊。
        /// 常用於租戶詳情頁面、租戶管理功能或在相關資源操作中驗證租戶存在性和狀態。
        /// </remarks>
        public async Task<Tenants?> GetTenantsData(Guid tenant_id)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(@"SELECT tenant_id,
                                   name,
                                   contact_email,
                                   is_active,
                                   billing_currency,
                                   legal_address,
                                   created_at,
                                   updated_at
                              FROM tenants
                             WHERE tenant_id  = @tenant_id
                          ORDER BY tenant_id");


            return (await ExecuteQueryAsync<Tenants>(strSql.ToString(), new { tenant_id = tenant_id }, true)).FirstOrDefault();
        }

        /// <summary>
        /// 建立新的租戶資料
        /// </summary>
        /// <param name="model">包含新租戶資料的模型，如租戶名稱、聯絡電子郵件、計費幣別、法定地址等</param>
        /// <returns>非同步新增的租戶記錄物件，包含系統生成的 ID 和相關資訊</returns>
        /// <remarks>
        /// 此方法在資料庫中新增一筆租戶記錄，使用提供的模型作為資料來源。
        /// 記錄租戶的相關資訊，包括租戶名稱、聯絡電子郵件、計費幣別和法定地址。
        /// 法定地址 (legal_address) 被轉換為 PostgreSQL 的 jsonb 格式存儲，允許存儲結構化的地址資訊。
        /// 新增的租戶預設狀態為啟用。
        /// 會自動記錄創建時間和更新時間。
        /// 新增完成後返回完整的記錄，包含系統生成的租戶 ID 和所有相關資訊。
        /// 通常用於系統管理介面中的新增租戶功能，或於租戶註冊過程中創建新租戶記錄。
        /// </remarks>
        public async Task<Tenants?> CreateTenantsData(TenantsModel model)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(@"INSERT INTO tenants (
	                                name,
	                                contact_email,
	                                billing_currency,
	                                legal_address
	                               )
                            VALUES (
		                            @name,
		                            @contact_email,
		                            @billing_currency,
		                            CAST(@legalAddress AS jsonb))
                            RETURNING tenant_id,
                                      name,
                                      contact_email,
                                      is_active,
                                      billing_currency,
                                      legal_address,
                                      created_at,
                                      updated_at;");

            return (await ExecuteQueryAsync<Tenants>(strSql.ToString(), model, true)).FirstOrDefault();
        }

        /// <summary>
        /// 更新租戶資料
        /// </summary>
        /// <param name="model">包含要更新的租戶資料的模型，需要包含 tenant_id</param>
        /// <returns>非同步更新後的租戶記錄物件，若更新失敗則返回 null</returns>
        /// <remarks>
        /// 此方法更新已存在的租戶記錄，使用提供的模型作為更新的資料來源。
        /// 只更新模型中提供的非空屬性，使用 COALESCE 函數確保只更新指定的欄位。
        /// 可更新的資料包括租戶名稱、聯絡電子郵件、啟用狀態和法定地址等屬性。
        /// 法定地址 (legal_address) 被轉換為 PostgreSQL 的 jsonb 格式存儲。
        /// 更新時會自動更新 updated_at 欄位，記錄租戶資訊的最後修改時間。
        /// 更新完成後返回更新後的完整記錄，包含所有租戶相關資訊。
        /// 更新租戶狀態（啟用/停用）時需謹慎，可能影響到該租戶下所有資源的可訪問性。
        /// </remarks>
        public async Task<Tenants?> UpdateTenantsData(TenantsModel model)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append($@"UPDATE tenants
                                SET updated_at = {Now},
                                    name = COALESCE(@name, name),
                                    contact_email = COALESCE(@contact_email, contact_email),
                                    is_active = COALESCE(@is_active, is_active),
                                    legal_address = COALESCE(CAST(@legalAddress AS jsonb), legal_address)
                              WHERE tenant_id = @tenant_id 
                          RETURNING tenant_id,
                                    name,
                                    contact_email,
                                    is_active,
                                    billing_currency,
                                    legal_address,
                                    created_at,
                                    updated_at;");


            return (await ExecuteQueryAsync<Tenants>(strSql.ToString(), model, true)).FirstOrDefault();
        }

        /// <summary>
        /// 停用特定租戶
        /// </summary>
        /// <param name="tenant_id">要停用的租戶的唯一識別碼</param>
        /// <returns>非同步布林值，表示停用操作是否成功</returns>
        /// <remarks>
        /// 此方法將指定租戶的狀態設為停用 (is_active = false)。
        /// 這是一種較安全的停用方式，相較於刪除操作，會保留租戶資料但將其標記為不可用。
        /// 停用時會自動更新 updated_at 欄位，記錄狀態變更的時間。
        /// 停用租戶後，該租戶下的所有資源將無法訪問，包括 API 訪問、使用者登入等。
        /// 此操作通常用於租戶合約終止、付款問題或其他需要暂停租戶存取的情況。
        /// 應謹慎使用此操作，並確保適當的權限控制和通知機制。
        /// </remarks>
        public async Task<bool> UpdateTenantsStatus(Guid tenant_id)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append($@"UPDATE tenants
                                SET updated_at = {Now} , 
                                    is_active = false
                              WHERE tenant_id = @tenant_id ");

            return await ExecuteCommandAsync(strSql.ToString(), new { tenant_id });
        }
    }
}
