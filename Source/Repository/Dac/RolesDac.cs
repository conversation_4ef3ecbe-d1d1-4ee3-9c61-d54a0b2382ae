using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 角色資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IRolesDac 介面，提供角色資料的存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的角色定義，包含查詢和創建角色的功能。
    /// 角色是權限管理的基礎元素，用於定義使用者在系統中的權限和資源存取能力。
    /// 為安全與存取控制系統提供基礎支援，帶控基於角色的權限管理。
    /// </remarks>
    public class RolesDac : _Dac, IRolesDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public RolesDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得所有角色資料清單
        /// </summary>
        /// <returns>非同步角色資料集合</returns>
        /// <remarks>
        /// 此方法查詢並返回系統中所有可用的角色資料。
        /// 查詢結果按創建時間降序排序，顯示最新創建的角色先。
        /// 返回的資料包含角色 ID、名稱、描述、創建時間和更新時間等基本資訊。
        /// 常用於填充使用者管理中的角色選擇下拉式選單、角色管理頁面或在權限設定時顯示可用角色。
        /// 角色可能包含各種系統角色，如管理員、普通使用者、讀取者等，用於定義使用者的權限範圍。
        /// 在基於角色的存取控制系統中，此方法提供了權限管理的基礎資料。
        /// </remarks>
        public async Task<IList<Roles>> GetRolesList()
        {
            string strSql = @"SELECT role_id,
                                     name,
                                     description,
                                     created_at,
                                     updated_at
                                FROM roles
                            ORDER BY created_at DESC";

            return await ExecuteQueryAsync<Roles>(strSql);
        }

        /// <summary>
        /// 建立新的角色資料
        /// </summary>
        /// <param name="model">包含新角色資料的模型，如角色名稱和描述等</param>
        /// <returns>非同步新增的角色記錄物件，包含系統生成的 ID 和相關資訊</returns>
        /// <remarks>
        /// 此方法在資料庫中新增一筆角色記錄，使用提供的模型作為資料來源。
        /// 記錄角色的相關資訊，包括角色名稱和描述等。
        /// 會自動產生角色的唯一識別碼和相關的時間戳記（如創建時間和更新時間）。
        /// 新增完成後返回完整的記錄，包含系統生成的角色 ID 和基本資訊。
        /// 在新增角色後，通常需要適當設定該角色的相關權限，以定義角色可執行的操作。
        /// 新角色實際上不屬於任何使用者，直到將其指定給特定使用者。
        /// 通常用於系統管理中的角色創建功能，以加強存取控制和權限管理的靈活性。
        /// </remarks>
        public async Task<Roles?> CreateRolesData(RolesModel model)
        {
            string strSql = @"INSERT INTO roles 
                                        (name,
                                         description)
                                   VALUES 
                                        (@name,
                                         @description)
                                RETURNING role_id,
                                          name,
                                          description,
                                          created_at,
                                          updated_at";

            return (await ExecuteQueryAsync<Roles>(strSql,model)).FirstOrDefault();
        }
    }

}
