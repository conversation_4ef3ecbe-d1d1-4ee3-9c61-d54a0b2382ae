using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Text;

namespace Paas.Dac
{
    /// <summary>
    /// API 金鑰資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IApiKeysDac 介面，提供 API 金鑰的完整資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的 API 金鑰，包含創建、查詢、更新和刪除 API 金鑰的功能。
    /// 支援多租戶環境，可根據租戶 ID 適當限制資料範圍。
    /// 提供完整的 API 金鑰生命週期管理，包括金鑰的生成、到期設定、狀態追蹤等。
    /// </remarks>
    public class ApiKeysDac : _Dac, IApiKeysDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public ApiKeysDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得 API 金鑰清單
        /// </summary>
        /// <param name="model">查詢條件模型，包含租戶 ID、狀態和分頁參數等</param>
        /// <returns>非同步 API 金鑰集合</returns>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的 API 金鑰資料。
        /// 可根據模型中的條件（如租戶 ID、金鑰狀態等）篩選結果。
        /// 查詢結果按創建時間降序排序，顯示最新創建的金鑰先。
        /// 返回的資料包含金鑰 ID、租戶 ID、API 金鑰值、狀態、權限範圍、到期時間、生成時間、最後使用時間和創建時間等詳細資訊。
        /// 支援分頁功能，使用 limit 和 offset 參數控制返回的記錄數量。
        /// 常用於 API 金鑰管理介面、租戶的 API 金鑰管理或審計功能等場景。
        /// </remarks>
        public async Task<IList<ApiKeys>> GetApiKeys(ApiKeys model)
        {
            string strSql = @" SELECT key_id, 
                                    tenant_id, 
                                    api_key, 
                                    status, 
                                    scopes, 
                                    expiration_at, 
                                    generated_at, 
                                    last_used_at, 
                                    created_at
                                FROM api_keys
                                WHERE (@tenant_id IS NULL OR tenant_id = @tenant_id)
                                    AND (@status IS NULL OR status = @status)
                                ORDER BY created_at DESC
                                LIMIT @limit OFFSET @offset;";

            return await ExecuteQueryAsync<ApiKeys>(strSql, model, true);
        }

        /// <summary>
        /// 根據金鑰 ID 取得特定 API 金鑰詳細資料
        /// </summary>
        /// <param name="keyId">要查詢的 API 金鑰的唯一識別碼</param>
        /// <returns>非同步 API 金鑰物件，如果未找到則返回 null</returns>
        /// <remarks>
        /// 此方法根據提供的金鑰 ID 查詢特定 API 金鑰的詳細資訊。
        /// 返回包含金鑰完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含金鑰 ID、租戶 ID、API 金鑰值、狀態、權限範圍、到期時間、生成時間、最後使用時間、創建時間和更新時間等詳細資訊。
        /// 常用於 API 金鑰詳情頁面、編輯金鑰前的資料讀取或驗證金鑰有效性等場景。
        /// </remarks>
        public async Task<ApiKeys?> GetApiKey(Guid keyId)
        {
            string strsql = @"SELECT 
                                key_id, 
                                tenant_id, 
                                api_key, 
                                status, 
                                scopes, 
                                expiration_at, 
                                generated_at, 
                                last_used_at, 
                                created_at,
                                updated_at
                            FROM api_keys
                            WHERE key_id = @keyId";

            return (await ExecuteQueryAsync<ApiKeys>(strsql, new { keyId },true)).FirstOrDefault();
        }

        /// <summary>
        /// 建立新的 API 金鑰
        /// </summary>
        /// <param name="model">包含新 API 金鑰資料的模型，如租戶 ID、API 金鑰值、權限範圍、到期時間等</param>
        /// <returns>非同步新增的 API 金鑰物件，包含系統生成的 ID 和相關資訊</returns>
        /// <remarks>
        /// 此方法在資料庫中新增一筆 API 金鑰記錄，使用提供的模型作為資料來源。
        /// 新增的 API 金鑰預設狀態為 'active'，表示可以立即使用。
        /// 權限範圍 (scopes) 字段被轉換為 PostgreSQL 的 jsonb 格式存儲，允許存儲複雜的權限結構。
        /// 會自動記錄金鑰的生成時間和創建時間，以追蹤 API 金鑰的生命週期。
        /// 新增完成後返回完整的記錄，包含系統生成的金鑰 ID 和各時間戳記。
        /// 通常用於為新租戶、新專案或新應用創建 API 存取權限以及金鑰輪替等場景。
        /// </remarks>
        public async Task<ApiKeys?> InsertApiKey(ApiKeys model)
        {
            string strsql = $@" INSERT INTO api_keys 
                                    (tenant_id, 
                                    api_key, 
                                    status,
                                    scopes, 
                                    expiration_at)
                                VALUES (
                                    @tenant_id,
                                    @api_key, 
                                    'active',
                                    CAST(@scopes AS jsonb), 
                                    @expiration_at)
                                RETURNING 
                                    key_id, 
                                    tenant_id, 
                                    api_key, 
                                    status, 
                                    scopes, 
                                    expiration_at,
                                    generated_at, 
                                    last_used_at, 
                                    created_at";

            return (await ExecuteQueryAsync<ApiKeys>(strsql, model,true)).FirstOrDefault();
        }

        /// <summary>
        /// 更新特定 API 金鑰資訊
        /// </summary>
        /// <param name="model">包含要更新的 API 金鑰資料的模型，需要包含 key_id</param>
        /// <returns>非同步更新後的 API 金鑰物件，若更新失敗則返回 null</returns>
        /// <remarks>
        /// 此方法更新已存在的 API 金鑰記錄，使用提供的模型作為更新的資料來源。
        /// 只更新模型中提供的非空屬性，使用 COALESCE 函數確保只更新指定的欄位。
        /// 可更新的資料包括金鑰狀態、權限範圍和到期時間等屬性。
        /// 權限範圍 (scopes) 字段會被轉換為 PostgreSQL 的 jsonb 格式存儲。
        /// 更新時會自動更新 updated_at 欄位，以記錄最後修改時間。
        /// 更新完成後返回更新後的完整記錄，包含所有金鑰相關資訊。
        /// 常用於更新金鑰狀態（啟用/停用）、調整權限範圍或延長/縮短金鑰有效期等場景。
        /// </remarks>
        public async Task<ApiKeys?> UpdateApiKey(ApiKeys model)
        {
            string sqlStr = new ($@"UPDATE api_keys
                              SET 
                                   status = COALESCE(@status, status),
                                   scopes = COALESCE(CAST(@scopes AS JSONB), scopes),
                                   expiration_at = COALESCE(@expiration_at, expiration_at),
                                   updated_at = {Now}
                              WHERE key_id = @key_id
                              RETURNING 
                                   key_id, 
                                   tenant_id, 
                                   api_key, 
                                   status, 
                                   scopes,
                                   expiration_at,
                                   generated_at, 
                                   last_used_at, 
                                   created_at");

            return (await ExecuteQueryAsync<ApiKeys>(sqlStr, model, true)).FirstOrDefault();
        }

        /// <summary>
        /// 刪除特定 API 金鑰
        /// </summary>
        /// <param name="keyId">要刪除的 API 金鑰的唯一識別碼</param>
        /// <returns>非同步布林值，表示刪除操作是否成功</returns>
        /// <remarks>
        /// 此方法根據指定的金鑰 ID 從資料庫中刪除 API 金鑰記錄。
        /// 此操作會永久刪除記錄，刪除後無法恢復，應謹慎使用。
        /// 刪除金鑰後，使用該金鑰的所有 API 請求將無法進行認證，產生缺乏授權錔誤。
        /// 建議在刪除前先進行備份或記錄金鑰相關資訊，以便於需要時可以參考。
        /// 如果只是暂時停用金鑰設定，建議使用更新狀態為 'inactive' 而非直接刪除。
        /// </remarks>
        public async Task<bool> DeleteApiKey(Guid keyId)
        {
            string sqlStr = @"DELETE FROM api_keys
                              WHERE key_id = @keyId";

            return await ExecuteCommandAsync(sqlStr, new { keyId });
        }
    }
}
