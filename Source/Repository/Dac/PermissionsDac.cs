using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 權限資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IPermissionsDac 介面，提供權限相關的資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的權限資料，包含查詢特定角色的權限清單等功能。
    /// 為系統的存取控制和權限檢測提供基礎支援，確保使用者僅能存取權限所允許的資源。
    /// 支援以角色為基礎的權限模型，其中每個角色可擁有多種資源的多種操作權限。
    /// </remarks>
    public class PermissionsDac : _Dac , IPermissionsDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public PermissionsDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得特定角色的所有權限列表
        /// </summary>
        /// <param name="role_id">要查詢權限的角色的唯一識別碼</param>
        /// <returns>非同步權限資料集合</returns>
        /// <remarks>
        /// 此方法根據提供的角色 ID 查詢該角色擁有的所有權限記錄。
        /// 包含了該角色可以存取的所有資源和其對應的操作權限。
        /// 查詢結果返回角色的所有權限屬性，包含權限 ID、角色 ID、資源標識、行為操作、創建時間和更新時間等。
        /// 系統使用該資料確定使用者是否擁有適當的權限執行特定的操作，是存取控制的基礎數據來源。
        /// 常用於權限管理介面、權限檢測和安全存取控制機制中。
        /// 在權限記錄中，"resource" 欄位指定資源的標識符，"action" 欄位指定對該資源可執行的操作（如讀取、寫入、更新、刪除等）。
        /// </remarks>
        public async Task<IList<Permissions>> GetPermissionsList(Guid role_id)
        {
            string strSql = @"SELECT permission_id,
                                     role_id,
                                     resource,
                                     action,
                                     created_at,
                                     updated_at
                                FROM permissions
                               WHERE role_id = @roleId";

            return await ExecuteQueryAsync<Permissions>(strSql, new { roleId = role_id });

        }
    }
}
