using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Text;

namespace Paas.Dac
{
    /// <summary>
    /// 通知資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 INotificationsDac 介面，提供系統通知的完整資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的通知功能，包含創建、查詢通知的功能。
    /// 支援多租戶環境，可根據租戶 ID 和使用者 ID 適當限制資料範圍。
    /// 提供完整的通知管理，支援不同類型、優先级和狀態的通知記錄。
    /// </remarks>
    public class NotificationsDac : _Dac, INotificationsDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public NotificationsDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得通知資料清單
        /// </summary>
        /// <param name="model">查詢條件模型，包含租戶 ID、使用者 ID、狀態、優先級和分頁參數等</param>
        /// <returns>非同步通知資料集合</returns>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的通知資料。
        /// 可根據模型中的條件（如租戶 ID、使用者 ID、通知狀態、優先級等）篩選結果。
        /// 查詢結果按創建時間降序排序，顯示最新的通知先。
        /// 返回的資料包含通知 ID、類型 ID、租戶 ID、使用者 ID、標題、內容、狀態、優先級、到期時間和創建時間等詳細資訊。
        /// 支援分頁功能，使用 Limit 和 Offset 參數控制返回的記錄數量。
        /// 常用於使用者的通知中心、通知列表或管理介面等場景。
        /// </remarks>
        public async Task<IList<Notifications>> GetNotifications(Notifications model)
        {
            string strSql = @"SELECT 
                                notification_id, 
                                type_id, 
                                tenant_id, 
                                user_id, 
                                title, 
                                message, 
                                status, 
                                priority, 
                                expires_at, 
                                created_at
                                FROM notifications
                              WHERE (@tenant_id IS NULL OR tenant_id = @tenant_id)
                                AND (@user_id IS NULL OR user_id = @user_id)
                                AND (@status IS NULL OR status = @status)
                                AND (@priority IS NULL OR priority = @priority)
                              ORDER BY created_at DESC
                              LIMIT @Limit OFFSET @Offset";

            return await ExecuteQueryAsync<Notifications>(strSql, model, true);
        }

        /// <summary>
        /// 根據通知 ID 取得特定通知詳細資料
        /// </summary>
        /// <param name="notification_id">要查詢的通知的唯一識別碼</param>
        /// <returns>非同步通知記錄物件，如果未找到則返回 null</returns>
        /// <remarks>
        /// 此方法根據提供的通知 ID 查詢特定通知的詳細資訊。
        /// 返回包含通知完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含通知 ID、類型 ID、租戶 ID、使用者 ID、標題、內容、狀態、優先級、到期時間和創建時間等詳細資訊。
        /// 常用於顯示通知的完整內容，如當使用者點擊通知以查看詳細資訊時。
        /// 查詢特定通知後，通常需要更新該通知的狀態，如從 'unread' 更新為 'read'。
        /// </remarks>
        public async Task<Notifications?> GetNotification(Guid notification_id)
        {
            string strsql = @"SELECT 
                                notification_id, 
                                type_id, 
                                tenant_id, 
                                user_id, 
                                title, 
                                message, 
                                status, 
                                priority, 
                                expires_at, 
                                created_at
                              FROM notifications
                              WHERE notification_id = @notification_id";

            return (await ExecuteQueryAsync<Notifications>(strsql, new { notification_id },true)).FirstOrDefault();
        }

        /// <summary>
        /// 建立新的通知
        /// </summary>
        /// <param name="model">包含新通知資料的模型，如類型 ID、租戶 ID、使用者 ID、標題、內容、優先級、到期時間等</param>
        /// <returns>非同步新增的通知記錄物件，包含系統生成的 ID 和相關資訊</returns>
        /// <remarks>
        /// 此方法在資料庫中新增一筆通知記錄，使用提供的模型作為資料來源。
        /// 新增的通知預設狀態為 'unread'，表示尚未被使用者讀取。
        /// 通知類型 (type_id) 表示通知的類別，如系統通知、警告、提醒等。
        /// 可設置通知的優先級 (priority)，用於決定通知的重要性和顯示順序。
        /// 可指定通知的到期時間 (expires_at)，超過此時間後通知可能會被自動移除或標記為過期。
        /// 新增完成後返回完整的記錄，包含系統生成的通知 ID 和創建時間。
        /// 通常用於系統事件通知、使用者提醒、重要公告或應用中的互動訊息等場景。
        /// </remarks>
        public async Task<Notifications?> InsertNotifications(Notifications model)
        {
            string strsql = @"INSERT INTO notifications 
                                (type_id, 
                                tenant_id, 
                                user_id, 
                                title, 
                                message, 
                                status,
                                priority, 
                                expires_at)
                              VALUES 
                                (@type_id,
                                @tenant_id,
                                @user_id,
                                @title, 
                                @message, 
                                'unread',
                                @priority, 
                                @expires_at)
                              RETURNING 
                                notification_id, 
                                type_id, 
                                tenant_id, 
                                user_id, 
                                title, 
                                message, 
                                status, 
                                priority, 
                                expires_at, 
                                created_at";

            return (await ExecuteQueryAsync<Notifications>(strsql, model, true)).FirstOrDefault();
        }
    }
}
