using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Dac
{
    /// <summary>
    /// 帳單資料存取類別
    /// </summary>
    /// <remarks>
    /// 此類別繼承自 _Dac 基礎類別並實作 IBillingDac 介面，提供帳單相關的資料存取功能。
    /// 負責處理帳單記錄的查詢、新增、更新和刪除等資料庫操作。
    /// 使用 SQL 查詢語句直接與資料庫進行互動，傳遞參數化的查詢以增強安全性。
    /// 支援多租戶環境中的帳單管理，為上層的服務層提供必要的資料存取能力。
    /// </remarks>
    public class BillingDac : _Dac, IBillingDac
    {
        /// <summary>
        /// 初始化帳單資料存取類別
        /// </summary>
        /// <remarks>
        /// 建構子接收一個連線控制中心集合，並將其傳遞給基礎類別的建構子。
        /// 這些連線控制中心用於建立資料庫連線，根據系統設定的資料庫提供者選擇適當的連線。
        /// </remarks>
        /// <param name="connectionControlCenters">資料庫連線控制中心的集合，用於建立資料庫連線</param>
        public BillingDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }


        /// <summary>
        /// 取得帳單資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的帳單資料。
        /// 使用參數化的 SQL 查詢，根據指定的條件（如租戶 ID、狀態等）遍历 billing_records 表。
        /// 查詢結果依創建時間降序排列，返回所有符合條件的帳單記錄。
        /// 支援多租戶環境，可根據租戶 ID 適當限制資料範圍。
        /// 返回的資料包含帳單 ID、租戶 ID、金額、幣別、付款方式、狀態、到期日、付款日期和建立/更新時間等詳細資訊。
        /// </remarks>
        /// <param name="model">帳單模型，包含查詢條件如租戶 ID、狀態等</param>
        /// <returns>符合條件的帳單記錄集合</returns>
        public async Task<IList<BillingRecords>> GetBillingDataList(BillingModel model)
        {
            string strSql = @"SELECT record_id, 
                                     tenant_id,
                                     amount,
                                     currency_id,
                                     payment_method_id,
                                     status,
                                     due_date,
                                     paid_at,
                                     created_at,
                                     updated_at
                                FROM billing_records
                               WHERE (@tenant_id IS NULL OR tenant_id = @tenant_id)
                                 AND (@status IS NULL OR status = @status)
                            ORDER BY created_at DESC ";


            return await ExecuteQueryAsync<BillingRecords>(strSql, model);
        }

        /// <summary>
        /// 根據帳單 ID 取得特定帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的帳單 ID 查詢特定帳單的詳細資訊。
        /// 使用參數化的 SQL 查詢，單一條件為帳單的唯一識別碼(record_id)。
        /// 返回包含帳單完整詳細資訊的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含帳單 ID、租戶 ID、金額、幣別、付款方式、狀態、到期日、付款日期和建立/更新時間等詳細資訊。
        /// </remarks>
        /// <param name="billing_id">要查詢的帳單的唯一識別碼</param>
        /// <returns>帳單記錄物件，如果未找到則返回 null</returns>
        public async Task<BillingRecords?> GetBillingDataById(Guid billing_id)
        {
            string strSql = @"SELECT record_id, 
                                     tenant_id,
                                     amount,
                                     currency_id,
                                     payment_method_id,
                                     status,
                                     due_date,
                                     paid_at,
                                     created_at,
                                     updated_at
                                FROM billing_records 
                               WHERE record_id = @billing_id  ";

            return (await ExecuteQueryAsync<BillingRecords>(strSql, new { billing_id })).FirstOrDefault();
        }

        /// <summary>
        /// 建立新的帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法在資料庫中新增一筆帳單記錄，使用提供的模型作為資料來源。
        /// 使用參數化的 SQL INSERT 語句將帳單資料新增至 billing_records 表中。
        /// 會將模型中的到期日期(due_date)轉換為 UTC 時間，確保資料庫中的時間格式一致。
        /// 使用 PostgreSQL 的 RETURNING 子句紀錄新增的資料，包括系統自動生成的欄位（如 record_id、created_at）。
        /// 返回新建立的帳單記錄，包含完整的資料內容和系統產生的唯一識別碼。
        /// </remarks>
        /// <param name="model">包含新帳單資料的模型，如租戶 ID、金額、幣別、付款方式、狀態和到期日</param>
        /// <returns>新增的帳單記錄物件，包含系統生成的 ID 和相關資訊</returns>
        public async Task<BillingRecords?> CreateBillingData(BillingModel model)
        {
            string strSql = @"INSERT INTO billing_records 
	                                  (tenant_id,
	                                   amount,
	                                   currency_id,
	                                   payment_method_id,
                                       status,
	                                   due_date)
                                   VALUES 
                                      (@tenant_id,
		                               @amount,
		                               @currency_id,
		                               @payment_method_id,
                                       @status,
		                               @due_date) 
                                RETURNING 
                                       record_id,
	                                   tenant_id,
	                                   amount,
	                                   currency_id,
	                                   payment_method_id,
	                                   status,
	                                   due_date,
	                                   paid_at,
	                                   created_at,
                                       updated_at;";

            var parameters = new
            {
                model.tenant_id,
                model.amount,
                model.currency_id,
                model.payment_method_id,
                model.status,
                due_date = model.due_date.UtcDateTime // 轉換為 UTC
            };

            return (await ExecuteQueryAsync<BillingRecords>(strSql, model)).FirstOrDefault();
        }

        /// <summary>
        /// 更新帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法更新已存在的帳單記錄，使用提供的模型作為資料來源。
        /// 使用參數化的 SQL UPDATE 語句更新 billing_records 表中的帳單資料。
        /// 使用 COALESCE 函數確保只更新模型中提供的非空欄位，空值欄位會保留原始值。
        /// 自動更新 updated_at 欄位為當前時間，同時保留原始的 created_at 值。
        /// 使用 PostgreSQL 的 RETURNING 子句返回更新後的完整記錄。
        /// 返回更新後的帳單記錄，包含最新的資料內容。
        /// </remarks>
        /// <param name="model">包含要更新的帳單資料的模型，需要包含 record_id</param>
        /// <returns>更新後的帳單記錄物件，若更新失敗則返回 null</returns>
        public async Task<BillingRecords?> UpdateBillingData(BillingModel model)
        {
            string strSql = @"UPDATE billing_records
                                 SET status = COALESCE(@status, status),
                                     paid_at = COALESCE(@paid_at, paid_at),
                                     created_at = created_at,
                                     updated_at = CURRENT_TIMESTAMP
                               WHERE record_id = @record_id
                           RETURNING 
                                     record_id,
	                                 tenant_id,
	                                 amount,
	                                 currency_id,
	                                 payment_method_id,
	                                 status,
	                                 due_date,
	                                 paid_at,
	                                 created_at,
                                     updated_at;";

            return (await ExecuteQueryAsync<BillingRecords>(strSql.ToString(), model)).FirstOrDefault();
        }

        /// <summary>
        /// 刪除特定帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法根據指定的帳單 ID 刪除資料庫中的帳單記錄。
        /// 使用參數化的 SQL DELETE 語句從 billing_records 表中刪除指定的記錄。
        /// 此操作會永久刪除記錄，刪除後無法恢復，應謹慎使用。
        /// 若要確保應用程式中的數據完整性，可考慮實作輔助方法以處理刪除前的關聯資料棄置。
        /// </remarks>
        /// <param name="billing_id">要刪除的帳單的唯一識別碼</param>
        /// <returns>刪除操作是否成功，成功為 true，失敗為 false</returns>
        public async Task<bool> DeleteBillingData(Guid billing_id)
        {
            string sqlStr = @"DELETE FROM billing_records
                              WHERE record_id = @billing_id";

            return await ExecuteCommandAsync(sqlStr, new { billing_id });
        }
    }
}
