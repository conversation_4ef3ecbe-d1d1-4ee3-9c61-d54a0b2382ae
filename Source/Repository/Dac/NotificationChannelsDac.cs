using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Text;

namespace Paas.Dac
{
    /// <summary>
    /// 通知管道資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 INotificationChannelsDac 介面，提供通知管道的完整資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的通知管道，包含創建、查詢、更新和刪除通知管道的功能。
    /// 支援不同的通知管道類型，如電子郵件、簡訊、應用內通知、推播訊息等。
    /// 管道設定使用結構化格式 (JSON) 存儲，可靈活設定各類型管道的特定參數。
    /// </remarks>
    public class NotificationChannelsDac : _Dac, INotificationChannelsDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public NotificationChannelsDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得通知管道設定清單
        /// </summary>
        /// <param name="model">查詢條件模型，包含啟用狀態和分頁參數等</param>
        /// <returns>非同步通知管道設定集合</returns>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的通知管道設定。
        /// 可根據模型中的條件（如管道的啟用狀態）篩選結果。
        /// 查詢結果按創建時間降序排序，顯示最新創建的管道設定先。
        /// 返回的資料包含管道 ID、管道類型、設定參數、啟用狀態、創建時間和更新時間等詳細資訊。
        /// 支援分頁功能，使用 Limit 和 Offset 參數控制返回的記錄數量。
        /// 常用於系統管理介面中的通知管道設定頁面、通知管道列表或設定處理。
        /// 管道類型 (type) 可能包括電子郵件、簡訊、應用內通知、推播訊息等不同的通知媒介。
        /// </remarks>
        public async Task<IList<NotificationChannels>> GetNotificationChannels(NotificationChannels model)
        {
            string strSql = @"  SELECT 
                                    channel_id, 
                                    type, 
                                    config, 
                                    is_active, 
                                    created_at, 
                                    updated_at
                                FROM notification_channels
                                WHERE (@is_active IS NULL OR is_active = @is_active)
                                ORDER BY created_at DESC
                                LIMIT @Limit OFFSET @Offset";

            return await ExecuteQueryAsync<NotificationChannels>(strSql, model, true);
        }

        /// <summary>
        /// 建立新的通知管道設定
        /// </summary>
        /// <param name="model">包含新通知管道設定的模型，如管道類型、設定參數、啟用狀態等</param>
        /// <returns>非同步新增的通知管道設定物件，包含系統生成的 ID 和相關資訊</returns>
        /// <remarks>
        /// 此方法在資料庫中新增一筆通知管道設定記錄，使用提供的模型作為資料來源。
        /// 記錄通知管道的相關資訊，包括管道類型、設定參數和啟用狀態。
        /// 管道類型 (type) 定義了通知的發送媒介，如電子郵件、簡訊、應用內通知等。
        /// 設定參數 (config) 以 JSON 格式存儲，包含特定管道類型的設定資訊，如電子郵件伺服器設定、簡訊 API 密鑰等。
        /// 啟用狀態 (is_active) 決定此管道是否可在系統中使用於通知發送。
        /// 新增完成後返回完整的記錄，包含系統生成的管道 ID、創建時間和更新時間。
        /// 通常用於系統管理介面中的通知管道設定功能，以支援各種不同類型的通知發送方式。
        /// </remarks>
        public async Task<NotificationChannels?> InsertNotificationChannel(NotificationChannels model)
        {
            string strsql = $@" INSERT INTO notification_channels 
                                    (type, 
                                    config, 
                                    is_active)
                                VALUES (
                                    @type,
                                    CAST(@config AS jsonb),
                                    @is_active)
                                RETURNING 
                                    channel_id, 
                                    type,
                                    config, 
                                    is_active, 
                                    created_at, 
                                    updated_at";

            return (await ExecuteQueryAsync<NotificationChannels>(strsql, model, true)).FirstOrDefault();
        }

        /// <summary>
        /// 更新特定通知管道設定
        /// </summary>
        /// <param name="model">包含要更新的通知管道設定的模型，需要包含 channel_id</param>
        /// <returns>非同步更新後的通知管道設定物件，若更新失敗則返回 null</returns>
        /// <remarks>
        /// 此方法更新已存在的通知管道設定記錄，使用提供的模型作為更新的資料來源。
        /// 只更新模型中提供的非空屬性，使用 COALESCE 函數確保只更新指定的欄位。
        /// 可更新的資料包括設定參數 (config) 和啟用狀態 (is_active) 等屬性。
        /// 設定參數 (config) 以 JSON 格式存儲，更新時會被轉換為 PostgreSQL 的 jsonb 格式。
        /// 更新時會自動更新 updated_at 欄位，記錄通知管道設定的最後修改時間。
        /// 更新完成後返回更新後的完整記錄，包含所有通知管道相關資訊。
        /// 更新管道狀態（啟用/停用）時需謹慎，可能影響到系統中使用該管道的通知發送功能。
        /// </remarks>
        public async Task<NotificationChannels?> UpdateNotificationChannel(NotificationChannels model)
        {
            string sqlStr = new($@"UPDATE notification_channels
                              SET 
                                   is_active = COALESCE(@is_active, is_active),
                                   config = COALESCE(CAST(@config AS JSONB), config),
                                   updated_at = {Now}
                              WHERE channel_id = @channel_id
                              RETURNING 
                                   channel_id, 
                                   type, 
                                   config, 
                                   is_active, 
                                   created_at, 
                                   updated_at");

            return (await ExecuteQueryAsync<NotificationChannels>(sqlStr, model, true)).FirstOrDefault();
        }

        /// <summary>
        /// 刪除特定通知管道設定
        /// </summary>
        /// <param name="channelId">要刪除的通知管道的唯一識別碼</param>
        /// <returns>非同步布林值，表示刪除操作是否成功</returns>
        /// <remarks>
        /// 此方法根據指定的管道 ID 從資料庫中刪除通知管道設定記錄。
        /// 此操作會永久刪除記錄，刪除後無法恢復，應謹慎使用。
        /// 刪除後，系統將不再能使用此管道發送通知，可能會影響現有的通知配置。
        /// 刪除管道前應確保沒有相關的通知模板或通知設定依賴該管道，以避免系統錯誤。
        /// 如果只是暂時停用管道功能，建議使用更新方法將 is_active 設為 false，而非直接刪除。
        /// 刪除操作應給予全面的權限控制，確保只有適當的系統管理員可以執行此操作。
        /// </remarks>
        public async Task<bool> DeleteNotificationChannel(Guid channelId)
        {
            string sqlStr = @"DELETE FROM notification_channels
                              WHERE channel_id  = @channelId";

            return await ExecuteCommandAsync(sqlStr, new { channelId });
        }
    }
}
