using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Text;

namespace Paas.Dac
{
    /// <summary>
    /// 安全性日誌資料存取層實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 ISecurityLogsDac 介面，提供安全性日誌的資料存取功能。
    /// 繼承自 _Dac 基礎類別，確保資料庫連線和交易管理的一致性。
    /// 通過依賴注入方式取得所需的資料庫連線控制中心。
    /// 負責管理系統中的安全性日誌資料，包含查詢安全日誌資料的功能。
    /// 支援多租戶環境，可根據租戶 ID 和使用者 ID 適當限制資料範圍。
    /// 記錄安全相關事件，如登入嘗試、權限變更、資料存取等，為系統提供安全審計和合規性驗證功能。
    /// </remarks>
    public class SecurityLogsDac : _Dac, ISecurityLogsDac
    {
        /// <summary>
        /// 建構子
        /// </summary>
        /// <param name="connectionControlCenters">資料庫連線控制中心集合</param>
        /// <remarks>
        /// 通過依賴注入方式取得所需的資料庫連線控制中心。
        /// 使用基礎類別的建構子初始化實例，確保適當的資料庫連線管理。
        /// </remarks>
        public SecurityLogsDac(IEnumerable<IConnectionControlCenter> connectionControlCenters) : base(connectionControlCenters) { }

        /// <summary>
        /// 取得安全性日誌資料清單
        /// </summary>
        /// <param name="model">查詢條件模型，包含租戶 ID、使用者 ID、事件類型 ID、時間範圍和分頁參數等</param>
        /// <returns>非同步安全性日誌資料集合</returns>
        /// <remarks>
        /// 此方法根據提供的模型參數查詢符合條件的安全性日誌資料。
        /// 可根據模型中的條件（如租戶 ID、事件類型 ID等）篩選結果。
        /// 支援時間範圍過濾，透過 start_time 和 end_time 字段進行時間範圍篩選。
        /// 查詢結果按創建時間降序排序，顯示最新的安全日誌先。
        /// 返回的資料包含日誌 ID、租戶 ID、使用者 ID、事件類型 ID、IP 位址、使用者代理資訊、詳細內容和創建時間等詳細資訊。
        /// 支援分頁功能，使用 Limit 和 Offset 參數控制返回的記錄數量。
        /// 常用於安全審計、使用者活動監控、安全事件調查和合規性報告等場景。
        /// </remarks>
        public async Task<IList<SecurityLogs>> GetSecurityLogs(SecurityLogs model)
        {
            string strSql = @"SELECT 
                                log_id, 
                                tenant_id, 
                                user_id, 
                                event_type_id, 
                                ip_address, 
                                user_agent, 
                                details, 
                                created_at
                              FROM security_logs
                              WHERE (@tenant_id IS NULL OR tenant_id = @tenant_id)
                                AND (@event_type_id IS NULL OR event_type_id = @event_type_id)
                                AND created_at BETWEEN @start_time AND @end_time
                              ORDER BY created_at DESC
                              LIMIT @Limit OFFSET @Offset";

            return await ExecuteQueryAsync<SecurityLogs>(strSql, model, true);
        }

        /// <summary>
        /// 根據日誌 ID 取得特定安全性日誌詳細資料
        /// </summary>
        /// <param name="logId">要查詢的安全性日誌的唯一識別碼</param>
        /// <returns>非同步安全性日誌記錄物件，如果未找到則返回 null</returns>
        /// <remarks>
        /// 此方法根據提供的日誌 ID 查詢特定安全性日誌的詳細資訊。
        /// 返回包含安全性日誌完整詳細資料的物件，若無匹配記錄則返回 null。
        /// 返回的資料包含日誌 ID、租戶 ID、使用者 ID、事件類型 ID、IP 位址、使用者代理資訊、詳細內容和創建時間等詳細資訊。
        /// 詳細內容 (details) 欄位通常以 JSON 格式存儲事件的特定內容，依事件類型而有不同的結構。
        /// 常用於安全事件分析、事件調查、審計核查或合規性報告等場景。
        /// 不同的事件類型可能需要不同的處理方式，可以根據 event_type_id 進行相應的解析與呈現。
        /// </remarks>
        public async Task<SecurityLogs?> GetSecurityLog(Guid logId)
        {
            string strsql = @"SELECT 
                                   log_id, 
                                   tenant_id, 
                                   user_id, 
                                   event_type_id, 
                                   ip_address, 
                                   user_agent, 
                                   details, 
                                   created_at
                               FROM security_logs
                               WHERE log_id = @logId";

            return (await ExecuteQueryAsync<SecurityLogs>(strsql, new { logId },true)).FirstOrDefault();
        }
    }
}
