using AutoMapper;
using Newtonsoft.Json;
using Paas.Dac;
using Paas.Model;
using System.Transactions;
using static System.Runtime.InteropServices.JavaScript.JSType;


namespace Paas.Service
{
    /// <summary>
    /// 租戶管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 ITenantsService 介面，提供租戶管理相關的服務功能。
    /// 貼賴資料存取層進行實際的資料庫操作，並使用 AutoMapper 轉換資料模型。
    /// 主要負責租戶的建立、查詢、更新和狀態管理功能，是多租戶架構的核心元件。
    /// 此服務在堆積建立、賬戶管理和使用者登入識別等功能中有重要作用。
    /// </remarks>
    public class TenantsService : ITenantsService
    {
        /// <summary>
        /// 租戶資料存取組件
        /// </summary>
        private readonly ITenantsDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化租戶管理服務
        /// </summary>
        /// <param name="dac">租戶資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public TenantsService(ITenantsDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得租戶資訊清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的查詢條件，取得符合條件的租戶資訊清單。
        /// 支援多種查詢條件，如已啟用狀態、建立時間範圍等，並支援分頁功能。
        /// 使用 DAC 層進行實際的資料庫查詢，並通過 AutoMapper 將結果轉換成服務層模型。
        /// 常用於管理介面的租戶清單展示和每月賬單計算等情境。
        /// </remarks>
        /// <param name="model">包含查詢條件的租戶模型，如 is_active、分頁參數等</param>
        /// <returns>符合條件的租戶資訊清單</returns>
        public async Task<IList<TenantsModel>> GetTenantsDataList(TenantsModel model)
        {
            // 取得租戶資訊清單
            IList<Tenants> result = await dac.GetTenantsDataList(model);

            return mapper.Map<IList<TenantsModel>>(result);
        }
        /// <summary>
        /// 取得指定租戶的詳細資訊
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的租戶 ID 取得特定租戶的詳細資訊。
        /// 返回的資料包含租戶的完整詳細資訊，如名稱、法律地址、聯絡方式、帳單設定和狀態等。
        /// 使用 DAC 層進行資料庫存取，並通過 AutoMapper 將結果轉換成服務層模型。
        /// 常用於租戶管理介面和租戶詳細資訊頁面中。
        /// </remarks>
        /// <param name="tenant_id">要查詢的租戶的唯一識別碼</param>
        /// <returns>包含租戶資訊的模型，如果找不到會返回可能為空的模型</returns>
        public async Task<TenantsModel> GetTenantsData(Guid tenant_id)
        {
            // 取得租戶資訊
            Tenants? result = await dac.GetTenantsData(tenant_id);

            return mapper.Map<TenantsModel>(result);
        }

        /// <summary>
        /// 新增租戶資料
        /// </summary>
        /// <remarks>
        /// 此方法用於在系統中建立新的租戶記錄，使用提供的模型作為資料來源。
        /// 在建立過程中會進行必要的資料處理，如將地址物件轉換為 JSON 字串以儲存至資料庫。
        /// 新創建的租戶會預設為啟用狀態，並設定建立的時間戳。
        /// 完成後返回的模型包含已生成的租戶識別碼和其他系統生成的欄位。
        /// </remarks>
        /// <param name="model">包含新租戶資料的模型，應包含名稱、聯絡方式和其他必要資訊</param>
        /// <returns>建立成功後的租戶模型，包含新生成的租戶 ID 和其他系統設定的欄位</returns>
        public async Task<TenantsModel> CreateTenantsData(TenantsModel model)
        {
            // 進行地址物件轉換
            model.legalAddress = model.legal_address != null ? JsonConvert.SerializeObject(model.legal_address) : "";
            // 新增租戶資料
            Tenants? data = await dac.CreateTenantsData(model);

            return mapper.Map<TenantsModel>(data);
        }

        /// <summary>
        /// 更新租戶資料
        /// </summary>
        /// <remarks>
        /// 此方法用於更新現有租戶的資料，使用提供的模型作為資料來源。
        /// 在更新過程中會進行必要的資料處理，如將地址物件轉換為 JSON 字串以儲存至資料庫。
        /// 更新過程會驗證租戶的存在性和必要欄位，並更新修改時間戳。
        /// 完成後返回的模型包含更新後的租戶資料，如名稱、地址資訊和其他可更新的欄位。
        /// </remarks>
        /// <param name="model">包含要更新的租戶資料模型，必須包含租戶 ID</param>
        /// <returns>更新成功後的租戶模型，包含最新的資料狀態</returns>
        public async Task<TenantsModel> UpdateTenantsData(TenantsModel model)
        {
            // 進行地址物件轉換
            model.legalAddress = model.legal_address != null ? JsonConvert.SerializeObject(model.legal_address) : "";
            // 更新租戶資料
            Tenants? data = await dac.UpdateTenantsData(model);

            return mapper.Map<TenantsModel>(data);
        }

        /// <summary>
        /// 更新租戶狀態
        /// </summary>
        /// <remarks>
        /// 此方法用於切換指定租戶的啟用/停用狀態，如果当前是啟用狀態則停用，反之也然。
        /// 此方法專限於狀態切換，不會改變租戶的其他資訊，如名稱、聯絡方式等。
        /// 停用租戶將導致相關的 API 服務、通知與賬單功能等也停止運作。
        /// 此操作應謹慎使用，可能需要額外的權限驗證或審核流程。
        /// </remarks>
        /// <param name="tenant_id">要切換狀態的租戶的唯一識別碼</param>
        /// <returns>切換狀態的結果，成功則返回 true，失敗則返回 false</returns>
        public async Task<bool> UpdateTenantsStatus(Guid tenant_id)
        {
            // 更新租戶狀態
            return await dac.UpdateTenantsStatus(tenant_id);
        }
    }
}
