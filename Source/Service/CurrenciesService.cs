using AutoMapper;
using Paas.Dac;
using Paas.Model;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Paas.Service
{
    /// <summary>
    /// 幣別管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 ICurrenciesService 介面，提供幣別相關的管理功能。
    /// 負責處理幣別的建立、查詢、更新和刪除等操作，是系統貨幣設定的核心元件。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// 幣別設定對於多幣別系統、貨幣轉換和貨幣相關報表至關重要。
    /// </remarks>
    public class CurrenciesService : ICurrenciesService
    {
        /// <summary>
        /// 幣別資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責幣別相關的資料庫操作，如查詢、新增、更新和刪除等。
        /// </remarks>
        private readonly ICurrenciesDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化幣別管理服務
        /// </summary>
        /// <param name="dac">幣別資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public CurrenciesService(ICurrenciesDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得所有幣別資訊
        /// </summary>
        /// <remarks>
        /// 此方法用於取得系統中所有可用的幣別資訊。
        /// 返回的資料包含每種幣別的完整詳細資料，如幣別代碼、名稱、符號、代獎和描述等。
        /// 使用 DAC 層進行資料庫查詢操作，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於幣別選擇列表、貨幣轉換功能、幣別設定頁面等情境。
        /// 此方法不接受任何查詢條件，會返回系統中記錄的所有幣別。
        /// </remarks>
        /// <returns>包含所有幣別資訊的模型集合</returns>
        public async Task<IList<CurrenciesModel>> GetCurrencies()
        {
            IList<Currencies> data = await dac.GetCurrencies();
            return mapper.Map<IList<CurrenciesModel>>(data);
        }

        /// <summary>
        /// 取得特定幣別的詳細資訊
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的幣別 ID 取得特定幣別的詳細資訊。
        /// 返回的資料包含幣別的完整詳細資料，如幣別代碼、名稱、符號、代獎和描述等。
        /// 使用 DAC 層進行資料庫查詢操作，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於幣別詳細資訊頁面、編輯幣別時的資料載入、貨幣轉換計算等情境。
        /// </remarks>
        /// <param name="currency_id">要查詢的幣別的唯一識別碼</param>
        /// <returns>特定幣別的詳細資訊模型，如果找不到則返回 null</returns>
        public async Task<CurrenciesModel?> GetCurrency(Guid currency_id)
        {
            Currencies? data = await dac.GetCurrency(currency_id);
            return mapper.Map<CurrenciesModel>(data);
        }

        /// <summary>
        /// 建立新的幣別
        /// </summary>
        /// <remarks>
        /// 此方法用於在系統中建立新的幣別記錄，使用提供的模型作為資料來源。
        /// 在建立過程中會產生唯一的幣別 ID，並設定必要的屬性如幣別代碼、名稱、符號等。
        /// 建立新幣別時會驗證幣別代碼的唯一性，並確保符合國際貨幣標準。
        /// 對於多幣別系統而言，此功能允許管理員撿充新的幣別支援。
        /// </remarks>
        /// <param name="model">包含新幣別資料的模型，如幣別代碼、名稱、符號等</param>
        /// <returns>新增成功後的幣別模型，包含系統生成的 ID 和相關屬性，如果失敗則返回 null</returns>
        public async Task<CurrenciesModel?> InsertCurrency(CurrenciesModel model)
        {
            Currencies? insertedData = await dac.InsertCurrency(mapper.Map<Currencies>(model));
            return mapper.Map<CurrenciesModel>(insertedData);
        }

        /// <summary>
        /// 更新特定幣別資訊
        /// </summary>
        /// <remarks>
        /// 此方法用於更新現有幣別的資料，使用提供的模型作為資料來源。
        /// 更新過程會驗證幣別的存在性和所有必要欄位的有效性。
        /// 可更新的屬性包括幣別的名稱、描述、符號、代獎等，但通常不會修改幣別的唯一代碼。
        /// 在多幣別系統中，此功能允許管理員更新幣別的顯示方式或相關屬性。
        /// </remarks>
        /// <param name="model">包含要更新的幣別資料的模型，必需包含幣別 ID</param>
        /// <returns>更新後的幣別模型，包含最新的資料狀態，如果失敗則返回 null</returns>
        public async Task<CurrenciesModel?> UpdateCurrency(CurrenciesModel model)
        {
            Currencies? updatedData = await dac.UpdateCurrency(mapper.Map<Currencies>(model));
            return mapper.Map<CurrenciesModel>(updatedData);
        }

        /// <summary>
        /// 刪除特定幣別
        /// </summary>
        /// <remarks>
        /// 此方法用於刪除指定的幣別記錄，根據提供的幣別 ID 進行操作。
        /// 刪除操作會永久性地移除該幣別，或可能將其標記為已刪除狀態，根據系統設計而定。
        /// 此操作應謹慎執行，因為刪除幣別可能會影響使用該幣別的交易記錄、貨幣轉換功能或報表。
        /// 執行此操作前應確保系統中不存在使用該幣別的活躍交易或資料。
        /// </remarks>
        /// <param name="currency_id">要刪除的幣別的唯一識別碼</param>
        /// <returns>刪除操作的結果，成功則返回 true，失敗則返回 false</returns>
        public async Task<bool> DeleteCurrency(Guid currency_id)
        {
            return await dac.DeleteCurrency(currency_id);
        }
    }
}
