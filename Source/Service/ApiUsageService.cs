using AutoMapper;
using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// API 使用紀錄服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IApiUsageService 介面，提供 API 使用紀錄相關的資料存取和處理功能。
    /// 貼賴資料存取層進行實際的資料庫操作，並使用 AutoMapper 轉換資料模型。
    /// 此服務管理 API 使用記錄，包含查詢、取得與新增 API 已使用紀錄功能。
    /// API 使用紀錄可用於計費、分析、安全審核和效能監控等目的。
    /// </remarks>
    public class ApiUsageService : IApiUsageService
    {
        /// <summary>
        /// API 使用紀錄資料存取組件
        /// </summary>
        private readonly IApiUsageDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化 API 使用紀錄服務
        /// </summary>
        /// <param name="dac">API 使用紀錄資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public ApiUsageService(IApiUsageDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得 API 使用紀錄清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的查詢條件取得符合條件的 API 使用紀錄清單。
        /// 可依據多種條件進行查詢，如租戶、API 識別碼、時間範圍等。
        /// 使用 DAC 層進行實際的資料庫查詢，并通過 AutoMapper 將結果轉換成服務層模型。
        /// 此方法適用於報表及管理介面，提供 API 使用情況的概覽。
        /// </remarks>
        /// <param name="model">包含查詢條件的 API 使用模型，如 tenant_id、api_id 等</param>
        /// <returns>符合條件的 API 使用紀錄清單</returns>
        public async Task<IList<ApiUsageModel>> GetApiUsageDataList(ApiUsageModel model)
        {
            // 取得API使用紀錄清單 
            IList<ApiUsage> result = await dac.GetApiUsageDataList(model);

            return mapper.Map<IList<ApiUsageModel>>(result);
        }
        /// <summary>
        /// 取得指定的 API 使用紀錄詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法根據指定的使用紀錄 ID 取得特定 API 使用紀錄的詳細資料。
        /// 用於查看單筆 API 調用的完整詳細資訊，如調用參數、回應內容、處理時間等。
        /// 使用 DAC 層進行資料庫存取，並通過 AutoMapper 轉換結果成服務層模型。
        /// 適用於需要查詢單筆 API 調用的場景，如除錯調查、效能分析或客戶支援。
        /// </remarks>
        /// <param name="usage_id">API 使用紀錄的唯一識別碼</param>
        /// <returns>包含 API 使用紀錄資訊的模型，如果找不到會返回可能為空的模型</returns>
        public async Task<ApiUsageModel> GetApiUsageData(Guid usage_id)
        {
            // 取得API使用紀錄
            ApiUsage? result = await dac.GetApiUsageData(usage_id);

            return mapper.Map<ApiUsageModel>(result);
        }

        /// <summary>
        /// 新增 API 使用紀錄
        /// </summary>
        /// <remarks>
        /// 此方法用於建立新的 API 使用紀錄，記錄 API 調用的相關資訊。
        /// 通常由系統自動調用，作為 API 監控和記錄機制的一部分。
        /// 新增的資料包含 API 調用的相關資訊，如調用時間、調用者、調用的 API、參數、回應狀態等。
        /// 此資料可用於系統監控、計費、報表和效能分析等用途。
        /// </remarks>
        /// <param name="model">包含 API 使用紀錄資料的模型，如調用時間、API ID、參數、回應等</param>
        /// <returns>新增後的 API 使用紀錄模型，包含系統產生的 ID</returns>
        public async Task<ApiUsageModel> CreateApiUsageData(ApiUsageModel model)
        {
            // 新增API使用紀錄
            ApiUsage? result = await dac.CreateApiUsageData(model);

            return mapper.Map<ApiUsageModel>(result);
        }
    }
}
