using AutoMapper;
using Paas.Dac;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 使用者管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IUsersService 介面，提供使用者相關的管理功能。
    /// 負責處理使用者的建立、查詢、更新和刪除等操作，是系統使用者管理的核心元件。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// 管理多租戶環境中的使用者帳號、權限和資料，確保系統的安全性和用戶隔離。
    /// </remarks>
    public class UsersService : IUsersService
    {
        /// <summary>
        /// 使用者資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責使用者相關的資料庫操作，如查詢、新增、更新和刪除等。
        /// </remarks>
        private readonly IUsersDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化使用者管理服務
        /// </summary>
        /// <param name="dac">使用者資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public UsersService(IUsersDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得使用者清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的查詢條件取得符合條件的使用者清單。
        /// 支援各種查詢條件，如使用者 ID、書號、電子郵件、租戶 ID 等。
        /// 使用 DAC 層進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於管理介面的使用者管理台、使用者搜尋和報表功能等。
        /// 在多租戶環境中，此方法可根據租戶 ID 限制查詢範圍，確保系統安全性。
        /// </remarks>
        /// <param name="model">包含查詢條件的使用者模型，如 user_id、username、email、tenant_id 等</param>
        /// <returns>符合條件的使用者清單</returns>
        public async Task<IList<UsersModel>> GetUsersList(UsersModel model)
        {
            // 新增使用者資料
            IList<Users> result = await dac.GetUsersList(model);

            return mapper.Map<IList<UsersModel>>(result);
        }

        /// <summary>
        /// 取得特定使用者的詳細資訊
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的使用者 ID 取得特定使用者的詳細資訊。
        /// 返回的資料包含使用者的完整個人資訊，如使用者名稱、電子郵件、角色、權限和相關的租戶資訊等。
        /// 使用 DAC 層的 GetUsersDataById 方法進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於使用者詳細資訊頁面、個人資料編輯、權限驗證等情境。
        /// 在多租戶環境中，應確保調用方有權限存取特定使用者的資料。
        /// </remarks>
        /// <param name="user_id">要查詢的使用者的唯一識別碼</param>
        /// <returns>特定使用者的詳細資訊模型，如果找不到則返回空模型</returns>
        public async Task<UsersModel> GetUsersData(Guid user_id)
        {
            // 取得特定使用者資料
            Users? result = await dac.GetUsersDataById(user_id);

            return mapper.Map<UsersModel>(result);
        }

        /// <summary>
        /// 建立新的使用者
        /// </summary>
        /// <remarks>
        /// 此方法用於在系統中建立新的使用者記錄，使用提供的模型作為資料來源。
        /// 在建立過程中會產生唯一的使用者 ID，並設定使用者的必要屬性，如使用者名稱、電子郵件、預設角色等。
        /// 此操作會驗證使用者資料的有效性，如電子郵件格式、使用者名稱的唯一性等。
        /// 在多租戶環境中，新使用者通常會關聯到特定的租戶，並根據租戶的權限設定給予相應的存取等級。
        /// </remarks>
        /// <param name="model">包含新使用者資料的模型，如使用者名稱、電子郵件、角色和租戶 ID 等</param>
        /// <returns>建立成功後的使用者模型，包含系統生成的 ID 和相關屬性</returns>
        public async Task<UsersModel> CreateUsers(UsersModel model)
        {
            // 新增使用者資料
            Users? result = await dac.CreateUsers(model);

            return mapper.Map<UsersModel>(result);
        }

        /// <summary>
        /// 更新使用者資料
        /// </summary>
        /// <remarks>
        /// 此方法用於更新現有使用者的資料，使用提供的模型作為資料來源。
        /// 更新過程會驗證使用者的存在性和所有必要欄位的有效性。
        /// 可更新的屬性包括使用者的基本資料、聯絡資訊、權限設定等，但通常不會修改使用者的唯一 ID 或其所屬租戶。
        /// 在多租戶環境中，應確保執行更新操作的用戶具備存取該使用者資料的適當權限。
        /// 更新操作可能會觸發相關事件，如通知、日誌記錄或權限重新計算等。
        /// </remarks>
        /// <param name="model">包含要更新的使用者資料的模型，必需包含使用者 ID</param>
        /// <returns>更新後的使用者模型，包含最新的資料狀態</returns>
        public async Task<UsersModel> UpdateUsers(UsersModel model)
        {
            // 更新使用者資料
            Users? result = await dac.UpdateUsers(model);

            return mapper.Map<UsersModel>(result);
        }

        /// <summary>
        /// 刪除使用者資料
        /// </summary>
        /// <remarks>
        /// 此方法用於刪除指定的使用者記錄，根據提供的使用者 ID 進行操作。
        /// 刪除操作可能會永久性地移除該使用者，或將其標記為停用狀態，根據系統設計而定。
        /// 此操作應謹慎執行，因為刪除使用者可能會影響相關的數據存取權限、已完成的操作記錄和其他系統功能。
        /// 在多租戶環境中，應限制只有特定角色（如租戶管理員或系統管理員）可以執行此操作。
        /// 執行刪除使用者操作後，應考慮轉移或備份該使用者的相關數據。
        /// </remarks>
        /// <param name="user_id">要刪除的使用者的唯一識別碼</param>
        /// <returns>刪除操作的結果，成功則返回 true，失敗則返回 false</returns>
        public async Task<bool> DeleteUsers(Guid user_id)
        {
            return await dac.DeleteUsers(user_id);
        }
    }
}
