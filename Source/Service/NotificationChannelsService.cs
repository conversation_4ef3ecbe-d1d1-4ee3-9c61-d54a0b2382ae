using AutoMapper;
using Microsoft.AspNetCore.Http.Connections;
using Paas.Dac;
using Paas.Model;
using System.Web;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Paas.Service
{
    /// <summary>
    /// 通知箤道管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 INotificationChannelsService 介面，提供通知箤道相關的管理功能。
    /// 負責處理通知箤道的建立、查詢、更新和刪除等操作，是系統通知傳送機制的核心元件。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// 支援多種通知箤道，如電子郵件、簡訊、應用程式推送、即時通訊等，為系統提供靈活的通知發送方式。
    /// </remarks>
    public class NotificationChannelsService : INotificationChannelsService
    {
        /// <summary>
        /// 通知箤道資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責通知箤道相關的資料庫操作，如查詢、新增、更新和刪除通知箤道設定等。
        /// </remarks>
        private readonly INotificationChannelsDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化通知箤道管理服務
        /// </summary>
        /// <param name="dac">通知箤道資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public NotificationChannelsService(INotificationChannelsDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得通知箤道設定清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的查詢條件取得符合條件的通知箤道設定清單。
        /// 支援各種查詢條件，如箤道 ID、箤道類型、狀態等。
        /// 使用 DAC 層進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於系統設定介面、通知箤道管理頁面等情境。
        /// 在多租戶環境中，此方法可能會根據租戶識別碼取得該租戶的通知箤道設定。
        /// </remarks>
        /// <param name="model">包含查詢條件的通知箤道模型，如 channel_id、type、status 等</param>
        /// <returns>符合條件的通知箤道設定清單</returns>
        public async Task<IList<NotificationChannelsModel>> GetNotificationChannels(NotificationChannelsModel model)
        {
            IList<NotificationChannels> data = await dac.GetNotificationChannels(mapper.Map<NotificationChannels>(model));
            return mapper.Map<IList<NotificationChannelsModel>>(data);
        }

        /// <summary>
        /// 新增通知箤道設定
        /// </summary>
        /// <remarks>
        /// 此方法用於在系統中建立新的通知箤道設定，使用提供的模型作為資料來源。
        /// 在建立過程中會產生唯一的箤道 ID，並設定箤道的必要屬性，如箤道類型、名稱、設定參數和驗證資訊等。
        /// 不同類型的通知箤道需要不同的設定參數，如電子郵件箤道需要 SMTP 伺服器設定，簡訊箤道需要 SMS 網關設定等。
        /// 適用於系統初始化、新增通知傳送方式或租戶特定通知箤道的設定等情境。
        /// </remarks>
        /// <param name="model">包含新通知箤道設定的模型，如箤道類型、名稱、設定參數等</param>
        /// <returns>建立成功後的通知箤道模型，包含系統生成的 ID 和相關屬性，如果失敗則返回 null</returns>
        public async Task<NotificationChannelsModel?> InsertNotificationChannel(NotificationChannelsModel model)
        {
            NotificationChannels? insertedData = await dac.InsertNotificationChannel(mapper.Map<NotificationChannels>(model));
            return mapper.Map<NotificationChannelsModel>(insertedData);
        }

        /// <summary>
        /// 更新特定通知箤道設定
        /// </summary>
        /// <remarks>
        /// 此方法用於更新現有通知箤道的設定，使用提供的模型作為資料來源。
        /// 更新過程會驗證箤道的存在性和所有必要欄位的有效性。
        /// 可更新的屬性包括箤道的名稱、狀態、設定參數和驗證資訊等，但通常不會修改箤道的類型和唯一 ID。
        /// 更新通知箤道設定可能會立即影響通過該箤道發送的通知，應謹慎執行並確保設定的正確性。
        /// 適用於調整通知箤道設定、更新驗證應用程式密鑰、修改箤道狀態等情境。
        /// </remarks>
        /// <param name="model">包含要更新的通知箤道設定的模型，必需包含箤道 ID</param>
        /// <returns>更新後的通知箤道模型，包含最新的設定狀態，如果失敗則返回 null</returns>
        public async Task<NotificationChannelsModel?> UpdateNotificationChannel(NotificationChannelsModel model)
        {
            NotificationChannels? updatedData = await dac.UpdateNotificationChannel(mapper.Map<NotificationChannels>(model));
            return mapper.Map<NotificationChannelsModel>(updatedData);
        }

        /// <summary>
        /// 刪除特定通知箤道設定
        /// </summary>
        /// <remarks>
        /// 此方法用於刪除指定的通知箤道設定，根據提供的箤道 ID 進行操作。
        /// 刪除操作會永久性地移除該通知箤道設定，或將其標記為已刪除狀態，根據系統設計而定。
        /// 此操作應謹慎執行，因為刪除通知箤道設定後，系統將無法透過該箤道發送通知，可能導致使用者無法收到重要通知。
        /// 在執行刪除操作前，應確保已設置其他替代的通知箤道，或該箤道確實已不再使用。
        /// </remarks>
        /// <param name="channelId">要刪除的通知箤道的唯一識別碼</param>
        /// <returns>刪除操作的結果，成功則返回 true，失敗則返回 false</returns>
        public async Task<bool> DeleteNotificationChannel(Guid channelId)
        {
            return await dac.DeleteNotificationChannel(channelId);
        }
    }
}
