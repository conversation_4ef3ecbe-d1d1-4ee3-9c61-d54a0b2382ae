using AutoMapper;
using Microsoft.AspNetCore.Http.Connections;
using Newtonsoft.Json;
using Paas.Dac;
using Paas.Model;
using System.Web;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Paas.Service
{
    /// <summary>
    /// 通知管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 INotificationsService 介面，提供通知訊息相關的管理功能。
    /// 負責處理通知的建立、查詢和管理等操作，是系統通知功能的核心元件。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// 支援多種通知類型和多種通知管道，例如系統通知、用戶提醒、安全警報等。
    /// </remarks>
    public class NotificationsService : INotificationsService
    {
        /// <summary>
        /// 通知資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責通知相關的資料庫操作，如查詢、新增通知等。
        /// </remarks>
        private readonly INotificationsDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化通知管理服務
        /// </summary>
        /// <param name="dac">通知資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public NotificationsService(INotificationsDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得通知清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的查詢條件取得符合條件的通知清單。
        /// 支援各種查詢條件，如通知 ID、接收者 ID、通知類型、狀態、時間範圍等。
        /// 使用 DAC 層進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於通知中心、通知歷史記錄、通知分析等情境。
        /// 多租戶環境中，此方法會根據用戶權限限制可查看的通知範圍。
        /// </remarks>
        /// <param name="model">包含查詢條件的通知模型，如 notification_id、user_id、type、status 等</param>
        /// <returns>符合條件的通知清單</returns>
        public async Task<IList<NotificationsModel>> GetNotifications(NotificationsModel model)
        {
            IList<Notifications> data = await dac.GetNotifications(mapper.Map<Notifications>(model));
            return mapper.Map<IList<NotificationsModel>>(data);
        }

        /// <summary>
        /// 取得特定通知的詳細資訊
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的通知 ID 取得特定通知的詳細資訊。
        /// 返回的資料包含通知的完整詳細資料，如標題、內容、發送時間、接收者、狀態、種類等。
        /// 使用 DAC 層進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於通知詳細資訊頁面、通知詳情彈窗、通知編輯功能等情境。
        /// 在多租戶環境中，應確保用戶有權限存取該通知。
        /// </remarks>
        /// <param name="notificationId">要查詢的通知的唯一識別碼</param>
        /// <returns>特定通知的詳細資訊模型，如果找不到則返回 null</returns>
        public async Task<NotificationsModel?> GetNotification(Guid notificationId)
        {
            Notifications? data = await dac.GetNotification(notificationId);
            return mapper.Map<NotificationsModel>(data);
        }

        /// <summary>
        /// 建立新的通知
        /// </summary>
        /// <remarks>
        /// 此方法用於在系統中建立新的通知記錄，使用提供的模型作為資料來源。
        /// 在建立過程中會產生唯一的通知 ID，並設定通知的必要屬性，如標題、內容、接收者、發送時間等。
        /// 新建立的通知可能會根據設定的通知管道自動發送，如電子郵件、網站內提醒、手機推播等。
        /// 適用於系統觸發的自動通知、管理員發送的公告、使用者間的訊息等情境。
        /// </remarks>
        /// <param name="model">包含新通知資料的模型，如標題、內容、接收者、通知種類等</param>
        /// <returns>建立成功後的通知模型，包含系統生成的 ID 和相關屬性，如果失敗則返回 null</returns>
        public async Task<NotificationsModel?> InsertNotification(NotificationsModel model)
        {
            Notifications? data = await dac.InsertNotifications(mapper.Map<Notifications>(model));

            return mapper.Map<NotificationsModel>(data);
        }
    }
}
