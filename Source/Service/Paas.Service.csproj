﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Class1.cs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Paas.Common.csproj" />
    <ProjectReference Include="..\Model\Paas.Model.csproj" />
    <ProjectReference Include="..\Repository\Paas.Dac.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

</Project>
