using AutoMapper;
using Paas.Dac;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 監控指標管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IMonitoringMetricsService 介面，提供系統監控指標相關的管理功能。
    /// 負責處理監控指標的查詢和分析等操作，是系統效能監控和健康狀態分析的核心元件。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// 管理系統各種效能和運營指標，如資源使用率、響應時間、失敗率、API 調用量等，為系統監控和效能優化提供支持。
    /// </remarks>
    public class MonitoringMetricsService : IMonitoringMetricsService
    {
        /// <summary>
        /// 監控指標資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責監控指標相關的資料庫操作，如查詢指標資料等。
        /// </remarks>
        private readonly IMonitoringMetricsDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化監控指標管理服務
        /// </summary>
        /// <param name="dac">監控指標資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public MonitoringMetricsService(IMonitoringMetricsDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得監控指標資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的查詢條件取得符合條件的監控指標資料清單。
        /// 支援各種查詢條件，如指標類型、監控目標、時間範圍、系統元件、資源群組等。
        /// 使用 DAC 層進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於監控儀表板、效能分析報表、系統健康狀態監控、資源使用率分析等情境。
        /// 在多租戶環境中，可依據租戶 ID 取得特定租戶的監控指標，或以管理者權限查看整體系統指標。
        /// </remarks>
        /// <param name="model">包含查詢條件的監控指標模型，如指標類型、時間範圍、資源群組等</param>
        /// <returns>符合條件的監控指標清單</returns>
        public async Task<IList<MonitoringMetricsModel>> GetMetricsDataList(MonitoringMetricsModel model)
        {
            IList<MetricsData> result = await dac.GetMetricsDataList(model);

            return mapper.Map<IList<MonitoringMetricsModel>>(result);
        }


        /// <summary>
        /// 取得特定監控指標的詳細資訊
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的指標 ID 取得特定監控指標的詳細資訊。
        /// 返回的資料包含指標的完整詳細資料，如指標名稱、種類、監控目標、數值、單位、時間戳記、相關資源、歷史趋勢等。
        /// 使用 DAC 層的 GetMetricsDataById 方法進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於指標詳細資訊頁面、特定指標趋勢分析、警報閾值詳細說明等情境。
        /// 此方法對於深入分析特定系統指標的表現和健康狀態特別有用。
        /// </remarks>
        /// <param name="metric_id">要查詢的監控指標的唯一識別碼</param>
        /// <returns>特定監控指標的詳細資訊模型，如果找不到則返回 null</returns>
        public async Task<MonitoringMetricsModel?> GetMetricsDataById(Guid metric_id)
        {
            MetricsData? result = await dac.GetMetricsDataById(metric_id);

            return mapper.Map<MonitoringMetricsModel>(result);
        }
    }
}
