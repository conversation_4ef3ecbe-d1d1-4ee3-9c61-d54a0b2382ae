using AutoMapper;
using Paas.Dac;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 角色管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IRolesService 介面，提供角色相關的管理功能。
    /// 負責處理系統角色的建立和查詢等操作，是系統基於角色的存取控制機制的核心元件。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// 與權限管理服務密切合作，為多租戶環境中的資源存取和功能使用模型提供支持。
    /// </remarks>
    public class RolesService : IRolesService
    {
        /// <summary>
        /// 角色資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責角色相關的資料庫操作，如查詢、建立角色等。
        /// </remarks>
        private readonly IRolesDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化角色管理服務
        /// </summary>
        /// <param name="dac">角色資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public RolesService(IRolesDac dac, IMapper mapper) 
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得所有角色資料清單
        /// </summary>
        /// <remarks>
        /// 此方法用於取得系統中定義的所有角色資料列表。
        /// 返回的資料包含每個角色的完整詳細資料，如角色名稱、描述、屬性和其他相關資訊。
        /// 使用 DAC 層進行資料庫查詢，並透過 AutoMapper 轉換為服務層模型。
        /// 適用於系統角色管理介面、使用者角色分配、預設角色選擇等情境。
        /// 在多租戶環境中，此方法將根據用戶權限取得適當的角色記錄範圍。
        /// </remarks>
        /// <returns>系統中定義的所有角色清單</returns>
        public async Task<IList<RolesModel>> GetRolesList()
        {
            IList<Roles> result = await dac.GetRolesList();

            return mapper.Map<IList<RolesModel>>(result);
        }

        /// <summary>
        /// 建立新的角色資料
        /// </summary>
        /// <remarks>
        /// 此方法用於在系統中建立新的角色記錄，使用提供的模型作為資料來源。
        /// 在建立過程中會產生唯一的角色 ID，並設定該角色的必要屬性，如角色名稱、描述等。
        /// 新建立的角色定義了用戶在系統中的操作權限範圍，是基於角色的存取控制的基礎。
        /// 適用於系統初始化或升級過程中的角色定義、定制化租戶角色等情境。
        /// 新增角色後，通常需要透過權限管理服務為其分配具體的權限。
        /// </remarks>
        /// <param name="model">包含新角色資料的模型，如角色名稱、描述和其他相關屬性</param>
        /// <returns>建立成功後的角色模型，包含系統生成的 ID 和相關屬性</returns>
        public async Task<RolesModel?> CreateRolesData(RolesModel model)
        {
            Roles? result = await dac.CreateRolesData(model);

            return mapper.Map<RolesModel>(result);
        }
    }
}
