using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// API 服務管理的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與 API 服務相關的操作，包括查詢、創建、更新和狀態管理等功能。
    /// 此服務負責管理系統中所有的 API 服務定義，包含每個 API 的詳細資訊、授權和限制等設定。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// 此介面應通過相依性注入在控制器或其他服務中使用。
    /// </remarks>
    public interface IApisService
    {
        /// <summary>
        /// 根據指定的查詢條件取得 API 服務資料清單
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的查詢條件取得符合條件的 API 服務清單。
        /// 支援基本的篩選和分頁功能，可用於管理介面或報表系統。
        /// 結果包含 API 服務的基本資訊，如名稱、版本、速率限制和狀態等。
        /// 可根據租戶、狀態或其他條件進行篩選，以取得符合特定需求的 API 服務資料。
        /// </remarks>
        /// <param name="model">包含查詢條件的 API 模型，如 tenant_id、is_active 等查詢參數</param>
        /// <returns>符合條件的 API 服務資料清單</returns>
        Task<List<ApisModel>> GetApisDataList(ApisModel model);

        /// <summary>
        /// 根據 API 識別碼取得特定 API 服務的詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的 API 識別碼取得特定 API 服務的詳細資料。
        /// 在查看或編輯 API 服務資料時使用，管理介面或詳情頁面顯示的主要方法。
        /// 結果包含 API 服務的完整資訊，包括名稱、描述、版本、速率限制和狀態等。
        /// 如果指定的 API ID 不存在，則返回 null。API ID 為空時也返回 null。
        /// </remarks>
        /// <param name="api_id">要查詢的 API 服務的唯一識別碼，可為 null</param>
        /// <returns>包含 API 服務詳細資料的模型，若找不到或 ID 為空則返回 null</returns>
        Task<ApisModel?> GetApisData(Guid? api_id);

        /// <summary>
        /// 創建新的 API 服務資料
        /// </summary>
        /// <remarks>
        /// 此方法會在系統中創建一個新的 API 服務記錄，使用提供的模型作為資料來源。
        /// 在創建過程中會驗證必要欄位，並生成唯一的 API 識別碼。
        /// 新創建的 API 服務預設為啟用狀態，並設定建立的時間戳。
        /// 此方法應進行安全驗證，確保只有授權的管理員或租戶管理員可以創建 API 服務。
        /// </remarks>
        /// <param name="model">包含新 API 服務資料的模型，應包含租戶 ID、API 名稱、描述、版本和速率限制等資訊</param>
        /// <returns>創建成功後的 API 服務模型，包含新生成的 API ID 和其他系統設定的欄位</returns>
        Task<ApisModel> CreateApisData(ApisModel model);

        /// <summary>
        /// 更新現有 API 服務的資料
        /// </summary>
        /// <remarks>
        /// 此方法會更新現有 API 服務的資料，使用提供的模型作為資料來源。
        /// 更新過程中會驗證 API 服務的存在性和必要欄位，並更新修改時間戳。
        /// 可更新的資訊包含 API 名稱、描述、版本和速率限制等，但不會更改 API 的唯一識別碼和所屬租戶。
        /// 專屬的狀態更新應使用 UpdateApisStatus 方法進行。
        /// </remarks>
        /// <param name="model">包含更新資料的 API 服務模型，必須包含要更新的 API ID</param>
        /// <returns>更新成功後的 API 服務模型，包含最新的資料狀態</returns>
        Task<ApisModel> UpdateApisData(ApisModel model);

        /// <summary>
        /// 切換 API 服務的啟用/停用狀態
        /// </summary>
        /// <remarks>
        /// 此方法會切換指定 API 服務的啟用/停用狀態，如果當前是啟用狀態則停用，反之亭然。
        /// 此方法專限於狀態切換，不會改變 API 服務的其他資訊。
        /// 停用 API 服務將導致相關的 API 請求被拒絕，相關的 API 金鑰也可能無法使用。
        /// 此操作應謹慎然使用，特別是處於生產環境中的 API 服務，可能需要額外的權限驗證。
        /// </remarks>
        /// <param name="api_id">要切換狀態的 API 服務唯一識別碼</param>
        /// <returns>切換狀態的結果，成功則返回 true，失敗則返回 false</returns>
        Task<bool> UpdateApisStatus(Guid api_id);
    }
}
