using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 監控指標服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與監控指標相關的操作，包括查詢和分析系統效能指標等功能。
    /// 此服務負責管理系統的各種監控指標，如 API 響應時間、系統資源使用率、錯誤率和使用者面臨的活動等。
    /// 這些指標對於系統管理員認識系統的運作狀態、識別潛在問題和改善系統效能非常重要。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// </remarks>
    public interface IMonitoringMetricsService
    {

        /// <summary>
        /// 根據查詢條件取得監控指標清單
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的查詢條件取得符合條件的監控指標清單。
        /// 支援多種篩選條件，如租戶 ID、指標類型、時間範圍和服務名稱等。
        /// 此方法常用於呈現系統效能儀表板、監控工具或報表系統，提供關於系統運行狀況的全面視圖。
        /// 返回的指標數據可用於分析系統調用率、效能跟蹤、資源使用模式與效率分析等各種用途。
        /// 各種指標可能包括位端測量、負載指標、錯誤率、反應時間和客戶端使用率等。
        /// </remarks>
        /// <param name="model">包含查詢條件的監控指標模型，如 tenant_id、metric_type、date_range 等查詢參數</param>
        /// <returns>符合條件的監控指標清單</returns>
        Task<IList<MonitoringMetricsModel>> GetMetricsDataList(MonitoringMetricsModel model);

        /// <summary>
        /// 根據指標 ID 取得特定監控指標的詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的指標 ID 取得特定監控指標的詳細資料。
        /// 在需要查看特定指標的詳細資訊時使用，如時間軸數據、歷史趨勢或深入分析工具。
        /// 結果包含監控指標的完整資訊，如指標 ID、指標名稱、指標類型、測量值、時間戳、離散區間和其他相關屬性。
        /// 若指定的指標 ID 不存在，則返回 null。
        /// 這些詳細的指標資料對於測訊系統效能、識別潛在問題和改善指標至關重要。
        /// </remarks>
        /// <param name="metric_id">要查詢的監控指標的唯一識別碼</param>
        /// <returns>包含監控指標詳細資料的模型，若找不到則返回 null</returns>
        Task<MonitoringMetricsModel?> GetMetricsDataById(Guid metric_id);
    }
}
