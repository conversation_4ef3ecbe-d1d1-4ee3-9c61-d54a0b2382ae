using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// JWT 身分驗證服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與 JSON Web Token (JWT) 相關的操作，用於安全身分驗證和授權。
    /// JWT 是一種開放標準（RFC 7519），定義了一種緊湊的、自包含的方式，用於雙方之間以 JSON 物件安全地傳輸資訊。
    /// 此服務負責生成和驗證 JWT 令牌，以實現系統中的使用者身分驗證和授權機制。
    /// 由於 JWT 與系統安全直接相關，因此此介面的實現應特別考慮安全性和可靠性，並避免可能的安全漏洞。
    /// </remarks>
    public interface IJwtService
    {
        /// <summary>
        /// 根據用戶名稱生成 JWT 身分驗證令牌
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的用戶名稱生成一個新的 JWT 令牌，用於後續的身分驗證和授權。
        /// 生成的令牌包含三個部分：頭部（Header）、負載（Payload）和簽名（Signature）。
        /// 負載部分包含用戶身分資訊，如用戶名稱、角色和權限等定制資訊。
        /// 令牌驗證時間應根據系統安全要求適當設定，避免過長的有效期導致安全疑慮。
        /// 應確保用於簽名的密鑰安全存儲，且不暴露於客戶端。
        /// </remarks>
        /// <param name="username">要生成令牌的用戶名稱，用於識別用戶身分</param>
        /// <returns>生成的 JWT 令牌字串，用於後續的身分驗證和授權</returns>
        string GenerateToken(string username);
    }
}
