using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 身分驗證服務介面
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與身分驗證相關的操作，包括使用者登入驗證等功能。
    /// 提供安全的使用者認證機制，支援密碼驗證和使用者資料查詢。
    /// 所有方法都返回 Task 物件，以支援非同步操作和更高的系統可擴展性。
    /// 此服務是系統安全模型的重要組成部分，確保只有合法使用者能夠存取系統資源。
    /// </remarks>
    public interface IAuthService
    {
        /// <summary>
        /// 驗證使用者登入憑證
        /// </summary>
        /// <remarks>
        /// 此方法會驗證提供的電子郵件和密碼是否正確。
        /// 使用安全的密碼比對機制，支援 BCrypt 等業界標準的密碼雜湊算法。
        /// 驗證成功時返回完整的使用者資訊，包括角色和權限等相關資料。
        /// 驗證失敗時返回 null，調用方應適當處理並記錄安全事件。
        /// 此方法應包含適當的安全措施，如防止暴力破解攻擊等。
        /// </remarks>
        /// <param name="email">使用者的電子郵件地址，用作登入帳號</param>
        /// <param name="password">使用者的原始密碼（明文）</param>
        /// <returns>驗證成功時返回使用者模型，失敗時返回 null</returns>
        Task<UsersModel?> ValidateUserAsync(string email, string password);

        /// <summary>
        /// 根據電子郵件查詢使用者資料
        /// </summary>
        /// <remarks>
        /// 此方法根據電子郵件地址查詢使用者的完整資料。
        /// 主要用於登入流程中的使用者查詢和身分驗證。
        /// 返回的資料包含使用者的基本資訊、角色和相關的租戶資訊等。
        /// 如果找不到對應的使用者，則返回 null。
        /// </remarks>
        /// <param name="email">要查詢的使用者電子郵件地址</param>
        /// <returns>找到的使用者模型，如果不存在則返回 null</returns>
        Task<UsersModel?> GetUserByEmailAsync(string email);
    }
}
