using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// API 使用紀錄服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中的 API 使用紀錄資料的管理操作，包括查詢和新增 API 使用紀錄等功能。
    /// 此服務用於追蹤和分析 API 的使用情況，以監控系統效能、驗證客戶端使用行為和計費目的。
    /// 透過收集和分析 API 使用數據，可以協助系統管理員貓測使用模式，偏好記錄和可能的效能問題。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// </remarks>
    public interface IApiUsageService
    {
        /// <summary>
        /// 根據查詢條件取得 API 使用紀錄清單
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的查詢條件取得符合條件的 API 使用紀錄清單。
        /// 支援多種篩選條件，如租戶 ID、API 識別碼、使用者 ID、時間範圍和狀態等。
        /// 此方法用於管理介面或報表功能，提供 API 使用狀況的概覽。
        /// 返回的結果包含 API 調用的組件東西資訊，如調用時間、調用者、調用的 API、回應狀態碼和處理時間等。
        /// </remarks>
        /// <param name="model">包含查詢條件的 API 使用紀錄模型，如 tenant_id、api_id、日期範圍等</param>
        /// <returns>符合條件的 API 使用紀錄清單</returns>
        Task<IList<ApiUsageModel>> GetApiUsageDataList(ApiUsageModel model);

        /// <summary>
        /// 根據使用紀錄 ID 取得特定 API 使用紀錄的詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的使用紀錄 ID 取得特定 API 使用紀錄的詳細資料。
        /// 用於查看特定 API 調用的完整詳細資訊，如數據分析、失敗診斷或安全審核等。
        /// 返回的結果包含調用的完整詳細資訊，如調用時間、調用者、調用參數、回應內容、效能指標等。
        /// 返回資訊中的敏感數據可能需要適當地做資訊安全處理，確保其符合資料保護和安全政策。
        /// </remarks>
        /// <param name="usage_id">要查詢的 API 使用紀錄的唯一識別碼</param>
        /// <returns>包含 API 使用紀錄詳細資料的模型，若找不到則返回 null</returns>
        Task<ApiUsageModel> GetApiUsageData(Guid usage_id);

        /// <summary>
        /// 新增 API 使用紀錄
        /// </summary>
        /// <remarks>
        /// 此方法用於建立新的 API 使用紀錄，記錄 API 調用的相關資訊。
        /// 通常由系統自動調用，作為 API 監控和紀錄機制的一部分。
        /// 新增的資料包含 API 調用的相關資訊，如調用時間、調用者、調用的 API、調用參數、回應狀態碼和處理時間等。
        /// 此資料可用於系統監控、計費、定期報表和效能分析等用途。
        /// 在記錄資料時，應考慮所收集資料的敏感性以及適用的數據保護規定。
        /// </remarks>
        /// <param name="model">包含 API 使用紀錄資料的模型，如調用時間、API ID、調用參數、回應結果等</param>
        /// <returns>新增後的 API 使用紀錄模型，包含系統產生的 ID</returns>
        Task<ApiUsageModel> CreateApiUsageData(ApiUsageModel model);
    }
}
