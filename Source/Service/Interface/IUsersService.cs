using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 使用者管理服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與使用者相關的操作，包括查詢、創建、更新和刪除等功能。
    /// 此服務負責管理系統中的使用者資料，包含使用者的基本資訊、帳號、權限和身分驗證等功能。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// 由於使用者資料的敏感性，此介面中的所有操作都應進行安全驗證和存取控制，以確保個人資料的保護和系統的安全性。
    /// </remarks>
    public interface IUsersService
    {
        /// <summary>
        /// 根據指定的查詢條件取得使用者清單
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的查詢條件取得符合條件的使用者清單。
        /// 支援基本的篩選和分頁功能，可用於管理介面或報表系統。
        /// 結果包含使用者的基本資訊，如帳號、電子郵件、角色和啟用狀態等。
        /// 可根據租戶、狀態、角色或其他條件進行篩選，以取得符合特定需求的使用者資料。
        /// 根據安全上下文，此方法應限制只返回調用者有權存取的使用者清單，例如管理員可查看所有使用者，普通用戶只能查看自己所屬租戶的使用者。
        /// </remarks>
        /// <param name="model">包含查詢條件的使用者模型，如 tenant_id、role_id、is_active 等查詢參數</param>
        /// <returns>符合條件的使用者資料清單</returns>
        Task<IList<UsersModel>> GetUsersList(UsersModel model);

        /// <summary>
        /// 根據使用者 ID 取得特定使用者的詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的使用者識別碼取得特定使用者的詳細資料。
        /// 在查看或編輯使用者資料時使用，管理介面或用戶詳情頁面顯示的主要方法。
        /// 結果包含使用者的完整資訊，包括帳號、電子郵件、角色、所屬租戶和啟用狀態等。
        /// 出於安全考慮，密碼相關信息應予以充分保護，不應在查詢結果中包含密碼原文或雜湊值。
        /// 如果指定的使用者 ID 不存在，則應返回適當的空值或拋出異常。
        /// 此方法應進行安全驗證，確保調用者有權存取所請求的使用者資料。
        /// </remarks>
        /// <param name="user_id">要查詢的使用者的唯一識別碼</param>
        /// <returns>包含使用者詳細資料的模型，若找不到則返回適當的空值</returns>
        Task<UsersModel> GetUsersData(Guid user_id);

        /// <summary>
        /// 創建新的使用者帳號
        /// </summary>
        /// <remarks>
        /// 此方法會在系統中創建一個新的使用者記錄，使用提供的模型作為資料來源。
        /// 在創建過程中會驗證必要欄位，如帳號、電子郵件和密碼等，並生成唯一的使用者識別碼。
        /// 新創建的使用者預設為啟用狀態，並會指定相應的角色和所屬租戶。
        /// 密碼資訊應適當加密存儲，不可存儲明文密碼，建議使用業界標準的加密演算法（如 bcrypt 或 PBKDF2）。
        /// 創建帳號後，系統可能需要發送歡迎通知或帳號啟用確認郵件等後續操作。
        /// </remarks>
        /// <param name="model">包含新使用者資料的模型，應包含租戶 ID、帳號、電子郵件、密碼和角色 ID 等資訊</param>
        /// <returns>創建成功後的使用者模型，包含新生成的使用者 ID 和其他系統設定的欄位</returns>
        Task<UsersModel> CreateUsers(UsersModel model);

        /// <summary>
        /// 更新現有使用者的資料
        /// </summary>
        /// <remarks>
        /// 此方法會更新現有使用者的資料，使用提供的模型作為資料來源。
        /// 更新過程中會驗證使用者的存在性和必要欄位，並更新修改時間戳。
        /// 可更新的資訊包含使用者的電子郵件、角色、密碼和狀態等，但不會更改使用者的唯一識別碼和帳號名稱。
        /// 如果更新密碼，應進行額外的驗證並適當加密新密碼，不可存儲明文密碼。
        /// 重要的資料更新（如角色變更、密碼重置）應記錄安全日誌，以便後續審核和監控。
        /// </remarks>
        /// <param name="model">包含更新資料的使用者模型，必須包含要更新的使用者 ID</param>
        /// <returns>更新成功後的使用者模型，包含最新的資料狀態</returns>
        Task<UsersModel> UpdateUsers(UsersModel model);

        /// <summary>
        /// 刪除指定的使用者帳號
        /// </summary>
        /// <remarks>
        /// 此方法會刪除系統中指定的使用者記錄。
        /// 基於數據完整性和審計需求，此操作可能只是將使用者標記為停用或刪除狀態，而不是從資料庫中完全刪除記錄。
        /// 刪除使用者後，相關的權限、日誌和客戶數據應適當處理，以確保系統其他部分的正常運作。
        /// 此操作應記錄詳細的安全日誌，包括操作者、操作時間和被刪除的使用者信息等。
        /// 此方法應進行全面的權限驗證，確保只有具有足夠權限的管理員或租戶管理員可以刪除使用者。
        /// </remarks>
        /// <param name="user_id">要刪除的使用者的唯一識別碼</param>
        /// <returns>刪除操作的結果，成功則返回 true，失敗則返回 false</returns>
        Task<bool> DeleteUsers(Guid user_id);
    }
}
