using Paas.Model;

namespace Paas.Service
{
    /// <summary>
    /// API 金鑰管理服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與 API 金鑰相關的操作，包括查詢、創建、更新和刪除等功能。
    /// 此服務負責管理系統中的 API 金鑰，這些金鑰用於驗證和授權客戶端請求 API 的存取。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// API 金鑰的安全性至關重要，此介面中的所有操作都應進行偵測記錄和適當的權限驗證。
    /// </remarks>
    public interface IApiKeysService
    {
        /// <summary>
        /// 根據指定的查詢條件取得 API 金鑰清單
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的查詢條件取得符合條件的 API 金鑰清單。
        /// 支援基本的篩選和分頁功能，可用於管理介面或報表系統。
        /// 結果包含 API 金鑰的基本資訊，如租戶、金鑰字串、權限範圍和到期日期等。
        /// 可根據租戶、狀態或其他條件進行篩選，以取得符合特定需求的 API 金鑰資料。
        /// 根據使用者的權限，系統會限制查詢結果僅包含其有權存取的金鑰。
        /// </remarks>
        /// <param name="model">包含查詢條件的 API 金鑰模型，如 tenant_id、status 等查詢參數</param>
        /// <returns>符合條件的 API 金鑰資料清單</returns>
        Task<IList<ApiKeysModel>> GetApiKeys(ApiKeysModel model);
        /// <summary>
        /// 根據金鑰 ID 取得特定 API 金鑰的詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的金鑰識別碼取得特定 API 金鑰的詳細資料。
        /// 在查看或編輯 API 金鑰資料時使用，管理介面或詳情頁面顯示的主要方法。
        /// 結果包含 API 金鑰的完整資訊，包括租戶、金鑰字串、權限範圍、狀態和到期日期等。
        /// 如果指定的金鑰 ID 不存在，則返回 null。
        /// 此方法應進行權限驗證，確保只有授權的管理員或租戶可以存取特定 API 金鑰的資料。
        /// </remarks>
        /// <param name="keyId">要查詢的 API 金鑰的唯一識別碼</param>
        /// <returns>包含 API 金鑰詳細資料的模型，若找不到則返回 null</returns>
        Task<ApiKeysModel?> GetApiKey(Guid keyId);
        /// <summary>
        /// 創建新的 API 金鑰
        /// </summary>
        /// <remarks>
        /// 此方法會在系統中創建一個新的 API 金鑰記錄，使用提供的模型作為資料來源。
        /// 在創建過程中會驗證必要欄位，並生成唯一的 API 金鑰識別碼。
        /// 新創建的 API 金鑰預設為啟用狀態，可設定其到期日期、權限範圍等屬性。
        /// 創建 API 金鑰時需注意安全性，本方法應進行全面的權限驗證和安全性審核。
        /// 金鑰創建後必須妥善存儲，再此權限層級有可能進行額外的動作將金鑰密碼加密。
        /// </remarks>
        /// <param name="model">包含新 API 金鑰資料的模型，應包含租戶 ID、API 金鑰字串、權限範圍和到期日期等資訊</param>
        /// <returns>創建成功後的 API 金鑰模型，包含新生成的金鑰 ID 和其他系統設定的欄位，失敗則返回 null</returns>
        Task<ApiKeysModel?> InsertApiKey(ApiKeysModel model);
        /// <summary>
        /// 更新現有 API 金鑰的資料
        /// </summary>
        /// <remarks>
        /// 此方法會更新現有 API 金鑰的資料，使用提供的模型作為資料來源。
        /// 更新過程中會驗證 API 金鑰的存在性和必要欄位，並更新修改時間戳。
        /// 可更新的資訊包含 API 金鑰的狀態、到期日期和權限範圍等，但不能更改金鑰的唯一識別碼和金鑰字串本身。
        /// 出於安全考慮，更新 API 金鑰應記錄操作日誌，以便實施稿運行好安全稿記錄相關操作。
        /// 當更新失敗時，方法應回傳適當的錯誤訊息或返回 null。
        /// </remarks>
        /// <param name="model">包含更新資料的 API 金鑰模型，必須包含要更新的金鑰 ID</param>
        /// <returns>更新成功後的 API 金鑰模型，包含最新的資料狀態，失敗則返回 null</returns>
        Task<ApiKeysModel?> UpdateApiKey(ApiKeysModel model);
        /// <summary>
        /// 刪除指定的 API 金鑰
        /// </summary>
        /// <remarks>
        /// 此方法會刪除系統中指定的 API 金鑰記錄。
        /// 刪除操作可能會將記錄標記為刪除狀態，而不是從資料庫中完全刪除，以便維護完整的客戶使用歷史記錄。
        /// 刪除 API 金鑰會導致使用該金鑰的客戶端無法再存取 API，因此在刪除前應先通知相關客戶。
        /// 此方法應進行全面的權限驗證，確保只有授權的管理員或租戶可以刪除 API 金鑰。
        /// 操作完成時應記錄安全日誌，以便於後續的客戶服務與安全稽核。
        /// </remarks>
        /// <param name="keyId">要刪除的 API 金鑰的唯一識別碼</param>
        /// <returns>刪除操作的結果，成功則返回 true，失敗則返回 false</returns>
        Task<bool> DeleteApiKey(Guid keyId);
    }
}
