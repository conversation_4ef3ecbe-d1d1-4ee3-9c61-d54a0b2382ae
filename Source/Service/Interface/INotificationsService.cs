using Paas.Model;

namespace Paas.Service
{
    /// <summary>
    /// 系統通知服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與通知相關的操作，包括查詢和創建通知等功能。
    /// 此服務負責管理系統中的通知，包含通知的建立、發送和存取。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// 通知系統是改善用戶體驗和及時通知用戶重要事件的關鍵元件。
    /// </remarks>
    public interface INotificationsService
    {
        /// <summary>
        /// 根據指定的查詢條件取得通知清單
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的查詢條件取得符合條件的通知清單。
        /// 支援基本的篩選和分頁功能，可用於通知中心或用戶儀表板。
        /// 結果包含通知的基本資訊，如標題、內容、優先級、狀態和時間戳等。
        /// 可根據租戶、用戶、狀態或優先級進行篩選，以取得符合特定需求的通知清單。
        /// 根據安全上下文，此方法應限制只返回調用者有權存取的通知，例如一般用戶只能查看自己的通知。
        /// </remarks>
        /// <param name="model">包含查詢條件的通知模型，如 tenant_id、user_id、status、priority 等查詢參數</param>
        /// <returns>符合條件的通知資料清單</returns>
        Task<IList<NotificationsModel>> GetNotifications(NotificationsModel model);
        /// <summary>
        /// 根據通知 ID 取得特定通知的詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的通知識別碼取得特定通知的詳細資料。
        /// 在查看通知詳情時使用，通知中心或通知詳情頁面顯示的主要方法。
        /// 結果包含通知的完整資訊，包括標題、內容、發送者、接收者、時間戳和狀態等。
        /// 如果指定的通知 ID 不存在，則返回 null。
        /// 此方法應進行安全驗證，確保只有通知的發送者或接收者可以存取通知的詳細資料。
        /// </remarks>
        /// <param name="notificationId">要查詢的通知的唯一識別碼</param>
        /// <returns>包含通知詳細資料的模型，若找不到則返回 null</returns>
        Task<NotificationsModel?> GetNotification(Guid notificationId);
        /// <summary>
        /// 創建新的系統通知
        /// </summary>
        /// <remarks>
        /// 此方法會在系統中創建一個新的通知記錄，使用提供的模型作為資料來源。
        /// 在創建過程中會驗證必要欄位，如標題、內容和接收者等，並生成唯一的通知識別碼。
        /// 新創建的通知預設為未讀狀態，並設定建立的時間戳。
        /// 根據通知的類型和設定，系統可能需要通過不同的渠道發送通知，如電子郵件、簡訊或應用內推送等。
        /// 創建通知後，應適當更新相關的渠道狀態和發送記錄。
        /// </remarks>
        /// <param name="model">包含新通知資料的模型，應包含租戶 ID、標題、內容、接收者和優先級等資訊</param>
        /// <returns>創建成功後的通知模型，包含新生成的通知 ID 和其他系統設定的欄位，失敗則返回 null</returns>
        Task<NotificationsModel?> InsertNotification(NotificationsModel model);
    }
}
