using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 租戶管理服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與租戶相關的服務操作，包括查詢、創建、更新和狀態管理等功能。
    /// 此服務是系統跟蹤和管理租戶的主要介面，實現部分應依照相關安全規定進行權限驗證。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// 此介面應通過相依性注入在控制器或其他服務中使用。
    /// </remarks>
    public interface ITenantsService
    {
        /// <summary>
        /// 根據指定的查詢條件取得租戶資訊清單
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的查詢條件得到符合條件的租戶資訊清單。
        /// 支援基本的篩選和分頁功能，可用於管理介面或報表系統。
        /// 結果包含租戶的基本信息，如名稱、聯絡方式、帳單幣別和狀態等。
        /// </remarks>
        /// <param name="model">包含查詢條件的租戶模型，如 is_active、分頁參數等</param>
        /// <returns>符合條件的租戶資訊清單</returns>
        Task<IList<TenantsModel>> GetTenantsDataList(TenantsModel model);

        /// <summary>
        /// 根據租戶 ID 取得特定租戶的詳細資訊
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的租戶識別碼取得特定租戶的詳細資訊。
        /// 在專限查看式編輯租戶資訊時使用，管理頁面或詳情頁面顯示的主要方法。
        /// 結果包含租戶的完整信息，包括法律地址、聯絡方式、帳單幣別和狀態等。
        /// 如果指定的租戶 ID 不存在，應返回適當的空值或拋出異常。
        /// </remarks>
        /// <param name="tenant_id">要查詢的租戶的唯一識別碼</param>
        /// <returns>包含租戶詳細資料的模型，或可能返回空值</returns>
        Task<TenantsModel> GetTenantsData(Guid tenant_id);

        /// <summary>
        /// 創建新的租戶資料
        /// </summary>
        /// <remarks>
        /// 此方法會在系統中創建一個新的租戶記錄，使用提供的模型作為資料來源。
        /// 在創建過程中會驗證必要欄位，並生成唯一的租戶識別碼。
        /// 新創建的租戶預設為啟用狀態，並設定建立的時間戳。
        /// 此方法可能需要新增相關顸目，如付款方式、預設管理員帳戶等。
        /// </remarks>
        /// <param name="model">包含新租戶資料的模型，應包含名稱、聯絡方式和其他必要資訊</param>
        /// <returns>創建成功後的租戶模型，包含新生成的租戶 ID 和其他系統設定的欄位</returns>
        Task<TenantsModel> CreateTenantsData(TenantsModel model);

        /// <summary>
        /// 更新現有租戶的資料
        /// </summary>
        /// <remarks>
        /// 此方法會更新現有租戶的資料，使用提供的模型作為資料來源。
        /// 更新過程中會驗證租戶的存在性和必要欄位，並更新修改時間戳。
        /// 可更新的資訊包含租戶名稱、聯絡方式、帳單幣別和地址等，但不會更改租戶的唯一識別碼。
        /// 專屬的狀態更新應使用 UpdateTenantsStatus 方法進行。
        /// </remarks>
        /// <param name="model">包含更新資料的租戶模型，必須包含要更新的租戶 ID</param>
        /// <returns>更新成功後的租戶模型，包含最新的資料狀態</returns>
        Task<TenantsModel> UpdateTenantsData(TenantsModel model);

        /// <summary>
        /// 切換租戶的啟用/停用狀態
        /// </summary>
        /// <remarks>
        /// 此方法會切換指定租戶的啟用/停用狀態，如果當前是啟用狀態則停用，反之亦然。
        /// 此方法專限於狀態切換，不會改變租戶的其他資訊。
        /// 停用租戶將導致相關的 API 服務、通知和帳單功能等也可能停止運作。
        /// 此操作應謹慎使用，可能需要額外的權限驗證。
        /// </remarks>
        /// <param name="tenant_id">要切換狀態的租戶唯一識別碼</param>
        /// <returns>切換狀態的結果，成功則返回 true，失敗則返回 false</returns>
        Task<bool> UpdateTenantsStatus(Guid tenant_id);
    }
}
