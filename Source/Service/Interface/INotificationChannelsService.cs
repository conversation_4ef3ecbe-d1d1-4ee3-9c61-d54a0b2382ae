using Paas.Model;

namespace Paas.Service
{
    /// <summary>
    /// 通知渠道管理服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與通知渠道相關的操作，包括查詢、創建、更新和刪除等功能。
    /// 此服務負責管理系統中的通知渠道設定，包含不同的通知發送渠道（如電子郵件、簡訊、應用內通知等）的設定和管理。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// 應通過相依性注入在控制器或其他服務中使用。
    /// </remarks>
    public interface INotificationChannelsService
    {
        /// <summary>
        /// 根據指定的查詢條件取得通知渠道清單，支持狀態過濾與分頁
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的查詢條件取得符合條件的通知渠道清單。
        /// 支援基本的篩選和分頁功能，可用於管理介面或通知設定頁面。
        /// 結果包含通知渠道的基本資訊，如渠道類型、設定、狀態和創建時間等。
        /// 可根據渠道類型或啟用狀態進行篩選，以取得符合特定需求的通知渠道清單。
        /// 此方法應限制只返回調用者有權存取的通知渠道，例如管理員可查看所有渠道，其他用戶可能只能查看自己所屬租戶的渠道。
        /// </remarks>
        /// <param name="model">包含查詢條件的通知渠道模型，如 is_active 等查詢參數</param>
        /// <returns>符合條件的通知渠道資料清單</returns>
        Task<IList<NotificationChannelsModel>> GetNotificationChannels(NotificationChannelsModel model);
        /// <summary>
        /// 創建新的通知渠道設定
        /// </summary>
        /// <remarks>
        /// 此方法會在系統中創建一個新的通知渠道記錄，使用提供的模型作為資料來源。
        /// 在創建過程中會驗證必要欄位，如渠道類型和設定等，並生成唯一的渠道識別碼。
        /// 新創建的通知渠道預設為啟用狀態，並設定建立的時間戳。
        /// 依據渠道類型，設定可能需要包含不同的參數，如電子郵件渠道需要包含 SMTP 伺服器設定、帳號和密碼等。
        /// 此方法應進行全面的權限驗證，確保只有授權的管理員或租戶管理員可以創建通知渠道。
        /// </remarks>
        /// <param name="model">包含新通知渠道資料的模型，應包含渠道類型和設定等必要資訊</param>
        /// <returns>創建成功後的通知渠道模型，包含新生成的渠道 ID 和其他系統設定的欄位，失敗則返回 null</returns>
        Task<NotificationChannelsModel?> InsertNotificationChannel(NotificationChannelsModel model);
        /// <summary>
        /// 更新現有通知渠道的設定
        /// </summary>
        /// <remarks>
        /// 此方法會更新現有通知渠道的設定，使用提供的模型作為資料來源。
        /// 更新過程中會驗證通知渠道的存在性和必要欄位，並更新修改時間戳。
        /// 可更新的資訊包含通知渠道的設定和啟用狀態等，但通常不會更改渠道的唯一識別碼和類型。
        /// 更新設定可能會影響相關的通知發送功能，因此應確保更新後的設定仍然有效且正確。
        /// 此方法應進行全面的權限驗證，確保只有授權的管理員或租戶管理員可以更新通知渠道。
        /// </remarks>
        /// <param name="model">包含更新資料的通知渠道模型，必須包含要更新的渠道 ID</param>
        /// <returns>更新成功後的通知渠道模型，包含最新的資料狀態，失敗則返回 null</returns>
        Task<NotificationChannelsModel?> UpdateNotificationChannel(NotificationChannelsModel model);
        /// <summary>
        /// 刪除指定的通知渠道設定
        /// </summary>
        /// <remarks>
        /// 此方法會刪除系統中指定的通知渠道記錄。
        /// 刪除操作可能會將記錄標記為刪除狀態，而不是從資料庫中完全刪除，以便維護歷史記錄。
        /// 刪除通知渠道會導致相關的通知無法再透過該渠道發送，因此在刪除前應確保有其他可用的通知渠道或者該渠道不再需要。
        /// 此方法應進行全面的權限驗證，確保只有授權的管理員或租戶管理員可以刪除通知渠道。
        /// 操作完成時應記錄詳細的安全日誌，包括操作者和操作時間等信息。
        /// </remarks>
        /// <param name="channelId">要刪除的通知渠道的唯一識別碼</param>
        /// <returns>刪除操作的結果，成功則返回 true，失敗則返回 false</returns>
        Task<bool> DeleteNotificationChannel(Guid channelId);
    }
}
