using Paas.Model;

namespace Paas.Service
{
    /// <summary>
    /// 幣別管理服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與幣別相關的操作，包括查詢、創建、更新和刪除等功能。
    /// 此服務負責管理系統中的幣別設定，包含代碼、符號、匯率和狀態等信息。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// 幣別設定在系統中的應用包括帳單、報表和付款自接等多個山區，因此此介面應適當限制只有管理員可以執行幣別的創建和管理操作。
    /// </remarks>
    public interface ICurrenciesService
    {
        /// <summary>
        /// 取得系統中所有幣別的資料清單
        /// </summary>
        /// <remarks>
        /// 此方法會取得系統中所有已設定的幣別清單。
        /// 結果包含每個幣別的詳細資訊，如幣別 ID、代碼、符號、匯率和啟用狀態等。
        /// 此資料用於幣別管理介面，並且在其他功能模組中也會使用，如運選號箋時的幣別一覽、設定租戶預設帳單幣別等。
        /// 系統中的幣別數量通常不多，衣托作一般情況下不需要分頁。
        /// 可能需要根據啟用狀態進行篩選，例如只顯示啟用中的幣別。
        /// </remarks>
        /// <returns>系統中所有幣別的清單，包含各幣別的詳細資訊</returns>
        Task<IList<CurrenciesModel>> GetCurrencies();
        /// <summary>
        /// 根據幣別 ID 取得特定幣別的詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的幣別識別碼取得特定幣別的詳細資料。
        /// 在查看或編輯幣別資料時使用，管理介面或詳情頁面顯示的主要方法。
        /// 結果包含幣別的完整資訊，包括幣別 ID、代碼、符號、匯率和啟用狀態等。
        /// 如果指定的幣別 ID 不存在，則返回 null。
        /// 此方法常用於在更新幣別信息前先取得特定幣別的現有資料。
        /// </remarks>
        /// <param name="currency_id">要查詢的幣別的唯一識別碼</param>
        /// <returns>包含幣別詳細資料的模型，若找不到則返回 null</returns>
        Task<CurrenciesModel?> GetCurrency(Guid currency_id);
        /// <summary>
        /// 創建新的幣別設定
        /// </summary>
        /// <remarks>
        /// 此方法會在系統中創建一個新的幣別記錄，使用提供的模型作為資料來源。
        /// 在創建過程中會驗證必要欄位，如幣別代碼、符號和匯率等，並生成唯一的幣別識別碼。
        /// 新創建的幣別可以設定為啟用或停用狀態，並設定建立的時間戳。
        /// 幣別代碼應遵循國際標準（如 ISO 4217），並保持唯一性，以避免幣別混淆和匯率問題。
        /// 此方法應進行全面的權限驗證，確保只有系統管理員具有創建新幣別的權限。
        /// </remarks>
        /// <param name="model">包含新幣別資料的模型，應包含代碼、符號、匯率和啟用狀態等必要資訊</param>
        /// <returns>創建成功後的幣別模型，包含新生成的幣別 ID 和其他系統設定的欄位，失敗則返回 null</returns>
        Task<CurrenciesModel?> InsertCurrency(CurrenciesModel model);
        /// <summary>
        /// 更新現有幣別的資料
        /// </summary>
        /// <remarks>
        /// 此方法會更新現有幣別的資料，使用提供的模型作為資料來源。
        /// 更新過程中會驗證幣別的存在性和必要欄位，並更新修改時間戳。
        /// 可更新的資訊包含幣別的代碼、符號、匯率和啟用狀態等，但不會更改幣別的唯一識別碼。
        /// 更新幣別資料可能會影響系統中計價和帳單功能，因此應謹慎進行，以避免對現有數據造成用點。
        /// 特別是匯率的更新應足對現有的帳單和交易紀錄進行適當處理，以確保數據一致性。
        /// 此方法應進行全面的權限驗證，確保只有系統管理員具有更新幣別的權限。
        /// </remarks>
        /// <param name="model">包含更新資料的幣別模型，必須包含要更新的幣別 ID</param>
        /// <returns>更新成功後的幣別模型，包含最新的資料狀態，失敗則返回 null</returns>
        Task<CurrenciesModel?> UpdateCurrency(CurrenciesModel model);
        /// <summary>
        /// 刪除指定的幣別設定
        /// </summary>
        /// <remarks>
        /// 此方法會刪除系統中指定的幣別記錄。
        /// 由於幣別可能和現有的帳單、交易或其他資料項目相關聯，嚴格上將使用跳來對映關係的設緾不應被允許刪除。
        /// 替代方案可能是將幣別標記為停用狀態，而不是從資料庫中完全刪除，以保持歷史數據的完整性。
        /// 此方法應進行全面的權限驗證，確保只有系統管理員具有刪除幣別的權限。
        /// 在刪除前應驗證該幣別是否沒有被使用，或者已使用的記錄是否已適當處理，以避免對系統中現有資料造成影響。
        /// </remarks>
        /// <param name="currency_id">要刪除的幣別的唯一識別碼</param>
        /// <returns>刪除操作的結果，成功則返回 true，失敗則返回 false</returns>
        Task<bool> DeleteCurrency(Guid currency_id);
    }
}
