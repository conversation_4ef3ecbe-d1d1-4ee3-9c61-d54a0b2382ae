using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 權限管理服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與權限管理相關的操作，用於識別和管理用戶角色可以執行的動作。
    /// 此服務為系統的安全與存取控制機制建立了基礎，確保用戶只能存取其被授權的資源和功能。
    /// 它處理系統中不同角色的權限定義和查詢，作為角色型存取控制（RBAC）模型的一部分。
    /// 此介面可用於清單式管理介面、用戶設定頁面或其他需要執行權限檢查的系統組件。
    /// 理解和善用權限管理是構建符合最低權限原則的安全系統的關鍵元素。
    /// </remarks>
    public interface IPermissionsService
    {
        /// <summary>
        /// 根據角色 ID 取得該角色的所有權限列表
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的角色 ID 取得該角色被授予的所有權限列表。
        /// 此方法用於呈現角色的權限管理介面、驗證用戶操作權限或在使用者介面中動態顯示/隱藏功能。
        /// 返回的權限清單包含所有角色定義的權限，每個權限實體包含權限名稱、代碼、描述和相關資源等資訊。
        /// 權限定義應包含對資源的操作類型，如讀取、寫入、更新或刪除操作等。
        /// 在查詢時，應考慮權限快取和效能最佳化，如快取常用權限列表或根據程式狀態進行權限檢查。
        /// </remarks>
        /// <param name="role_id">要查詢權限的角色的唯一識別碼</param>
        /// <returns>該角色擁有的權限清單</returns>
        Task<IList<PermissionsModel>> GetPermissionsList(Guid role_id);
    }
}
