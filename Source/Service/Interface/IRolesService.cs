using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 角色管理服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與角色相關的操作，包括查詢和創建角色等功能。
    /// 角色是基於角色的存取控制（RBAC）模型的基礎組成部分，用於定義使用者群組和它們擁有的權限。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// 此服務是系統安全模型的重要組成部分，應適當限制只有管理員可以執行角色的創建和管理操作。
    /// </remarks>
    public interface IRolesService
    {

        /// <summary>
        /// 取得系統中所有角色的資料清單
        /// </summary>
        /// <remarks>
        /// 此方法會取得系統中所有已定義的角色清單。
        /// 結果包含每個角色的詳細資訊，如角色 ID、名稱和描述等。
        /// 此資料用於角色管理介面，如使用者管理、權限設定和使用者角色指派等功能。
        /// 根據安全考慮，可能需要確保只有絕役使用者星術存取角色定義，或根據使用者的權限範圈適當篩選結果。
        /// 得將一般情況下不需要分頁，因為系統中的角色数量通常較少。
        /// </remarks>
        /// <returns>系統中所有角色的清單，包含各角色的詳細資訊</returns>
        Task<IList<RolesModel>> GetRolesList();

        /// <summary>
        /// 創建新的系統角色
        /// </summary>
        /// <remarks>
        /// 此方法會在系統中創建一個新的角色記錄，使用提供的模型作為資料來源。
        /// 在創建過程中會驗證必要欄位，如角色名稱和描述，並生成唯一的角色識別碼。
        /// 新創建的角色預設沒有任何權限，需要通過獨立的權限管理功能進行權限指派。
        /// 此方法應進行全面的權限驗證，確保只有系統管理員具有創建新角色的權限。
        /// 角色名稱應保持唯一性，以避免角色混淆和權限問題。
        /// </remarks>
        /// <param name="model">包含新角色資料的模型，應包含角色名稱和描述等必要資訊</param>
        /// <returns>創建成功後的角色模型，包含新生成的角色 ID 和其他系統設定的欄位</returns>
        Task<RolesModel?> CreateRolesData(RolesModel model);
    }
}
