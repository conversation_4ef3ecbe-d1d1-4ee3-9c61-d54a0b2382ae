using Paas.Model;

namespace Paas.Service
{
    /// <summary>
    /// 安全日誌服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與安全日誌相關的操作，包括查詢和存取等功能。
    /// 此服務負責管理系統中的安全日誌資料，記錄用戶操作、管理活動、系統事件和安全相關的變化等。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// 安全日誌系統是保護和監控系統安全性的重要組成部分，為稽核、問題追蹤和安全事件調查提供了必要的記錄。
    /// 由於安全日誌中可能包含敏感或機密資訊，因此此介面應條限制的使用權限，只允許授權的管理員或安全人員存取。
    /// </remarks>
    public interface ISecurityLogsService
    {
        /// <summary>
        /// 根據指定的查詢條件取得安全日誌清單，可依租戶、事件類型、時間區間等條件篩選與分頁
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的查詢條件取得符合條件的安全日誌清單。
        /// 支援多種篩選條件，包括租戶 ID、事件類型、時間範圍、用戶 ID 和 IP 地址等。
        /// 同時支援基本的分頁功能，可用於安全管理介面、審計報表或安全監控介面。
        /// 結果包含安全日誌的基本資訊，如日誌 ID、事件類型、事件時間、操作用戶、IP 地址和事件描述等。
        /// 根據系統的安全策略，此方法必須執行嚴格的權限驗證，確保調用者只能存取其有權存取的安全日誌。
        /// 安全日誌的存取應被記錄，以便在需要時進行審計和追蹤。
        /// </remarks>
        /// <param name="model">包含查詢條件的安全日誌模型，如 tenant_id、event_type_id、date_range 等查詢參數</param>
        /// <returns>符合條件的安全日誌清單</returns>
        Task<IList<SecurityLogsModel>> GetSecurityLogs(SecurityLogsModel model);
        /// <summary>
        /// 根據日誌 ID 取得特定安全日誌的詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的日誌識別碼取得特定安全日誌的詳細資料。
        /// 在查看安全日誌詳情時使用，安全管理介面或事件調查頁面顯示的主要方法。
        /// 結果包含安全日誌的完整資訊，包括日誌 ID、事件類型、事件時間、租戶、操作用戶、IP 地址、事件描述和相關資料等。
        /// 如果指定的日誌 ID 不存在，則返回 null。
        /// 此方法必須執行嚴格的權限驗證，確保調用者有權存取該安全日誌。
        /// 存取特定安全日誌的操作本身也應被記錄為新的安全日誌，以便追蹤誰在查看敏感的安全事件。
        /// </remarks>
        /// <param name="logId">要查詢的安全日誌的唯一識別碼</param>
        /// <returns>包含安全日誌詳細資料的模型，若找不到則返回 null</returns>
        Task<SecurityLogsModel?> GetSecurityLog(Guid logId);
    }
}
