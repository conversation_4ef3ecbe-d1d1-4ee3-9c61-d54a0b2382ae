using AutoMapper;
using Paas.Dac;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 帳單管理服務的介面定義
    /// </summary>
    /// <remarks>
    /// 此介面定義了系統中與帳單相關的操作，包括查詢、創建、更新和刪除等功能。
    /// 此服務負責管理系統中的帳單記錄，包含帳單的建立、追蹤、支付和記錄等功能。
    /// 所有方法都返回 Task 物件，以支援靜應式操作和更高的系統可擴展性。
    /// 帳單管理是系統負責監控和記錄系統使用者的交易和支付活動的核心組件。
    /// 此介面用於處理租戶和系統使用者的付費分析、設定、價格計算下對帳上分析切和定價策設定等相關功能。
    /// </remarks>
    public interface IBillingService
    {

        /// <summary>
        /// 根據指定的查詢條件取得帳單資料清單
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的查詢條件取得符合條件的帳單清單。
        /// 支援基本的篩選和分頁功能，可用於帳單管理介面、報表系統或貼表前端客槚。
        /// 結果包含帳單的基本資訊，如帳單 ID、金額、幣別、付款方式、狀態和到期日期等。
        /// 可根據租戶、狀態、日期範圍或金額範圍進行篩選，以取得符合特定需求的帳單清單。
        /// 此方法應進行權限驗證，確保調用者只能查詢其有權存取的帳單資料。
        /// </remarks>
        /// <param name="model">包含查詢條件的帳單模型，如 tenant_id、status、date_range 等查詢參數</param>
        /// <returns>符合條件的帳單資料清單</returns>
        Task<IList<BillingModel>> GetBillingDataList(BillingModel model);

        /// <summary>
        /// 根據帳單 ID 取得特定帳單的詳細資料
        /// </summary>
        /// <remarks>
        /// 此方法會根據提供的帳單識別碼取得特定帳單的詳細資料。
        /// 在查看或編輯帳單資料時使用，帳單管理介面或詳情頁面顯示的主要方法。
        /// 結果包含帳單的完整資訊，包括租戶、金額、幣別、付款方式、狀態、到期日期和支付時間等。
        /// 如果指定的帳單 ID 不存在，則可能返回適當的空值或拋出異常。
        /// 此方法應進行權限驗證，確保調用者只能查詢其有權存取的帳單資料。
        /// </remarks>
        /// <param name="billing_id">要查詢的帳單的唯一識別碼</param>
        /// <returns>包含帳單詳細資料的模型，若找不到則返回適當的空值</returns>
        Task<BillingModel> GetBillingDataById(Guid billing_id);


        /// <summary>
        /// 創建新的帳單記錄
        /// </summary>
        /// <remarks>
        /// 此方法會在系統中創建一個新的帳單記錄，使用提供的模型作為資料來源。
        /// 在創建過程中會驗證必要欄位，如租戶 ID、金額、幣別、付款方式和到期日期等，並生成唯一的帳單識別碼。
        /// 新創建的帳單預設為待付款狀態，並設定建立的時間戳。
        /// 帳單創建後，系統可能需要發送通知或通知相關人員來處理帳單的付款。
        /// 此方法應進行權限驗證，確保調用者有權創建帳單記錄。
        /// </remarks>
        /// <param name="model">包含新帳單資料的模型，應包含租戶 ID、金額、幣別、付款方式和到期日期等必要資訊</param>
        /// <returns>創建成功後的帳單模型，包含新生成的帳單 ID 和其他系統設定的欄位</returns>
        Task<BillingModel> CreateBillingData(BillingModel model);

        /// <summary>
        /// 更新現有帳單的資料
        /// </summary>
        /// <remarks>
        /// 此方法會更新現有帳單的資料，使用提供的模型作為資料來源。
        /// 更新過程中會驗證帳單的存在性和必要欄位，並更新修改時間戳。
        /// 可更新的資訊包含帳單的狀態、金額、到期日期和支付時間等，但不會更改帳單的唯一識別碼和原始租戶。
        /// 帳單更新可能會影響相關的報表和通知，因此應謹慎操作，並確保更新後的狀態變化會適當證諻往數據。
        /// 重要的變更（如狀態變更為已付款）應記錄日誌，以便後續稽核和追蹤。
        /// 此方法應進行權限驗證，確保調用者有權更新帳單記錄。
        /// </remarks>
        /// <param name="model">包含更新資料的帳單模型，必須包含要更新的帳單 ID</param>
        /// <returns>更新成功後的帳單模型，包含最新的資料狀態</returns>
        Task<BillingModel> UpdateBillingData(BillingModel model);

        /// <summary>
        /// 刪除指定的帳單記錄
        /// </summary>
        /// <remarks>
        /// 此方法會刪除系統中指定的帳單記錄。
        /// 基於帳戶和記錄的需求，一般不建議完全刪除帳單記錄，而是將其標記為已刪除或已作廢狀態，以保留完整的歷史記錄。
        /// 此方法應進行全面的權限驗證，確保只有具有適當權限的使用者（如管理員或財務人員）可以刪除帳單記錄。
        /// 刪除帳單前應檢查其狀態，如果已付款或進行中的帳單可能需要額外的確認和記錄。
        /// 刪除操作必須記錄詳細的審計日誌，包括刪除原因、操作者和時間等。
        /// </remarks>
        /// <param name="billing_id">要刪除的帳單的唯一識別碼</param>
        /// <returns>刪除操作的結果，成功則返回 true，失敗則返回 false</returns>
        Task<bool> DeleteBillingData(Guid billing_id);
    }
}
