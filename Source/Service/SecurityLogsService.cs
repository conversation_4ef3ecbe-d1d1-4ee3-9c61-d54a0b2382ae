using AutoMapper;
using Microsoft.AspNetCore.Http.Connections;
using Newtonsoft.Json;
using Paas.Dac;
using Paas.Model;
using System.Web;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Paas.Service
{
    /// <summary>
    /// 安全日誌管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 ISecurityLogsService 介面，提供安全日誌相關的管理功能。
    /// 負責處理安全日誌的查詢和分析等操作，是系統安全稽核和審計的核心元件。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// 記錄系統中重要的安全事件，如登入嘗試、權限變更、敏感操作和可能的安全威脅，以助於系統安全分析和稽核。
    /// </remarks>
    public class SecurityLogsService : ISecurityLogsService
    {
        /// <summary>
        /// 安全日誌資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責安全日誌相關的資料庫操作，如查詢日誌等。
        /// </remarks>
        private readonly ISecurityLogsDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化安全日誌管理服務
        /// </summary>
        /// <param name="dac">安全日誌資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public SecurityLogsService(ISecurityLogsDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得安全日誌清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的查詢條件取得符合條件的安全日誌記錄清單。
        /// 支援各種查詢條件，如租戶 ID、日誌類型、事件識別碼、嚴重性等級、時間範圍、IP 位址等。
        /// 使用 DAC 層進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於安全日誌查詢介面、安全稽核功能、异常行為分析、安全報表生成等情境。
        /// 在多租戶環境中，此方法應限制用戶只能存取其租戶相關的安全日誌，除非具有特殊管理權限。
        /// </remarks>
        /// <param name="model">包含查詢條件的安全日誌模型，如租戶 ID、日誌類型、時間範圍、分頁參數等</param>
        /// <returns>符合條件的安全日誌清單</returns>
        public async Task<IList<SecurityLogsModel>> GetSecurityLogs(SecurityLogsModel model)
        {
            IList<SecurityLogs> data = await dac.GetSecurityLogs(mapper.Map<SecurityLogs>(model));
            return mapper.Map<IList<SecurityLogsModel>>(data);
        }

        /// <summary>
        /// 取得特定安全日誌的詳細資訊
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的日誌 ID 取得特定安全日誌的詳細資訊。
        /// 返回的資料包含日誌的完整詳細資料，如日誌類型、時間戳記、事件說明、相關用戶、IP 位址、操作結果和額外上下文等。
        /// 使用 DAC 層的 GetSecurityLog 方法進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於安全日誌詳細資訊頁面、特定事件調查、安全事件深度分析等情境。
        /// 在多租戶環境中，使用此方法時應驗證用戶是否有權限存取特定的安全日誌記錄。
        /// </remarks>
        /// <param name="logId">要查詢的安全日誌的唯一識別碼</param>
        /// <returns>特定安全日誌的詳細資訊模型，如果找不到則返回 null</returns>
        public async Task<SecurityLogsModel?> GetSecurityLog(Guid logId)
        {
            SecurityLogs? data = await dac.GetSecurityLog(logId);
            return mapper.Map<SecurityLogsModel>(data);
        }
    }
}
