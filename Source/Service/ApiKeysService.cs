using AutoMapper;
using Microsoft.AspNetCore.Http.Connections;
using Paas.Dac;
using Paas.Model;
using System.Web;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Paas.Service
{
    /// <summary>
    /// API 金鑰管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IApiKeysService 介面，提供 API 金鑰相關的管理功能。
    /// 負責處理 API 金鑰的建立、查詢、更新和刪除等操作，是系統 API 安全存取的核心元件。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// API 金鑰用於識別和授權外部系統對 API 的存取，並追蹤 API 的使用情況。
    /// </remarks>
    public class ApiKeysService : IApiKeysService
    {
        /// <summary>
        /// API 金鑰資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責 API 金鑰相關的資料庫操作，如查詢、新增、更新和刪除等。
        /// </remarks>
        private readonly IApiKeysDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化 API 金鑰管理服務
        /// </summary>
        /// <param name="dac">API 金鑰資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public ApiKeysService(IApiKeysDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得 API 金鑰清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的查詢條件取得符合條件的 API 金鑰清單。
        /// 支援使用各種條件進行查詢，如租戶 ID、金鑰狀態、建立時間範圍等。
        /// 使用 DAC 層進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於管理介面或報表系統，顯示系統中所有的 API 金鑰。
        /// </remarks>
        /// <param name="model">包含查詢條件的 API 金鑰模型，如 tenant_id、status 等</param>
        /// <returns>符合條件的 API 金鑰清單</returns>
        public async Task<IList<ApiKeysModel>> GetApiKeys(ApiKeysModel model)
        {
            IList<ApiKeys> data = await dac.GetApiKeys(mapper.Map<ApiKeys>(model));
            return mapper.Map<IList<ApiKeysModel>>(data);
        }

        /// <summary>
        /// 取得指定 API 金鑰的詳細資訊
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的金鑰 ID 取得特定 API 金鑰的詳細資訊。
        /// 返回的資料包含 API 金鑰的完整詳細資訊，如金鑰值、狀態、相關租戶、建立時間和到期時間等。
        /// 使用 DAC 層進行資料庫存取，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於查看單一 API 金鑰的詳細資訊，如金鑰管理介面或詳細資訊頁面。
        /// </remarks>
        /// <param name="keyId">要查詢的 API 金鑰的唯一識別碼</param>
        /// <returns>包含 API 金鑰資訊的模型，如果找不到會返回 null</returns>
        public async Task<ApiKeysModel?> GetApiKey(Guid keyId)
        {
            ApiKeys? data = await dac.GetApiKey(keyId);
            return mapper.Map<ApiKeysModel>(data);
        }

        /// <summary>
        /// 建立新的 API 金鑰
        /// </summary>
        /// <remarks>
        /// 此方法用於在系統中建立新的 API 金鑰，使用提供的模型作為資料來源。
        /// 在建立過程中會產生唯一的金鑰 ID，並設定金鑰的必要屬性，如建立時間、預設狀態等。
        /// 新創建的 API 金鑰可用於識別和授權外部工具、應用程式或第三方服務存取系統的 API。
        /// 完成後返回的模型包含已生成的 API 金鑰識別碼、金鑰值和其他相關屬性。
        /// </remarks>
        /// <param name="model">包含新 API 金鑰資料的模型，如租戶 ID、金鑰值、存取範圍等</param>
        /// <returns>建立成功後的 API 金鑰模型，包含新生成的 ID 和相關屬性，如果失敗則返回 null</returns>
        public async Task<ApiKeysModel?> InsertApiKey(ApiKeysModel model)
        {
            ApiKeys? insertedData = await dac.InsertApiKey(mapper.Map<ApiKeys>(model));
            return mapper.Map<ApiKeysModel>(insertedData);
        }

        /// <summary>
        /// 更新特定 API 金鑰
        /// </summary>
        /// <remarks>
        /// 此方法用於更新現有 API 金鑰的資料，使用提供的模型作為資料來源。
        /// 更新過程會驗證 API 金鑰的存在性和所有必要欄位的有效性。
        /// 可更新的屬性包括金鑰的狀態、金鑰存取範圍、到期時間等，但不會修改金鑰的唯一 ID 或建立時間。
        /// 更新 API 金鑰屬性如狀態或權限，可能會立即影響使用該金鑰的外部系統對 API 的存取能力。
        /// </remarks>
        /// <param name="model">包含要更新的 API 金鑰資料的模型，必需包含金鑰 ID</param>
        /// <returns>更新成功後的 API 金鑰模型，包含最新的資料狀態，如果失敗則返回 null</returns>
        public async Task<ApiKeysModel?> UpdateApiKey(ApiKeysModel model)
        {
            ApiKeys? updatedData = await dac.UpdateApiKey(mapper.Map<ApiKeys>(model));
            return mapper.Map<ApiKeysModel>(updatedData);
        }

        /// <summary>
        /// 刪除特定 API 金鑰
        /// </summary>
        /// <remarks>
        /// 此方法用於刪除指定的 API 金鑰，根據提供的金鑰 ID 進行操作。
        /// 刪除操作會永久性地移除該 API 金鑰，或可能將其標記為已刪除狀態，根據系統設計而定。
        /// 此操作應謹慎執行，因為刪除 API 金鑰會立即終止使用該金鑰的外部系統對 API 的存取能力。
        /// 執行此操作可能需要額外的權限驗證或確認流程，以避免意外刪除重要的 API 金鑰。
        /// </remarks>
        /// <param name="keyId">要刪除的 API 金鑰的唯一識別碼</param>
        /// <returns>刪除操作的結果，成功則返回 true，失敗則返回 false</returns>
        public async Task<bool> DeleteApiKey(Guid keyId)
        {
            return await dac.DeleteApiKey(keyId);
        }
    }
}
