using AutoMapper;
using Paas.Dac;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Paas.Service
{
    /// <summary>
    /// API 服務管理實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IApisService 介面，提供 API 服務相關的管理功能。
    /// 負責管理系統中的 API 服務記錄，包含查詢、建立、更新和狀態管理等操作。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// API 服務記錄用於追蹤和管理系統提供的不同 API 端點、其功能和狀態。
    /// 此服務是 API 管理、監控和文檔系統的核心元件。
    /// </remarks>
    public class ApisService : IApisService
    {
        /// <summary>
        /// API 服務資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責 API 服務相關的資料庫操作，如查詢、新增、更新和狀態管理等。
        /// </remarks>
        private readonly IApisDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化 API 服務管理服務
        /// </summary>
        /// <param name="dac">API 服務資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public ApisService(IApisDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得 API 服務資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的查詢條件取得符合條件的 API 服務資料清單。
        /// 支援多種查詢條件，如 API 識別碼、名稱、狀態、分類等。
        /// 使用 DAC 層進行實際的資料庫查詢，並透過 AutoMapper 將結果轉換成服務層模型。
        /// 此方法適用於 API 管理介面、API 目錄和管理報表等情境，顯示系統提供的所有 API 服務。
        /// </remarks>
        /// <param name="model">包含查詢條件的 API 服務模型，如 api_id、name、status 等</param>
        /// <returns>符合條件的 API 服務資料清單</returns>
        public async Task<List<ApisModel>> GetApisDataList(ApisModel model)
        {
            // 取得API服務資料
            List<Apis> data = (await dac.GetApisDataList(model)).ToList();
            // 進行資料轉換
            List<ApisModel> result = mapper.Map<List<ApisModel>>(data);

            return result;
        }

        /// <summary>
        /// 取得指定 API 服務的詳細資訊
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的 API 識別碼取得特定 API 服務的詳細資訊。
        /// 返回的資料包含 API 的完整詳細資訊，如名稱、描述、端點路徑、版本、狀態和相關參數等。
        /// 使用 DAC 層進行資料庫操作，並透過篩選第一個符合條件的結果來取得特定 API 服務。
        /// 適用於 API 管理介面、API 詳細資訊頁面、API 文檔等情境，顯示單一 API 的詳細資訊。
        /// </remarks>
        /// <param name="api_id">要查詢的 API 服務的唯一識別碼，可為空值</param>
        /// <returns>包含 API 服務資訊的模型，如果找不到則可能返回 空的模型</returns>
        public async Task<ApisModel?> GetApisData(Guid? api_id)
        {
            // 取得API服務資料
            var queryModel = new ApisModel
            {
                api_id = api_id,
                name = string.Empty,
                description = string.Empty,
                version = string.Empty
            };
            // 由於已在建立物件時初始化，這裡不需要再次賦值
            
            Apis data = (await dac.GetApisDataList(queryModel)).FirstOrDefault();
            
            // 進行資料轉換，確保當 data 為 null 時返回一個有效的 ApisModel
            if (data == null)
            {
                return new ApisModel
                {
                    name = string.Empty,
                    description = string.Empty,
                    version = string.Empty
                };
            }
            
            ApisModel result = mapper.Map<ApisModel>(data);
            return result;
        }

        /// <summary>
        /// 新增 API 服務資料
        /// </summary>
        /// <remarks>
        /// 此方法用於在系統中建立新的 API 服務記錄，使用提供的模型作為資料來源。
        /// 新創建的 API 服務包含基本屬性如名稱、描述、端點路徑、版本、參數等資訊。
        /// 此操作會在資料庫中建立一個新的 API 服務記錄，並產生唯一的 API 識別碼。
        /// 新增的 API 服務可使用於 API 目錄、文檔系統和開發者面板等，提供開發者了解和使用系統提供的功能。
        /// </remarks>
        /// <param name="model">包含新 API 服務資料的模型，如名稱、描述、端點路徑、參數等</param>
        /// <returns>新增完成的 API 服務模型，包含系統生成的 ID 和相關屬性</returns>
        public async Task<ApisModel?> CreateApisData(ApisModel model)
        {
            // 新增API服務資料
            Apis? data = await dac.CreateApisData(model);

            // 確保嘗試映射的結果不為 null
            if (data == null)
            {
                return null;
            }

            ApisModel result = mapper.Map<ApisModel>(data);
            return result; 
        }

        /// <summary>
        /// 更新 API 服務資料
        /// </summary>
        /// <remarks>
        /// 此方法用於更新現有 API 服務的資料，使用提供的模型作為資料來源。
        /// 更新過程會驗證 API 服務的存在性和所有必要欄位的有效性。
        /// 可更新的屬性包括 API 的名稱、描述、端點路徑、版本、參數等，但通常不會修改 API 的唯一識別碼。
        /// 更新 API 服務資訊可能會影響開發者使用此 API 的方式，尤其是更改端點或參數時，應謹慎處理並進行適當的版本控制。
        /// </remarks>
        /// <param name="model">包含要更新的 API 服務資料模型，必需包含 API 識別碼</param>
        /// <returns>更新後的 API 服務模型，包含最新的資料狀態</returns>
        public async Task<ApisModel?> UpdateApisData(ApisModel model)
        {
            // 更新API服務資料
            Apis? data = await dac.UpdateApisData(model);

            // 確保嘗試映射的結果不為 null
            if (data == null)
            {
                return null;
            }

            ApisModel result = mapper.Map<ApisModel>(data);
            return result;
        }

        /// <summary>
        /// 更新 API 服務資料狀態
        /// </summary>
        /// <remarks>
        /// 此方法用於切換指定 API 服務的啟用/停用狀態。
        /// 如果当前狀態為啟用，則切換為停用，反之亦然。
        /// 此方法專限於狀態切換，不會修改 API 的其他屬性如名稱、描述等。
        /// 停用 API 服務會導致該 API 端點不再接受新的調用請求，因此在生產環境中應謹慎執行此操作。
        /// 此操作通常用於自動化環境中的 API 生命週期管理，或在需要自動停用某些 API 時使用。
        /// </remarks>
        /// <param name="api_id">要切換狀態的 API 服務的唯一識別碼</param>
        /// <returns>狀態更新操作的結果，成功返回 true，失敗返回 false</returns>
        public async Task<bool> UpdateApisStatus(Guid api_id)
        {
            // 更新API服務資料狀態
            return await dac.UpdateApisStatus(api_id);
        }
    }
}
