using AutoMapper;
using BCrypt.Net;
using Paas.Dac;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 身分驗證服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IAuthService 介面，提供使用者身分驗證相關的功能。
    /// 負責處理使用者登入驗證、密碼比對和使用者資料查詢等操作。
    /// 使用 BCrypt 進行安全的密碼雜湊比對，確保密碼安全性。
    /// 透過資料存取層進行實際的資料庫操作，並使用 AutoMapper 進行資料模型轉換。
    /// 為系統提供安全可靠的身分驗證機制，是系統安全架構的核心元件。
    /// </remarks>
    public class AuthService : IAuthService
    {
        /// <summary>
        /// 使用者資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責使用者相關的資料庫操作，如查詢使用者資料等。
        /// </remarks>
        private readonly IUsersDac usersDac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化身分驗證服務
        /// </summary>
        /// <param name="usersDac">使用者資料存取組件</param>
        /// <param name="mapper">資料模型映射器</param>
        public AuthService(IUsersDac usersDac, IMapper mapper)
        {
            this.usersDac = usersDac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 驗證使用者登入憑證
        /// </summary>
        /// <remarks>
        /// 此方法會先根據電子郵件查詢使用者，然後使用 BCrypt 驗證密碼。
        /// 使用安全的密碼比對機制，防止時序攻擊和其他安全威脅。
        /// 驗證成功時返回完整的使用者資訊，失敗時返回 null。
        /// 此方法包含適當的錯誤處理，確保系統穩定性和安全性。
        /// </remarks>
        /// <param name="email">使用者的電子郵件地址</param>
        /// <param name="password">使用者的原始密碼（明文）</param>
        /// <returns>驗證成功時返回使用者模型，失敗時返回 null</returns>
        public async Task<UsersModel?> ValidateUserAsync(string email, string password)
        {
            try
            {
                // 根據電子郵件查詢使用者
                var user = await GetUserByEmailAsync(email);
                if (user == null)
                {
                    return null;
                }

                // 使用 BCrypt 驗證密碼
                bool isPasswordValid = BCrypt.Net.BCrypt.Verify(password, user.password_hash);
                
                if (isPasswordValid)
                {
                    // 驗證成功，返回使用者資料（不包含密碼雜湊）
                    user.password_hash = string.Empty; // 清除密碼雜湊，避免洩露
                    return user;
                }

                return null;
            }
            catch (Exception)
            {
                // 發生錯誤時返回 null，避免洩露系統資訊
                return null;
            }
        }

        /// <summary>
        /// 根據電子郵件查詢使用者資料
        /// </summary>
        /// <remarks>
        /// 此方法根據電子郵件地址查詢使用者的完整資料。
        /// 使用資料存取層進行資料庫查詢，並透過 AutoMapper 轉換結果。
        /// 如果找不到對應的使用者，則返回 null。
        /// </remarks>
        /// <param name="email">要查詢的使用者電子郵件地址</param>
        /// <returns>找到的使用者模型，如果不存在則返回 null</returns>
        public async Task<UsersModel?> GetUserByEmailAsync(string email)
        {
            try
            {
                var user = await usersDac.GetUserByEmailAsync(email);
                return mapper.Map<UsersModel>(user);
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}
