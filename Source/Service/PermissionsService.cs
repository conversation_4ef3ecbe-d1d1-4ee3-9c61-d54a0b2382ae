using AutoMapper;
using Paas.Dac;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 權限管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IPermissionsService 介面，提供權限相關的管理功能。
    /// 負責處理角色權限的查詢和管理等操作，是系統安全與權限控制的核心元件。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// 支援基於角色的權限系統，為多租戶環境提供安全及定製化的存取控制。
    /// </remarks>
    public class PermissionsService : IPermissionsService
    {
        /// <summary>
        /// 權限資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責權限相關的資料庫操作，如查詢角色權限等。
        /// </remarks>
        private readonly IPermissionsDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化權限管理服務
        /// </summary>
        /// <param name="dac">權限資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public PermissionsService(IPermissionsDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得指定角色的所有權限列表
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的角色 ID 取得該角色所擁有的所有權限列表。
        /// 返回的資料包含每個權限的完整詳細資料，如權限名稱、代碼、描述、資源類型和存取的範圍等。
        /// 使用 DAC 層進行資料庫查詢，直接取得角色與權限的關聯資料，並透過 AutoMapper 轉換為服務層模型。
        /// 適用於角色管理介面、權限分配頁面、權限驗證邏輯等情境。
        /// 在多租戶環境中，此方法提供了管理普通使用者存取系統功能的基礎。
        /// </remarks>
        /// <param name="role_id">要查詢權限的角色的唯一識別碼</param>
        /// <returns>指定角色擁有的權限列表</returns>
        public async Task<IList<PermissionsModel>> GetPermissionsList(Guid role_id)
        {
            // 取得角色下權限資料
            IList<Permissions> result = await dac.GetPermissionsList(role_id);

            return mapper.Map<IList<PermissionsModel>>(result);
        }
    }
}
