using AutoMapper;
using Paas.Dac;
using Paas.Dac.Interface;
using Paas.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// 帳單管理服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IBillingService 介面，提供帳單相關的管理功能。
    /// 負責處理帳單的建立、查詢、更新和刪除等操作，是系統賬務管理的核心元件。
    /// 使用資料存取層進行實際的資料庫操作，並透過 AutoMapper 進行資料模型的轉換。
    /// 管理多租戶環境中的帳單記錄、收費計算和交易記錄，為交易和服務提供計費的支持。
    /// </remarks>
    public class BillingService : IBillingService
    {
        /// <summary>
        /// 帳單資料存取組件
        /// </summary>
        /// <remarks>
        /// 負責帳單相關的資料庫操作，如查詢、新增、更新和刪除帳單記錄等。
        /// </remarks>
        private readonly IBillingDac dac;
        
        /// <summary>
        /// 資料模型映射器
        /// </summary>
        /// <remarks>
        /// 用於資料模型和資料庫實體之間的轉換，簡化層與層之間的資料交換。
        /// </remarks>
        private readonly IMapper mapper;

        /// <summary>
        /// 初始化帳單管理服務
        /// </summary>
        /// <param name="dac">帳單資料存取組件</param>
        /// <param name="mapper">資料模型映射器，用於實體和模型之間的轉換</param>
        public BillingService(IBillingDac dac, IMapper mapper)
        {
            this.dac = dac;
            this.mapper = mapper;
        }

        /// <summary>
        /// 取得帳單資料清單
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的查詢條件取得符合條件的帳單資料清單。
        /// 支援各種查詢條件，如帳單 ID、租戶 ID、日期範圍、金額範圍、付款狀態等。
        /// 使用 DAC 層進行資料庫查詢，並透過分頁參數（Offset 和 Limit）控制結果集大小。
        /// 透過 AutoMapper 將結果轉換為服務層模型，以方便前端展示和操作。
        /// 適用於帳單管理介面、賬務報表、交易歷史查詢等情境。
        /// </remarks>
        /// <param name="model">包含查詢條件的帳單模型，具有分頁參數 Offset（起始頁）和 Limit（每頁數量）</param>
        /// <returns>符合條件的帳單資料清單，已按分頁參數進行資料限制</returns>
        public async Task<IList<BillingModel>> GetBillingDataList(BillingModel model)
        {
            // 取得帳單資訊
            IList<BillingRecords> data = (await dac.GetBillingDataList(model)).Skip((model.Offset - 1) * model.Limit).Take(model.Limit).ToList(); ;
            return mapper.Map<IList<BillingModel>>(data);
        }

        /// <summary>
        /// 取得特定帳單的詳細資訊
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的帳單 ID 取得特定帳單的詳細資訊。
        /// 返回的資料包含帳單的完整詳細資料，如帳單號碼、金額、日期、相關租戶、付款狀態、交易明細等。
        /// 使用 DAC 層的 GetBillingDataById 方法進行資料庫查詢，並透過 AutoMapper 轉換結果為服務層模型。
        /// 適用於帳單詳細資訊頁面、發票查看、付款確認等情境。
        /// 在多租戶環境中，應確保用戶只能存取其有權限的帳單資訊。
        /// </remarks>
        /// <param name="billing_id">要查詢的帳單的唯一識別碼</param>
        /// <returns>特定帳單的詳細資訊模型，如果找不到則返回空模型</returns>
        public async Task<BillingModel> GetBillingDataById(Guid billing_id)
        {
            // 取得帳單資訊
            BillingRecords? data = await dac.GetBillingDataById(billing_id);
            return mapper.Map<BillingModel>(data);
        }

        /// <summary>
        /// 新增帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法用於在系統中建立新的帳單記錄，使用提供的模型作為資料來源。
        /// 在建立過程中會產生唯一的帳單 ID，並設定帳單的必要屬性，如金額、日期、相關租戶、初始付款狀態等。
        /// 新創建的帳單會綁定到特定租戶或使用者，並可能觸發相關的通知或自動收款流程。
        /// 適用於服務訂購、賬務系統手動建立帳單、定期收費等情境。
        /// 執行建立帳單操作可能需要特定的權限，且須遵循賬務實務規範。
        /// </remarks>
        /// <param name="model">包含新帳單資料的模型，如金額、日期、相關租戶、付款狀態等</param>
        /// <returns>建立成功後的帳單模型，包含系統生成的 ID 和相關屬性</returns>
        public async Task<BillingModel> CreateBillingData(BillingModel model)
        {
            BillingRecords? result = await dac.CreateBillingData(model);
            return mapper.Map<BillingModel>(result);
        }

        /// <summary>
        /// 更新帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法用於更新現有帳單的資料，使用提供的模型作為資料來源。
        /// 更新過程會驗證帳單的存在性和所有必要欄位的有效性。
        /// 可更新的屬性包括帳單金額、付款狀態、到期日等，但通常不會修改帳單的唯一 ID 或建立日期。
        /// 從賬務實務角度考慮，更新帳單應記錄完整的修改歷史，以維護財務記錄的完整性。
        /// 適用於調整帳單金額、更新付款狀態、更正帳單錯誤等情境。
        /// </remarks>
        /// <param name="model">包含要更新的帳單資料的模型，必需包含帳單 ID</param>
        /// <returns>更新後的帳單模型，包含最新的資料狀態</returns>
        public async Task<BillingModel> UpdateBillingData(BillingModel model)
        {
            BillingRecords? result = await dac.UpdateBillingData(model);
            return mapper.Map<BillingModel>(result);
        }

        /// <summary>
        /// 刪除特定帳單資料
        /// </summary>
        /// <remarks>
        /// 此方法用於刪除指定的帳單記錄，根據提供的帳單 ID 進行操作。
        /// 刪除操作可能會永久性地移除該帳單，或將其標記為已刪除狀態，根據系統設計而定。
        /// 從賬務記錄的角度考慮，權限應是受限的，且應留下記錄或備註以維護實驗轃。
        /// 此操作應謹慎執行，因為刪除帳單可能會影響相關的賬務報表、收益計算和稅務記錄。
        /// 當執行此操作時，須先確保該帳單沒有被其他系統流程所依賴。
        /// </remarks>
        /// <param name="billing_id">要刪除的帳單的唯一識別碼</param>
        /// <returns>刪除操作的結果，成功則返回 true，失敗則返回 false</returns>
        public async Task<bool> DeleteBillingData(Guid billing_id)
        {
            return await dac.DeleteBillingData(billing_id);
        }
    }
}
