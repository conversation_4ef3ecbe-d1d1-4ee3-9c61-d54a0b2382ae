using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Paas.Service
{
    /// <summary>
    /// JWT 認證與授權服務實作類別
    /// </summary>
    /// <remarks>
    /// 此類別實作了 IJwtService 介面，提供 JWT (JSON Web Token) 的產生和驗證功能。
    /// JWT 用於安全地在不同系統之間傳輸使用者識別和授權資訊。
    /// 此服務貼賴應用程式設定文件中的 JwtSettings 密鑰設定來產生和驗證權杖。
    /// 產生的權杖包含使用者識別、到期時間和其他必要的證許資訊。
    /// 此服務在驗證 API 調用、賺設使用者身份和實現無狀態認證系統中有重要作用。
    /// </remarks>
    public class JwtService : IJwtService
    {
        /// <summary>
        /// 應用程式設定資訊
        /// </summary>
        /// <remarks>
        /// 包含 JWT 相關設定，如簽章密鑰、發行者、接收者和權杖有效期限。
        /// </remarks>
        private readonly IConfiguration _config;

        /// <summary>
        /// 初始化 JWT 服務
        /// </summary>
        /// <param name="config">應用程式設定，包含 JWT 相關參數</param>
        public JwtService(IConfiguration config)
        {
            _config = config;
        }

        /// <summary>
        /// 產生 JWT 權杖
        /// </summary>
        /// <remarks>
        /// 此方法根據提供的使用者名稱產生安全的 JWT 權杖。
        /// 產生的權杖包含使用者識別、唯一的權杖 ID 和到期時間等資訊。
        /// 使用應用程式設定檔中的 JwtSettings 密鑰設定進行權杖簽章，確保權杖的安全性。
        /// 此權杖可用於後續的 API 調用、資源存取和用戶驗證中。
        /// 產生的權杖由授權中間件進行驗證，確保資源的安全存取。
        /// </remarks>
        /// <param name="username">要產生權杖的使用者名稱，用於權杖的 Subject 證許</param>
        /// <returns>簽名並格式化的 JWT 權杖字串，应包含於請求的 Authorization 標頭中</returns>
        public string GenerateToken(string username)
        {
            // 取得appsetting 設定
            var jwtSettings = _config.GetSection("JwtSettings");
            var secretKey = Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]!);

            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, username),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            //產生token
            var token = new JwtSecurityToken(
                issuer: jwtSettings["Issuer"],
                audience: jwtSettings["Audience"],
                claims: claims,
                expires: DateTime.UtcNow.AddMinutes(Convert.ToDouble(jwtSettings["ExpireMinutes"])),
                signingCredentials: new SigningCredentials(
                    new SymmetricSecurityKey(secretKey),
                    SecurityAlgorithms.HmacSha256)
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }
}
