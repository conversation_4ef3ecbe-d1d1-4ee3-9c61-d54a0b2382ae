#!/bin/bash

# Swagger 控制功能測試腳本

echo "🧪 開始測試 Swagger 控制功能..."

# 測試函數
test_swagger_enabled() {
    echo "📋 測試 1: 啟用 Swagger UI"
    
    # 啟動容器 (啟用 Swagger)
    docker run --rm -d -p 8080:8080 -e EnableSwagger=true --name swagger-test-enabled paas-api:latest
    
    # 等待容器啟動
    echo "⏳ 等待容器啟動..."
    sleep 10
    
    # 測試 Swagger UI
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/swagger/index.html)
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ 測試通過: Swagger UI 已啟用 (HTTP $HTTP_CODE)"
    else
        echo "❌ 測試失敗: Swagger UI 未啟用 (HTTP $HTTP_CODE)"
    fi
    
    # 停止容器
    docker stop swagger-test-enabled > /dev/null 2>&1
}

test_swagger_disabled() {
    echo "📋 測試 2: 停用 Swagger UI"
    
    # 啟動容器 (停用 Swagger)
    docker run --rm -d -p 8080:8080 -e EnableSwagger=false --name swagger-test-disabled paas-api:latest
    
    # 等待容器啟動
    echo "⏳ 等待容器啟動..."
    sleep 10
    
    # 測試 Swagger UI
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/swagger/index.html)
    
    if [ "$HTTP_CODE" = "404" ]; then
        echo "✅ 測試通過: Swagger UI 已停用 (HTTP $HTTP_CODE)"
    else
        echo "❌ 測試失敗: Swagger UI 未正確停用 (HTTP $HTTP_CODE)"
    fi
    
    # 測試健康檢查端點是否仍然可用
    HEALTH_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/v1/health)
    
    if [ "$HEALTH_CODE" = "200" ] || [ "$HEALTH_CODE" = "503" ]; then
        echo "✅ 健康檢查端點正常 (HTTP $HEALTH_CODE)"
    else
        echo "❌ 健康檢查端點異常 (HTTP $HEALTH_CODE)"
    fi
    
    # 停止容器
    docker stop swagger-test-disabled > /dev/null 2>&1
}

# 檢查 Docker Image 是否存在
if ! docker images paas-api:latest | grep -q paas-api; then
    echo "❌ 錯誤: 找不到 paas-api:latest Docker Image"
    echo "請先執行: docker build -t paas-api:latest ."
    exit 1
fi

# 執行測試
test_swagger_enabled
echo ""
test_swagger_disabled

echo ""
echo "🎉 Swagger 控制功能測試完成！"
echo ""
echo "📚 使用說明:"
echo "• 啟用 Swagger: docker run -e EnableSwagger=true -p 8080:8080 paas-api:latest"
echo "• 停用 Swagger: docker run -e EnableSwagger=false -p 8080:8080 paas-api:latest"
echo "• 預設行為: 開發環境啟用，生產環境停用"
