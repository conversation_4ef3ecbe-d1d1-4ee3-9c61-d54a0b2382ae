# API 程式規格書

※ 所有 API 路徑皆採用版本管理，預設使用 `/v1/` 前綴。  
※ 每個端點皆要求正確的認證與授權，否則返回 `401 Unauthorized` 或 `403 Forbidden`。  
※ 錯誤回應統一格式如下：
```json
{
  "error_code": "ERR_INVALID_INPUT",
  "message": "輸入資料格式錯誤",
  "details": "詳細說明或驗證資訊"
}
```
※ 各查詢 API 均支持分頁（limit/offset）與排序參數。

---

## 1. 租戶管理 (tenants)

### 1.1 GET /v1/tenants
**說明**: 取得所有租戶列表，支援依 `is_active` 條件篩選與分頁。  
**SQL 語法**:
```SQL
SELECT tenant_id, name, contact_email, is_active, billing_currency, legal_address, created_at, updated_at
FROM tenants
WHERE (:is_active IS NULL OR is_active = :is_active)
ORDER BY created_at DESC
LIMIT :limit OFFSET :offset;
```
**範例請求**:
```bash
GET /v1/tenants?is_active=true&limit=20&offset=0
```
**成功回應 (200 OK)**:
```json
[
  {
    "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
    "name": "範例科技",
    "contact_email": "<EMAIL>",
    "is_active": true,
    "billing_currency": "123e4567-e89b-12d3-a456-426614174000",
    "legal_address": {
      "street": "民生東路三段",
      "city": "台北市",
      "country": "TW",
      "postalCode": "104"
    },
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-01T00:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

---

### 1.2 GET /v1/tenants/{tenant_id}
**說明**: 根據 `tenant_id` 獲取特定租戶資訊。  
**SQL 語法**:
```SQL
SELECT tenant_id, name, contact_email, is_active, billing_currency, legal_address, created_at, updated_at
FROM tenants
WHERE tenant_id = :tenant_id;
```
**範例請求**:
```bash
GET /v1/tenants/b836f4ec-3b77-4972-bccf-1234567890ab
```
**成功回應 (200 OK)**:
```json
{
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "name": "範例科技",
  "contact_email": "<EMAIL>",
  "is_active": true,
  "billing_currency": "123e4567-e89b-12d3-a456-426614174000",
  "legal_address": {
    "street": "民生東路三段",
    "city": "台北市",
    "country": "TW",
    "postalCode": "104"
  },
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`
- `403 Forbidden`

---

### 1.3 POST /v1/tenants
**說明**: 建立新租戶。  
**SQL 語法**:
```SQL
INSERT INTO tenants (name, contact_email, billing_currency, legal_address)
VALUES (:name, :contact_email, :billing_currency, :legal_address)
RETURNING tenant_id, name, contact_email, is_active, billing_currency, legal_address, created_at, updated_at;
```
**範例請求**:
```bash
POST /v1/tenants
Content-Type: application/json

{
  "name": "範例科技",
  "contact_email": "<EMAIL>",
  "billing_currency": "123e4567-e89b-12d3-a456-426614174000",
  "legal_address": {
    "street": "民生東路三段",
    "city": "台北市",
    "country": "TW",
    "postalCode": "104"
  }
}
```
**成功回應 (201 Created)**:
```json
{
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "name": "範例科技",
  "contact_email": "<EMAIL>",
  "is_active": true,
  "billing_currency": "123e4567-e89b-12d3-a456-426614174000",
  "legal_address": {
    "street": "民生東路三段",
    "city": "台北市",
    "country": "TW",
    "postalCode": "104"
  },
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`
- `401 Unauthorized` / `403 Forbidden`

---

### 1.4 PUT /v1/tenants/{tenant_id}
**說明**: 更新指定租戶資訊（支援部分欄位更新）。  
**SQL 語法** (示例，針對部分欄位更新):
```SQL
UPDATE tenants
SET name = COALESCE(:name, name),
    contact_email = COALESCE(:contact_email, contact_email),
    is_active = COALESCE(:is_active, is_active),
    legal_address = jsonb_strip_nulls(legal_address || :legal_address),
    updated_at = CURRENT_TIMESTAMP
WHERE tenant_id = :tenant_id
RETURNING tenant_id, name, contact_email, is_active, billing_currency, legal_address, created_at, updated_at;
```
**範例請求**:
```bash
PUT /v1/tenants/b836f4ec-3b77-4972-bccf-1234567890ab
Content-Type: application/json

{
  "name": "新公司名稱",
  "contact_email": "<EMAIL>",
  "is_active": false,
  "legal_address": {
    "street": "新地址路100號"
  }
}
```
**成功回應 (200 OK)**:
```json
{
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "name": "新公司名稱",
  "contact_email": "<EMAIL>",
  "is_active": false,
  "billing_currency": "123e4567-e89b-12d3-a456-426614174000",
  "legal_address": {
    "street": "新地址路100號"
  },
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-05T10:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`
- `400 Bad Request`

---

### 1.5 DELETE /v1/tenants/{tenant_id}
**說明**: 刪除指定租戶，或依業務需求將其標記為停用。  
**SQL 語法** (示例：標記停用):
```SQL
UPDATE tenants
SET is_active = false,
    updated_at = CURRENT_TIMESTAMP
WHERE tenant_id = :tenant_id;
```
**範例請求**:
```bash
DELETE /v1/tenants/b836f4ec-3b77-4972-bccf-1234567890ab
```
**成功回應**:
- `204 No Content` 或 `200 OK`
**錯誤回應**:
- `404 Not Found`
- `403 Forbidden`

---

## 2. API 閘道管理 (apis)

### 2.1 GET /v1/apis
**說明**: 取得所有 API 列表，可依 `tenant_id` 過濾。  
**SQL 語法**:
```SQL
SELECT api_id, tenant_id, name, description, version, rate_limit_per_minute, is_active, created_at, updated_at
FROM apis
WHERE (:tenant_id IS NULL OR tenant_id = :tenant_id)
ORDER BY created_at DESC
LIMIT :limit OFFSET :offset;
```
**範例請求**:
```bash
GET /v1/apis?tenant_id=b836f4ec-3b77-4972-bccf-1234567890ab&limit=20&offset=0
```
**成功回應 (200 OK)**:
```json
[
  {
    "api_id": "967f4ec0-b235-48fa-bddf-9876543210aa",
    "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
    "name": "Payment API",
    "description": "金流交易API服務",
    "version": "1.0.0",
    "rate_limit_per_minute": 5000,
    "is_active": true,
    "created_at": "2025-01-02T00:00:00Z",
    "updated_at": "2025-01-02T00:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

---

### 2.2 GET /v1/apis/{api_id}
**說明**: 根據 `api_id` 獲取特定 API 詳細資訊。  
**SQL 語法**:
```SQL
SELECT api_id, tenant_id, name, description, version, rate_limit_per_minute, is_active, created_at, updated_at
FROM apis
WHERE api_id = :api_id;
```
**範例請求**:
```bash
GET /v1/apis/967f4ec0-b235-48fa-bddf-9876543210aa
```
**成功回應 (200 OK)**:
```json
{
  "api_id": "967f4ec0-b235-48fa-bddf-9876543210aa",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "name": "Payment API",
  "description": "金流交易API服務",
  "version": "1.0.0",
  "rate_limit_per_minute": 5000,
  "is_active": true,
  "created_at": "2025-01-02T00:00:00Z",
  "updated_at": "2025-01-02T00:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`
- `403 Forbidden`

---

### 2.3 POST /v1/apis
**說明**: 為指定租戶創建新的 API 服務。  
**SQL 語法**:
```SQL
INSERT INTO apis (tenant_id, name, description, version, rate_limit_per_minute)
VALUES (:tenant_id, :name, :description, :version, :rate_limit_per_minute)
RETURNING api_id, tenant_id, name, description, version, rate_limit_per_minute, is_active, created_at, updated_at;
```
**範例請求**:
```bash
POST /v1/apis
Content-Type: application/json

{
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "name": "Order API",
  "description": "訂單處理API",
  "version": "1.0.0",
  "rate_limit_per_minute": 2000
}
```
**成功回應 (201 Created)**:
```json
{
  "api_id": "967f4ec0-b235-48fa-bddf-9876543210aa",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "name": "Order API",
  "description": "訂單處理API",
  "version": "1.0.0",
  "rate_limit_per_minute": 2000,
  "is_active": true,
  "created_at": "2025-01-05T00:00:00Z",
  "updated_at": "2025-01-05T00:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`
- `401 Unauthorized` / `403 Forbidden`

---

### 2.4 PUT /v1/apis/{api_id}
**說明**: 更新特定 API 的資訊（例如版本、流量限制、啟用狀態）。  
**SQL 語法**:
```SQL
UPDATE apis
SET version = COALESCE(:version, version),
    rate_limit_per_minute = COALESCE(:rate_limit_per_minute, rate_limit_per_minute),
    is_active = COALESCE(:is_active, is_active),
    updated_at = CURRENT_TIMESTAMP
WHERE api_id = :api_id
RETURNING api_id, tenant_id, name, description, version, rate_limit_per_minute, is_active, created_at, updated_at;
```
**範例請求**:
```bash
PUT /v1/apis/967f4ec0-b235-48fa-bddf-9876543210aa
Content-Type: application/json

{
  "version": "1.1.0",
  "rate_limit_per_minute": 3000,
  "is_active": false
}
```
**成功回應 (200 OK)**:
```json
{
  "api_id": "967f4ec0-b235-48fa-bddf-9876543210aa",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "name": "Order API",
  "description": "訂單處理API",
  "version": "1.1.0",
  "rate_limit_per_minute": 3000,
  "is_active": false,
  "created_at": "2025-01-05T00:00:00Z",
  "updated_at": "2025-01-06T09:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`
- `400 Bad Request`

---

### 2.5 DELETE /v1/apis/{api_id}
**說明**: 刪除特定 API。  
**SQL 語法**:
```SQL
DELETE FROM apis
WHERE api_id = :api_id;
```
**範例請求**:
```bash
DELETE /v1/apis/967f4ec0-b235-48fa-bddf-9876543210aa
```
**成功回應**:
- `204 No Content`
**錯誤回應**:
- `404 Not Found`
- `403 Forbidden`

---

## 3. API 使用紀錄 (api_usage)

### 3.1 GET /v1/api_usage
**說明**: 獲取所有 API 使用紀錄，可依 `api_id`、時間區間等條件篩選與分頁。  
**SQL 語法**:
```SQL
SELECT usage_id, api_id, key_id, request_ip, response_time, status_code, created_at
FROM api_usage
WHERE (:api_id IS NULL OR api_id = :api_id)
  AND created_at BETWEEN :start_time AND :end_time
ORDER BY created_at DESC
LIMIT :limit OFFSET :offset;
```
**範例請求**:
```bash
GET /v1/api_usage?api_id=967f4ec0-b235-48fa-bddf-9876543210aa&start_time=2025-01-01T00:00:00Z&end_time=2025-01-31T23:59:59Z&limit=20&offset=0
```
**成功回應 (200 OK)**:
```json
[
  {
    "usage_id": "ab12cd34-ef56-7890-ab12-34567890abcd",
    "api_id": "967f4ec0-b235-48fa-bddf-9876543210aa",
    "key_id": "12345678-1234-5678-1234-567812345678",
    "request_ip": "***********",
    "response_time": 150.5,
    "status_code": 200,
    "created_at": "2025-01-05T10:20:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

---

### 3.2 GET /v1/api_usage/{usage_id}
**說明**: 根據使用紀錄 ID 獲取特定紀錄。  
**SQL 語法**:
```SQL
SELECT usage_id, api_id, key_id, request_ip, response_time, status_code, created_at
FROM api_usage
WHERE usage_id = :usage_id;
```
**範例請求**:
```bash
GET /v1/api_usage/ab12cd34-ef56-7890-ab12-34567890abcd
```
**成功回應 (200 OK)**:
```json
{
  "usage_id": "ab12cd34-ef56-7890-ab12-34567890abcd",
  "api_id": "967f4ec0-b235-48fa-bddf-9876543210aa",
  "key_id": "12345678-1234-5678-1234-567812345678",
  "request_ip": "***********",
  "response_time": 150.5,
  "status_code": 200,
  "created_at": "2025-01-05T10:20:00Z"
}
```
**錯誤回應**:
- `404 Not Found`

---

### 3.3 POST /v1/api_usage
**說明**: 創建新的 API 使用紀錄（通常由 API Gateway 或後端自動呼叫）。  
**SQL 語法**:
```SQL
INSERT INTO api_usage (api_id, key_id, request_ip, response_time, status_code)
VALUES (:api_id, :key_id, :request_ip, :response_time, :status_code)
RETURNING usage_id, api_id, key_id, request_ip, response_time, status_code, created_at;
```
**範例請求**:
```bash
POST /v1/api_usage
Content-Type: application/json

{
  "api_id": "967f4ec0-b235-48fa-bddf-9876543210aa",
  "key_id": "12345678-1234-5678-1234-567812345678",
  "request_ip": "***********",
  "response_time": 200.0,
  "status_code": 200
}
```
**成功回應 (201 Created)**:
```json
{
  "usage_id": "ab12cd34-ef56-7890-ab12-34567890abcd",
  "api_id": "967f4ec0-b235-48fa-bddf-9876543210aa",
  "key_id": "12345678-1234-5678-1234-567812345678",
  "request_ip": "***********",
  "response_time": 200.0,
  "status_code": 200,
  "created_at": "2025-01-05T10:20:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

---

## 4. 帳單與付款管理 (billing)

### 4.1 GET /v1/billing
**說明**: 獲取所有帳單資訊，可依 `tenant_id`、`status`、時間區間等條件篩選與分頁。  
**SQL 語法**:
```SQL
SELECT record_id, tenant_id, amount, currency_id, payment_method_id, status, due_date, paid_at, created_at
FROM billing_records
WHERE (:tenant_id IS NULL OR tenant_id = :tenant_id)
  AND (:status IS NULL OR status = :status)
ORDER BY created_at DESC
LIMIT :limit OFFSET :offset;
```
**範例請求**:
```bash
GET /v1/billing?tenant_id=b836f4ec-3b77-4972-bccf-1234567890ab&status=pending&limit=20&offset=0
```
**成功回應 (200 OK)**:
```json
[
  {
    "record_id": "aaaabbbb-cccc-dddd-eeee-ffffffffffff",
    "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
    "amount": 100.0,
    "currency_id": "123e4567-e89b-12d3-a456-426614174000",
    "payment_method_id": "11223344-5566-7788-99aa-bbccddeeff00",
    "status": "pending",
    "due_date": "2025-02-01T00:00:00Z",
    "paid_at": null,
    "created_at": "2025-01-10T00:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

---

### 4.2 GET /v1/billing/{billing_id}
**說明**: 根據帳單 ID 獲取特定帳單資訊。  
**SQL 語法**:
```SQL
SELECT record_id, tenant_id, amount, currency_id, payment_method_id, status, due_date, paid_at, created_at
FROM billing_records
WHERE record_id = :billing_id;
```
**範例請求**:
```bash
GET /v1/billing/aaaabbbb-cccc-dddd-eeee-ffffffffffff
```
**成功回應 (200 OK)**:
```json
{
  "record_id": "aaaabbbb-cccc-dddd-eeee-ffffffffffff",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "amount": 100.0,
  "currency_id": "123e4567-e89b-12d3-a456-426614174000",
  "payment_method_id": "11223344-5566-7788-99aa-bbccddeeff00",
  "status": "pending",
  "due_date": "2025-02-01T00:00:00Z",
  "paid_at": null,
  "created_at": "2025-01-10T00:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`

---

### 4.3 POST /v1/billing
**說明**: 創建新的帳單。  
**SQL 語法**:
```SQL
INSERT INTO billing_records (tenant_id, amount, currency_id, payment_method_id, due_date)
VALUES (:tenant_id, :amount, :currency_id, :payment_method_id, :due_date)
RETURNING record_id, tenant_id, amount, currency_id, payment_method_id, status, due_date, paid_at, created_at;
```
**範例請求**:
```bash
POST /v1/billing
Content-Type: application/json

{
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "amount": 150.75,
  "currency_id": "123e4567-e89b-12d3-a456-426614174000",
  "payment_method_id": "11223344-5566-7788-99aa-bbccddeeff00",
  "due_date": "2025-03-01T00:00:00Z"
}
```
**成功回應 (201 Created)**:
```json
{
  "record_id": "aaaabbbb-cccc-dddd-eeee-ffffffffffff",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "amount": 150.75,
  "currency_id": "123e4567-e89b-12d3-a456-426614174000",
  "payment_method_id": "11223344-5566-7788-99aa-bbccddeeff00",
  "status": "pending",
  "due_date": "2025-03-01T00:00:00Z",
  "paid_at": null,
  "created_at": "2025-01-15T00:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

---

### 4.4 PUT /v1/billing/{billing_id}
**說明**: 更新特定帳單（例如變更狀態或付款時間）。  
**SQL 語法**:
```SQL
UPDATE billing_records
SET status = COALESCE(:status, status),
    paid_at = COALESCE(:paid_at, paid_at),
    created_at = created_at,
    updated_at = CURRENT_TIMESTAMP
WHERE record_id = :billing_id
RETURNING record_id, tenant_id, amount, currency_id, payment_method_id, status, due_date, paid_at, created_at;
```
**範例請求**:
```bash
PUT /v1/billing/aaaabbbb-cccc-dddd-eeee-ffffffffffff
Content-Type: application/json

{
  "status": "paid",
  "paid_at": "2025-01-20T12:00:00Z"
}
```
**成功回應 (200 OK)**:
```json
{
  "record_id": "aaaabbbb-cccc-dddd-eeee-ffffffffffff",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "amount": 150.75,
  "currency_id": "123e4567-e89b-12d3-a456-426614174000",
  "payment_method_id": "11223344-5566-7788-99aa-bbccddeeff00",
  "status": "paid",
  "due_date": "2025-03-01T00:00:00Z",
  "paid_at": "2025-01-20T12:00:00Z",
  "created_at": "2025-01-15T00:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`
- `400 Bad Request`

---

### 4.5 DELETE /v1/billing/{billing_id}
**說明**: 刪除特定帳單。  
**SQL 語法**:
```SQL
DELETE FROM billing_records
WHERE record_id = :billing_id;
```
**範例請求**:
```bash
DELETE /v1/billing/aaaabbbb-cccc-dddd-eeee-ffffffffffff
```
**成功回應**:
- `204 No Content`
**錯誤回應**:
- `404 Not Found`
- `403 Forbidden`

---

## 5. 幣別管理 (currencies)

### 5.1 GET /v1/currencies
**說明**: 獲取所有幣別資訊。  
**SQL 語法**:
```SQL
SELECT currency_id, code, symbol, exchange_rate, is_active, last_updated_at, created_at
FROM currencies
ORDER BY created_at DESC;
```
**範例請求**:
```bash
GET /v1/currencies
```
**成功回應 (200 OK)**:
```json
[
  {
    "currency_id": "123e4567-e89b-12d3-a456-426614174000",
    "code": "USD",
    "symbol": "$",
    "exchange_rate": 1.0,
    "is_active": true,
    "last_updated_at": "2025-01-01T00:00:00Z",
    "created_at": "2025-01-01T00:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized` / `403 Forbidden`

---

### 5.2 GET /v1/currencies/{currency_id}
**說明**: 根據幣別 ID 獲取特定資訊。  
**SQL 語法**:
```SQL
SELECT currency_id, code, symbol, exchange_rate, is_active, last_updated_at, created_at
FROM currencies
WHERE currency_id = :currency_id;
```
**範例請求**:
```bash
GET /v1/currencies/123e4567-e89b-12d3-a456-426614174000
```
**成功回應 (200 OK)**:
```json
{
  "currency_id": "123e4567-e89b-12d3-a456-426614174000",
  "code": "USD",
  "symbol": "$",
  "exchange_rate": 1.0,
  "is_active": true,
  "last_updated_at": "2025-01-01T00:00:00Z",
  "created_at": "2025-01-01T00:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`

---

### 5.3 POST /v1/currencies
**說明**: 創建新的幣別。  
**SQL 語法**:
```SQL
INSERT INTO currencies (code, symbol, exchange_rate, is_active)
VALUES (:code, :symbol, :exchange_rate, :is_active)
RETURNING currency_id, code, symbol, exchange_rate, is_active, last_updated_at, created_at;
```
**範例請求**:
```bash
POST /v1/currencies
Content-Type: application/json

{
  "code": "TWD",
  "symbol": "NT$",
  "exchange_rate": 30.5,
  "is_active": true
}
```
**成功回應 (201 Created)**:
```json
{
  "currency_id": "123e4567-e89b-12d3-a456-426614174001",
  "code": "TWD",
  "symbol": "NT$",
  "exchange_rate": 30.5,
  "is_active": true,
  "last_updated_at": "2025-01-10T00:00:00Z",
  "created_at": "2025-01-10T00:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

---

### 5.4 PUT /v1/currencies/{currency_id}
**說明**: 更新特定幣別資訊（例如匯率）。  
**SQL 語法**:
```SQL
UPDATE currencies
SET exchange_rate = :exchange_rate,
    is_active = :is_active,
    last_updated_at = CURRENT_TIMESTAMP
WHERE currency_id = :currency_id
RETURNING currency_id, code, symbol, exchange_rate, is_active, last_updated_at, created_at;
```
**範例請求**:
```bash
PUT /v1/currencies/123e4567-e89b-12d3-a456-426614174001
Content-Type: application/json

{
  "exchange_rate": 31.0,
  "is_active": false
}
```
**成功回應 (200 OK)**:
```json
{
  "currency_id": "123e4567-e89b-12d3-a456-426614174001",
  "code": "TWD",
  "symbol": "NT$",
  "exchange_rate": 31.0,
  "is_active": false,
  "last_updated_at": "2025-01-15T00:00:00Z",
  "created_at": "2025-01-10T00:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

---

### 5.5 DELETE /v1/currencies/{currency_id}
**說明**: 刪除特定幣別。  
**SQL 語法**:
```SQL
DELETE FROM currencies
WHERE currency_id = :currency_id;
```
**範例請求**:
```bash
DELETE /v1/currencies/123e4567-e89b-12d3-a456-426614174001
```
**成功回應**:
- `204 No Content`
**錯誤回應**:
- `404 Not Found`
- `403 Forbidden`

---

## 6. SSO 登入管理 (sso_providers 與 tenant_sso)

※ 實際資料表為 `sso_configs`，此處 API 路徑僅供參考。

### 6.1 GET /v1/sso_providers
**說明**: 獲取所有 SSO 提供者列表（可依常數或額外資料表實作）。  
**SQL 語法**: （此範例僅模擬返回固定資料）
```SQL
-- 模擬查詢，實際可使用資料表或常數配置
SELECT '1111-2222-3333-4444' AS provider_id, 'google' AS provider_type, 'Google OAuth2' AS description
UNION ALL
SELECT '5555-6666-7777-8888', 'microsoft', 'Microsoft Azure AD';
```
**範例請求**:
```bash
GET /v1/sso_providers
```
**成功回應 (200 OK)**:
```json
[
  {
    "provider_id": "1111-2222-3333-4444",
    "provider_type": "google",
    "description": "Google OAuth2"
  },
  {
    "provider_id": "5555-6666-7777-8888",
    "provider_type": "microsoft",
    "description": "Microsoft Azure AD"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

---

### 6.2 GET /v1/sso_providers/{provider_id}
**說明**: 根據提供者 ID 獲取特定 SSO 提供者資訊。  
**SQL 語法**: （模擬查詢）
```SQL
SELECT '1111-2222-3333-4444' AS provider_id, 'google' AS provider_type, 'Google OAuth2' AS description,
       '{"auth_url": "https://accounts.google.com/o/oauth2/auth"}'::JSONB AS metadata
WHERE '1111-2222-3333-4444' = :provider_id;
```
**範例請求**:
```bash
GET /v1/sso_providers/1111-2222-3333-4444
```
**成功回應 (200 OK)**:
```json
{
  "provider_id": "1111-2222-3333-4444",
  "provider_type": "google",
  "description": "Google OAuth2",
  "metadata": {
    "auth_url": "https://accounts.google.com/o/oauth2/auth"
  }
}
```
**錯誤回應**:
- `404 Not Found`

---

### 6.3 POST /v1/tenant_sso
**說明**: 為租戶創建 SSO 設定（對應 sso_configs）。  
**SQL 語法**:
```SQL
INSERT INTO sso_configs (tenant_id, provider_type, client_id, client_secret, metadata)
VALUES (:tenant_id, :provider_type, :client_id, pgp_sym_encrypt(:client_secret, 'key'), :metadata)
RETURNING config_id, tenant_id, provider_type, client_id, client_secret, metadata, is_active, created_at, updated_at;
```
**範例請求**:
```bash
POST /v1/tenant_sso
Content-Type: application/json

{
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "provider_type": "google",
  "client_id": "your-google-client-id",
  "client_secret": "your-encrypted-secret",
  "metadata": {
    "auth_url": "https://accounts.google.com/o/oauth2/auth",
    "token_url": "https://oauth2.googleapis.com/token"
  }
}
```
**成功回應 (201 Created)**:
```json
{
  "config_id": "cdef1234-5678-90ab-cdef-1234567890ab",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "provider_type": "google",
  "client_id": "your-google-client-id",
  "client_secret": "your-encrypted-secret",
  "metadata": {
    "auth_url": "https://accounts.google.com/o/oauth2/auth",
    "token_url": "https://oauth2.googleapis.com/token"
  },
  "is_active": true,
  "created_at": "2025-01-20T00:00:00Z",
  "updated_at": "2025-01-20T00:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

---

### 6.4 DELETE /v1/tenant_sso/{sso_id}
**說明**: 刪除租戶的 SSO 設定。  
**SQL 語法**:
```SQL
DELETE FROM sso_configs
WHERE config_id = :sso_id;
```
**範例請求**:
```bash
DELETE /v1/tenant_sso/cdef1234-5678-90ab-cdef-1234567890ab
```
**成功回應**:
- `204 No Content`
**錯誤回應**:
- `404 Not Found`
- `403 Forbidden`

---

## 7. 多 API Keys 管理 (api_keys)

### 7.1 GET /v1/api_keys
**說明**: 獲取所有 API 金鑰，可依 `tenant_id` 或 `status` 過濾與分頁。  
**SQL 語法**:
```SQL
SELECT key_id, tenant_id, api_key, status, scopes, expiration_at, generated_at, last_used_at, created_at
FROM api_keys
WHERE (:tenant_id IS NULL OR tenant_id = :tenant_id)
  AND (:status IS NULL OR status = :status)
ORDER BY created_at DESC
LIMIT :limit OFFSET :offset;
```
**範例請求**:
```bash
GET /v1/api_keys?tenant_id=b836f4ec-3b77-4972-bccf-1234567890ab&status=active&limit=20&offset=0
```
**成功回應 (200 OK)**:
```json
[
  {
    "key_id": "12345678-1234-5678-1234-567812345678",
    "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
    "api_key": "generated-key",
    "status": "active",
    "scopes": { "read": true, "write": false },
    "expiration_at": null,
    "generated_at": "2025-01-01T08:00:00Z",
    "last_used_at": null,
    "created_at": "2025-01-01T08:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

---

### 7.2 GET /v1/api_keys/{key_id}
**說明**: 根據金鑰 ID 獲取特定金鑰資訊。  
**SQL 語法**:
```SQL
SELECT key_id, tenant_id, api_key, status, scopes, expiration_at, generated_at, last_used_at, created_at
FROM api_keys
WHERE key_id = :key_id;
```
**範例請求**:
```bash
GET /v1/api_keys/12345678-1234-5678-1234-567812345678
```
**成功回應 (200 OK)**:
```json
{
  "key_id": "12345678-1234-5678-1234-567812345678",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "api_key": "generated-key",
  "status": "active",
  "scopes": { "read": true, "write": false },
  "expiration_at": null,
  "generated_at": "2025-01-01T08:00:00Z",
  "last_used_at": "2025-01-02T10:00:00Z",
  "created_at": "2025-01-01T08:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`

---

### 7.3 POST /v1/api_keys
**說明**: 創建新的 API 金鑰。  
**SQL 語法**:
```SQL
INSERT INTO api_keys (tenant_id, api_key, scopes, expiration_at)
VALUES (:tenant_id, :api_key, :scopes, :expiration_at)
RETURNING key_id, tenant_id, api_key, status, scopes, expiration_at, generated_at, last_used_at, created_at;
```
**範例請求**:
```bash
POST /v1/api_keys
Content-Type: application/json

{
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "api_key": "some-random-string",
  "scopes": { "read": true, "write": true },
  "expiration_at": "2025-12-31T00:00:00Z"
}
```
**成功回應 (201 Created)**:
```json
{
  "key_id": "00000000-1111-2222-3333-444444444444",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "api_key": "some-random-string",
  "status": "active",
  "scopes": { "read": true, "write": true },
  "expiration_at": "2025-12-31T00:00:00Z",
  "generated_at": "2025-01-10T09:00:00Z",
  "last_used_at": null,
  "created_at": "2025-01-10T09:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

---

### 7.4 PUT /v1/api_keys/{key_id}
**說明**: 更新特定金鑰（例如撤銷或延長有效期）。  
**SQL 語法**:
```SQL
UPDATE api_keys
SET status = COALESCE(:status, status),
    expiration_at = :expiration_at,
    updated_at = CURRENT_TIMESTAMP
WHERE key_id = :key_id
RETURNING key_id, tenant_id, api_key, status, scopes, expiration_at, generated_at, last_used_at, created_at;
```
**範例請求**:
```bash
PUT /v1/api_keys/00000000-1111-2222-3333-444444444444
Content-Type: application/json

{
  "status": "revoked",
  "expiration_at": null
}
```
**成功回應 (200 OK)**:
```json
{
  "key_id": "00000000-1111-2222-3333-444444444444",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "api_key": "some-random-string",
  "status": "revoked",
  "scopes": { "read": true, "write": true },
  "expiration_at": null,
  "generated_at": "2025-01-10T09:00:00Z",
  "last_used_at": null,
  "created_at": "2025-01-10T09:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

---

### 7.5 DELETE /v1/api_keys/{key_id}
**說明**: 刪除特定金鑰。  
**SQL 語法**:
```SQL
DELETE FROM api_keys
WHERE key_id = :key_id;
```
**範例請求**:
```bash
DELETE /v1/api_keys/00000000-1111-2222-3333-444444444444
```
**成功回應**:
- `204 No Content`
**錯誤回應**:
- `404 Not Found`
- `403 Forbidden`

---

## 8. 安全性日誌 (security_logs)

### 8.1 GET /v1/security_logs
**說明**: 獲取所有安全性日誌，可依租戶、事件類型、時間區間等條件篩選與分頁。  
**SQL 語法**:
```SQL
SELECT log_id, tenant_id, user_id, event_type_id, ip_address, user_agent, details, created_at
FROM security_logs
WHERE (:tenant_id IS NULL OR tenant_id = :tenant_id)
  AND (:event_type_id IS NULL OR event_type_id = :event_type_id)
  AND created_at BETWEEN :start_time AND :end_time
ORDER BY created_at DESC
LIMIT :limit OFFSET :offset;
```
**範例請求**:
```bash
GET /v1/security_logs?tenant_id=b836f4ec-3b77-4972-bccf-1234567890ab&start_time=2025-01-01T00:00:00Z&end_time=2025-01-31T23:59:59Z&limit=20&offset=0
```
**成功回應 (200 OK)**:
```json
[
  {
    "log_id": "abcdef12-3456-7890-abcd-ef1234567890",
    "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
    "user_id": "99999999-aaaa-bbbb-cccc-ddddeeeeefff",
    "event_type_id": "88888888-1111-2222-3333-444444444444",
    "ip_address": "***********",
    "user_agent": "Mozilla/5.0",
    "details": { "action": "login_failed", "reason": "invalid password" },
    "created_at": "2025-01-15T10:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

---

### 8.2 GET /v1/security_logs/{log_id}
**說明**: 根據日誌 ID 獲取特定安全性日誌詳情。  
**SQL 語法**:
```SQL
SELECT log_id, tenant_id, user_id, event_type_id, ip_address, user_agent, details, created_at
FROM security_logs
WHERE log_id = :log_id;
```
**範例請求**:
```bash
GET /v1/security_logs/abcdef12-3456-7890-abcd-ef1234567890
```
**成功回應 (200 OK)**:
```json
{
  "log_id": "abcdef12-3456-7890-abcd-ef1234567890",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "user_id": "99999999-aaaa-bbbb-cccc-ddddeeeeefff",
  "event_type_id": "88888888-1111-2222-3333-444444444444",
  "ip_address": "***********",
  "user_agent": "Mozilla/5.0",
  "details": { "action": "login_failed", "reason": "invalid password" },
  "created_at": "2025-01-15T10:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`

---

## 9. 監控指標 (monitoring_metrics)

### 9.1 GET /v1/monitoring_metrics
**說明**: 獲取所有監控指標資訊，可依 `tenant_id`、`api_id`、`metric_type_id`、時間區間等條件篩選與分頁。  
**SQL 語法**:
```SQL
SELECT data_id, metric_type_id, tenant_id, api_id, value, tags, timestamp
FROM metrics_data
WHERE (:tenant_id IS NULL OR tenant_id = :tenant_id)
  AND (:api_id IS NULL OR api_id = :api_id)
  AND (:metric_type_id IS NULL OR metric_type_id = :metric_type_id)
  AND timestamp BETWEEN :start_time AND :end_time
ORDER BY timestamp DESC
LIMIT :limit OFFSET :offset;
```
**範例請求**:
```bash
GET /v1/monitoring_metrics?tenant_id=b836f4ec-3b77-4972-bccf-1234567890ab&start_time=2025-01-20T00:00:00Z&end_time=2025-01-20T23:59:59Z&limit=20&offset=0
```
**成功回應 (200 OK)**:
```json
[
  {
    "data_id": "f1e2d3c4-b5a6-7890-1234-56789abcdef0",
    "metric_type_id": "11111111-2222-3333-4444-555555555555",
    "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
    "api_id": "967f4ec0-b235-48fa-bddf-9876543210aa",
    "value": 10.5,
    "tags": { "environment": "production" },
    "timestamp": "2025-01-20T12:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

---

### 9.2 GET /v1/monitoring_metrics/{metric_id}
**說明**: 根據指標 ID (`data_id`) 獲取特定監控指標詳情。  
**SQL 語法**:
```SQL
SELECT data_id, metric_type_id, tenant_id, api_id, value, tags, timestamp
FROM metrics_data
WHERE data_id = :data_id;
```
**範例請求**:
```bash
GET /v1/monitoring_metrics/f1e2d3c4-b5a6-7890-1234-56789abcdef0
```
**成功回應 (200 OK)**:
```json
{
  "data_id": "f1e2d3c4-b5a6-7890-1234-56789abcdef0",
  "metric_type_id": "11111111-2222-3333-4444-555555555555",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "api_id": "967f4ec0-b235-48fa-bddf-9876543210aa",
  "value": 10.5,
  "tags": { "environment": "production" },
  "timestamp": "2025-01-20T12:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`

---

## 10. 通知系統 (notifications)

### 10.1 GET /v1/notifications
**說明**: 獲取所有通知，可依 `tenant_id`、`user_id`、`status`、`priority` 等條件篩選與分頁。  
**SQL 語法**:
```SQL
SELECT notification_id, type_id, tenant_id, user_id, title, message, status, priority, expires_at, created_at
FROM notifications
WHERE (:tenant_id IS NULL OR tenant_id = :tenant_id)
  AND (:user_id IS NULL OR user_id = :user_id)
  AND (:status IS NULL OR status = :status)
  AND (:priority IS NULL OR priority = :priority)
ORDER BY created_at DESC
LIMIT :limit OFFSET :offset;
```
**範例請求**:
```bash
GET /v1/notifications?tenant_id=b836f4ec-3b77-4972-bccf-1234567890ab&limit=20&offset=0
```
**成功回應 (200 OK)**:
```json
[
  {
    "notification_id": "99998888-7777-6666-5555-444433332222",
    "type_id": "1111-2222-3333-4444-555566667777",
    "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
    "user_id": "99999999-aaaa-bbbb-cccc-ddddeeeeefff",
    "title": "系統維護通知",
    "message": "系統將於今晚12點進行維護。",
    "status": "unread",
    "priority": "high",
    "expires_at": "2025-01-30T00:00:00Z",
    "created_at": "2025-01-20T00:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

---

### 10.2 GET /v1/notifications/{notification_id}
**說明**: 根據通知 ID 獲取特定通知詳情。  
**SQL 語法**:
```SQL
SELECT notification_id, type_id, tenant_id, user_id, title, message, status, priority, expires_at, created_at
FROM notifications
WHERE notification_id = :notification_id;
```
**範例請求**:
```bash
GET /v1/notifications/99998888-7777-6666-5555-444433332222
```
**成功回應 (200 OK)**:
```json
{
  "notification_id": "99998888-7777-6666-5555-444433332222",
  "type_id": "1111-2222-3333-4444-555566667777",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "user_id": "99999999-aaaa-bbbb-cccc-ddddeeeeefff",
  "title": "系統維護通知",
  "message": "系統將於今晚12點進行維護。",
  "status": "unread",
  "priority": "high",
  "expires_at": "2025-01-30T00:00:00Z",
  "created_at": "2025-01-20T00:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`

---

### 10.3 POST /v1/notifications
**說明**: 創建新的通知。  
**SQL 語法**:
```SQL
INSERT INTO notifications (type_id, tenant_id, user_id, title, message, priority, expires_at)
VALUES (:type_id, :tenant_id, :user_id, :title, :message, :priority, :expires_at)
RETURNING notification_id, type_id, tenant_id, user_id, title, message, status, priority, expires_at, created_at;
```
**範例請求**:
```bash
POST /v1/notifications
Content-Type: application/json

{
  "type_id": "1111-2222-3333-4444-555566667777",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "user_id": "99999999-aaaa-bbbb-cccc-ddddeeeeefff",
  "title": "新的通知",
  "message": "這是通知內容",
  "priority": "medium",
  "expires_at": "2025-02-01T00:00:00Z"
}
```
**成功回應 (201 Created)**:
```json
{
  "notification_id": "99998888-7777-6666-5555-444433332222",
  "type_id": "1111-2222-3333-4444-555566667777",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "user_id": "99999999-aaaa-bbbb-cccc-ddddeeeeefff",
  "title": "新的通知",
  "message": "這是通知內容",
  "status": "unread",
  "priority": "medium",
  "expires_at": "2025-02-01T00:00:00Z",
  "created_at": "2025-01-22T00:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

---

## 11. 使用者與權限管理 (users, roles, permissions)

### 11.1 使用者管理 (users)

#### 11.1.1 GET /v1/users
**說明**: 獲取所有使用者，支援依 `tenant_id`、`role_id` 篩選與分頁。  
**SQL 語法**:
```SQL
SELECT user_id, tenant_id, username, email, role_id, created_at, updated_at
FROM users
WHERE (:tenant_id IS NULL OR tenant_id = :tenant_id)
  AND (:role_id IS NULL OR role_id = :role_id)
ORDER BY created_at DESC
LIMIT :limit OFFSET :offset;
```
**範例請求**:
```bash
GET /v1/users?tenant_id=b836f4ec-3b77-4972-bccf-1234567890ab&limit=20&offset=0
```
**成功回應 (200 OK)**:
```json
[
  {
    "user_id": "99999999-aaaa-bbbb-cccc-ddddeeeeefff",
    "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
    "username": "john_doe",
    "email": "<EMAIL>",
    "role_id": "2222-3333-4444-5555-666677778888",
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-01T00:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

#### 11.1.2 GET /v1/users/{user_id}
**說明**: 根據使用者 ID 獲取詳細資訊。  
**SQL 語法**:
```SQL
SELECT user_id, tenant_id, username, email, role_id, created_at, updated_at
FROM users
WHERE user_id = :user_id;
```
**範例請求**:
```bash
GET /v1/users/99999999-aaaa-bbbb-cccc-ddddeeeeefff
```
**成功回應 (200 OK)**:
```json
{
  "user_id": "99999999-aaaa-bbbb-cccc-ddddeeeeefff",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "username": "john_doe",
  "email": "<EMAIL>",
  "role_id": "2222-3333-4444-5555-666677778888",
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`

#### 11.1.3 POST /v1/users
**說明**: 創建新的使用者。  
**SQL 語法**:
```SQL
INSERT INTO users (tenant_id, username, email, password_hash, role_id)
VALUES (:tenant_id, :username, :email, :password_hash, :role_id)
RETURNING user_id, tenant_id, username, email, role_id, created_at, updated_at;
```
**範例請求**:
```bash
POST /v1/users
Content-Type: application/json

{
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "username": "john_doe",
  "email": "<EMAIL>",
  "password_hash": "hashed_password_value",
  "role_id": "2222-3333-4444-5555-666677778888"
}
```
**成功回應 (201 Created)**:
```json
{
  "user_id": "99999999-aaaa-bbbb-cccc-ddddeeeeefff",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "username": "john_doe",
  "email": "<EMAIL>",
  "role_id": "2222-3333-4444-5555-666677778888",
  "created_at": "2025-01-10T00:00:00Z",
  "updated_at": "2025-01-10T00:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

#### 11.1.4 PUT /v1/users/{user_id}
**說明**: 更新特定使用者資訊（例如 email、角色、密碼等）。  
**SQL 語法**:
```SQL
UPDATE users
SET email = COALESCE(:email, email),
    role_id = COALESCE(:role_id, role_id),
    updated_at = CURRENT_TIMESTAMP
WHERE user_id = :user_id
RETURNING user_id, tenant_id, username, email, role_id, created_at, updated_at;
```
**範例請求**:
```bash
PUT /v1/users/99999999-aaaa-bbbb-cccc-ddddeeeeefff
Content-Type: application/json

{
  "email": "<EMAIL>",
  "role_id": "3333-4444-5555-6666-777788889999"
}
```
**成功回應 (200 OK)**:
```json
{
  "user_id": "99999999-aaaa-bbbb-cccc-ddddeeeeefff",
  "tenant_id": "b836f4ec-3b77-4972-bccf-1234567890ab",
  "username": "john_doe",
  "email": "<EMAIL>",
  "role_id": "3333-4444-5555-6666-777788889999",
  "created_at": "2025-01-10T00:00:00Z",
  "updated_at": "2025-01-12T09:00:00Z"
}
```
**錯誤回應**:
- `404 Not Found`
- `400 Bad Request`

#### 11.1.5 DELETE /v1/users/{user_id}
**說明**: 刪除或停用特定使用者。  
**SQL 語法**:
```SQL
DELETE FROM users
WHERE user_id = :user_id;
```
**範例請求**:
```bash
DELETE /v1/users/99999999-aaaa-bbbb-cccc-ddddeeeeefff
```
**成功回應**:
- `204 No Content`
**錯誤回應**:
- `404 Not Found`

---

### 11.2 角色管理 (roles)

#### 11.2.1 GET /v1/roles
**說明**: 取得所有角色。  
**SQL 語法**:
```SQL
SELECT role_id, name, description, created_at
FROM roles
ORDER BY created_at DESC;
```
**範例請求**:
```bash
GET /v1/roles
```
**成功回應 (200 OK)**:
```json
[
  {
    "role_id": "2222-3333-4444-5555-666677778888",
    "name": "tenant_admin",
    "description": "租戶管理員，可管理租戶內所有資源",
    "created_at": "2025-01-01T00:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`

#### 11.2.2 POST /v1/roles
**說明**: 創建新的角色。  
**SQL 語法**:
```SQL
INSERT INTO roles (name, description)
VALUES (:name, :description)
RETURNING role_id, name, description, created_at;
```
**範例請求**:
```bash
POST /v1/roles
Content-Type: application/json

{
  "name": "developer",
  "description": "API開發者，可管理API設定與金鑰等"
}
```
**成功回應 (201 Created)**:
```json
{
  "role_id": "9999-aaaa-bbbb-cccc-ddddeeeeefff",
  "name": "developer",
  "description": "API開發者，可管理API設定與金鑰等",
  "created_at": "2025-01-15T00:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

---

### 11.3 權限管理 (permissions)

#### 11.3.1 GET /v1/permissions?role_id={role_id}
**說明**: 根據 `role_id` 取得該角色的所有權限列表。  
**SQL 語法**:
```SQL
SELECT permission_id, role_id, resource, action, created_at
FROM permissions
WHERE role_id = :role_id;
```
**範例請求**:
```bash
GET /v1/permissions?role_id=2222-3333-4444-5555-666677778888
```
**成功回應 (200 OK)**:
```json
[
  {
    "permission_id": "aabbccdd-eeff-1122-3344-5566778899aa",
    "role_id": "2222-3333-4444-5555-666677778888",
    "resource": "api",
    "action": "read",
    "created_at": "2025-01-02T00:00:00Z"
  },
  {
    "permission_id": "bbbcccdd-eeff-1122-3344-5566778899bb",
    "role_id": "2222-3333-4444-5555-666677778888",
    "resource": "api",
    "action": "write",
    "created_at": "2025-01-02T00:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`

*※ 若需要動態新增、更新或移除權限，建議額外提供 POST、PUT、DELETE 端點。*

---

## 12. 通知管道設定 (notification_channels)

### 12.1 GET /v1/notification_channels
**說明**: 取得所有通知管道設定，支持狀態過濾與分頁。  
**SQL 語法**:
```SQL
SELECT channel_id, type, config, is_active, created_at, updated_at
FROM notification_channels
ORDER BY created_at DESC
LIMIT :limit OFFSET :offset;
```
**範例請求**:
```bash
GET /v1/notification_channels?limit=20&offset=0
```
**成功回應 (200 OK)**:
```json
[
  {
    "channel_id": "aaaabbbb-cccc-dddd-eeee-ffffffffffff",
    "type": "email",
    "config": { "smtp_host": "smtp.example.com", "port": 587 },
    "is_active": true,
    "created_at": "2025-01-10T00:00:00Z",
    "updated_at": "2025-01-10T00:00:00Z"
  }
]
```
**錯誤回應**:
- `401 Unauthorized`
- `403 Forbidden`

---

### 12.2 POST /v1/notification_channels
**說明**: 創建新的通知管道設定。  
**SQL 語法**:
```SQL
INSERT INTO notification_channels (type, config, is_active)
VALUES (:type, :config, :is_active)
RETURNING channel_id, type, config, is_active, created_at, updated_at;
```
**範例請求**:
```bash
POST /v1/notification_channels
Content-Type: application/json

{
  "type": "email",
  "config": { "smtp_host": "smtp.example.com", "port": 587 },
  "is_active": true
}
```
**成功回應 (201 Created)**:
```json
{
  "channel_id": "aaaabbbb-cccc-dddd-eeee-ffffffffffff",
  "type": "email",
  "config": { "smtp_host": "smtp.example.com", "port": 587 },
  "is_active": true,
  "created_at": "2025-01-10T00:00:00Z",
  "updated_at": "2025-01-10T00:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`

---

### 12.3 PUT /v1/notification_channels/{channel_id}
**說明**: 更新特定通知管道設定（例如修改配置、啟用狀態）。  
**SQL 語法**:
```SQL
UPDATE notification_channels
SET config = :config,
    is_active = :is_active,
    updated_at = CURRENT_TIMESTAMP
WHERE channel_id = :channel_id
RETURNING channel_id, type, config, is_active, created_at, updated_at;
```
**範例請求**:
```bash
PUT /v1/notification_channels/aaaabbbb-cccc-dddd-eeee-ffffffffffff
Content-Type: application/json

{
  "config": { "smtp_host": "smtp.newexample.com", "port": 465 },
  "is_active": false
}
```
**成功回應 (200 OK)**:
```json
{
  "channel_id": "aaaabbbb-cccc-dddd-eeee-ffffffffffff",
  "type": "email",
  "config": { "smtp_host": "smtp.newexample.com", "port": 465 },
  "is_active": false,
  "created_at": "2025-01-10T00:00:00Z",
  "updated_at": "2025-01-12T08:00:00Z"
}
```
**錯誤回應**:
- `400 Bad Request`
- `404 Not Found`

---

### 12.4 DELETE /v1/notification_channels/{channel_id}
**說明**: 刪除特定通知管道設定。  
**SQL 語法**:
```SQL
DELETE FROM notification_channels
WHERE channel_id = :channel_id;
```
**範例請求**:
```bash
DELETE /v1/notification_channels/aaaabbbb-cccc-dddd-eeee-ffffffffffff
```
**成功回應**:
- `204 No Content`
**錯誤回應**:
- `404 Not Found`

---

## 13. 健康檢查

### 13.1 GET /v1/health
**說明**: 檢查系統整體健康狀態，返回各子系統（例如資料庫、Kong、計費、監控）的狀態。  
**SQL 語法**: （此端點通常不需要直接連接資料庫，而是聚合各服務狀態，以下為示例伺服器端邏輯）
```SQL
-- 無固定SQL，此端點透過各服務狀態 API 聚合結果返回
```
**範例請求**:
```bash
GET /v1/health
```
**成功回應 (200 OK)**:
```json
{
  "status": "healthy",
  "components": {
    "database": "ok",
    "kong": "ok",
    "billing": "ok",
    "monitoring": "ok"
  },
  "timestamp": "2025-01-20T12:00:00Z"
}
```
**錯誤回應**:
- `503 Service Unavailable`

---

# 綜合補充說明

1. **API 版本管理**：所有 API 路徑均以 `/v1/` 開頭，未來版本更新可新增 `/v2/`，以保持向下兼容。  
2. **統一錯誤回應格式**：所有錯誤回應均應以統一格式返回（包含 error_code、message 與 details）。  
3. **安全性措施**：所有端點均需實施認證（例如 JWT）、授權檢查，並加強 CSRF、XSS 保護；敏感資料如密碼與 client_secret 應使用加密存儲。  
4. **分頁與排序**：對於返回多筆資料的端點，建議支援分頁（limit/offset 或 cursor-based）及排序參數。  
5. **監控與日誌**：系統透過 Prometheus、ELK 與分散式追蹤（例如 Jaeger/Zipkin）實現全面監控與故障排查。